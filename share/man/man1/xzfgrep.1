.\"
.\" Original zgrep.1 for gzip: <PERSON><PERSON><PERSON><PERSON>
.\"                            <PERSON> <<EMAIL>>
.\"
.\" Modifications for XZ Utils: Lasse Collin
.\"
.\" License: GNU GPLv2+
.\"
.TH XZGREP 1 "2022-07-19" "Tukaani" "XZ Utils"
.SH NAME
xzgrep \- search compressed files for a regular expression
.SH SYNOPSIS
.B xzgrep
.RI [ grep_options ]
.RB [ \-e ]
.I pattern
.RI [ file... ]
.br
.B xzegrep
\&...
.br
.B xzfgrep
\&...
.br
.B lzgrep
\&...
.br
.B lzegrep
\&...
.br
.B lzfgrep
\&...
.SH DESCRIPTION
.B xzgrep
invokes
.BR grep (1)
on
.I files
which may be either uncompressed or compressed with
.BR xz (1),
.BR lzma (1),
.BR gzip (1),
.BR bzip2 (1),
.<PERSON> lzop (1),
or
.BR zstd (1).
All options specified are passed directly to
.BR grep (1).
.PP
If no
.I file
is specified, then standard input is decompressed if necessary
and fed to
.BR grep (1).
When reading from standard input,
.BR gzip (1),
.BR bzip2 (1),
.BR lzop (1),
and
.BR zstd (1)
compressed files are not supported.
.PP
If
.B xzgrep
is invoked as
.B xzegrep
or
.B xzfgrep
then
.B grep \-E
or
.B grep \-F
is used instead of
.BR grep (1).
The same applies to names
.BR lzgrep ,
.BR lzegrep ,
and
.BR lzfgrep ,
which are provided for backward compatibility with LZMA Utils.
.SH EXIT STATUS
.TP
0
At least one match was found from at least one of the input files.
No errors occurred.
.TP
1
No matches were found from any of the input files.
No errors occurred.
.TP
>1
One or more errors occurred.
It is unknown if matches were found.
.SH ENVIRONMENT
.TP
.B GREP
If the
.B GREP
environment variable is set,
.B xzgrep
uses it instead of
.BR grep (1),
.BR "grep \-E" ,
or
.BR "grep \-F" .
.SH "SEE ALSO"
.BR grep (1),
.BR xz (1),
.BR gzip (1),
.BR bzip2 (1),
.BR lzop (1),
.BR zstd (1),
.BR zgrep (1)
