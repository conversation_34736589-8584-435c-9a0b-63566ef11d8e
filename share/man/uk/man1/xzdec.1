.\"
.\" Author: <PERSON><PERSON>
.\"
.\" This file has been put into the public domain.
.\" You can do whatever you want with this file.
.\"
.\"*******************************************************************
.\"
.\" This file was generated with po4a. Translate the source file.
.\"
.\"*******************************************************************
.TH XZDEC 1 "19 квітня 2017 року" Tukaani "XZ Utils"
.SH НАЗВА
xzdec, lzmadec — невеличкі розпакувальники .xz і .lzma
.SH "КОРОТКИЙ ОПИС"
\fBxzdec\fP [\fIпараметр...\fP] [\fIфайл...\fP]
.br
\fBlzmadec\fP [\fIпараметр...\fP] [\fIфайл...\fP]
.SH ОПИС
\fBxzdec\fP є інструментом на основі liblzma, який призначено лише для
розпаковування файлів \fB.xz\fP (і лише файлів \fB.xz\fP). \fBxzdec\fP призначено для
того, щоб працювати як повноцінний замінник \fBxz\fP(1) у більшості типових
ситуацій, де скрипт було написано для використання \fBxz \-\-decompress \-\-stdout\fP (і, можливо, декількох інших типових параметрів), для
розпаковування файлів \fB.xz\fP. \fBlzmadec\fP є тотожним до \fBxzdec\fP, але у
\fBlzmadec\fP передбачено підтримку файлів \fB.lzma\fP, замість файлів \fB.xz\fP.
.PP
Щоб зменшити розмір виконуваного файла, у \fBxzdec\fP не передбачено підтримки
багатопотокової обробки та локалізації, а також читання параметрів зі
змінних середовища \fBXZ_DEFAULTS\fP і \fBXZ_OPT\fP. У \fBxzdec\fP не передбачено
підтримки показу проміжних даних щодо поступу: надсилання \fBSIGINFO\fP до
\fBxzdec\fP не призводить ні до яких наслідків, але надсилання \fBSIGUSR1\fP
перериває процес, замість показу даних щодо поступу.
.SH ПАРАМЕТРИ
.TP 
\fB\-d\fP, \fB\-\-decompress\fP, \fB\-\-uncompress\fP
Буде проігноровано для сумісності з \fBxz\fP(1). У \fBxzdec\fP передбачено
підтримку лише розпаковування.
.TP 
\fB\-k\fP, \fB\-\-keep\fP
Буде проігноровано. Призначено для сумісності з \fBxz\fP(1). \fBxzdec\fP ніколи не
створюватиме і ніколи не вилучатиме ці файли.
.TP 
\fB\-c\fP, \fB\-\-stdout\fP, \fB\-\-to\-stdout\fP
Буде проігноровано. Для сумісності з \fBxz\fP(1). \fBxzdec\fP завжди записує
розпаковані дані до стандартного виведення.
.TP 
\fB\-q\fP, \fB\-\-quiet\fP
Якщо цей параметр вказано один раз, нічого не станеться, оскільки \fBxzdec\fP
ніколи не показуватиме жодних попереджень або нотаток. Вкажіть параметр
двічі, щоб придушити повідомлення про помилки.
.TP 
\fB\-Q\fP, \fB\-\-no\-warn\fP
Буде проігноровано для сумісності із \fBxz\fP(1). \fBxzdec\fP ніколи не
використовує стан виходу 2.
.TP 
\fB\-h\fP, \fB\-\-help\fP
Вивести довідкове повідомлення і успішно завершити роботу.
.TP 
\fB\-V\fP, \fB\-\-version\fP
Вивести номер версії \fBxzdec\fP та liblzma.
.SH "СТАН ВИХОДУ"
.TP 
\fB0\fP
Усе добре.
.TP 
\fB1\fP
Сталася помилка.
.PP
\fBxzdec\fP не має жодних повідомлень із попередженнями, на відміну від
\fBxz\fP(1), тому у \fBxzdec\fP стан виходу 2 не використовується.
.SH ПРИМІТКИ
Користуйтеся \fBxz\fP(1), замість \fBxzdec\fP або \fBlzmadec\fP, для щоденних
потреб. \fBxzdec\fP та \fBlzmadec\fP призначено лише для тих ситуацій, коли
важливо мати меншу програму для розпаковування, ніж \fBxz\fP(1).
.PP
\fBxzdec\fP і \fBlzmadec\fP не такі вже і малі програми. Їхній розмір можна
зменшити викиданням можливостей з liblzma під час збирання, але цього
зазвичай не роблять для виконуваних файлів, які поширюються у типових, не
вбудованих, дистрибутивах операційних систем. Якщо вам потрібний дуже мала
програма для розпаковування \fB.xz\fP, варто скористатися XZ Embedded.
.SH "ДИВ. ТАКОЖ"
\fBxz\fP(1)
.PP
Вбудовуваний XZ: <https://tukaani.org/xz/embedded.html>
