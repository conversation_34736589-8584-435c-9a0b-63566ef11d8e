'\" t
.\"
.\" Authors: <AUTHORS>
.\"          <PERSON><PERSON>\"
.\" This file has been put into the public domain.
.\" You can do whatever you want with this file.
.\"
.\"*******************************************************************
.\"
.\" This file was generated with po4a. Translate the source file.
.\"
.\"*******************************************************************
.TH XZ 1 "17 липня 2023 року" Tukaani "XZ Utils"
.
.SH НАЗВА
xz, unxz, xzcat, lzma, unlzma, lzcat — стискання та розпаковування файлів
\&.xz і .lzma
.
.SH "КОРОТКИЙ ОПИС"
\fBxz\fP [\fIпараметр...\fP] [\fIфайл...\fP]
.
.SH "СКОРОЧЕННЯ КОМАНД"
\fBunxz\fP є рівноцінним до \fBxz \-\-decompress\fP.
.br
\fBxzcat\fP є рівноцінним до \fBxz \-\-decompress \-\-stdout\fP.
.br
\fBlzma\fP є рівноцінним до \fBxz \-\-format=lzma\fP.
.br
\fBunlzma\fP є рівноцінним до \fBxz \-\-format=lzma \-\-decompress\fP.
.br
\fBlzcat\fP є рівноцінним до \fBxz \-\-format=lzma \-\-decompress \-\-stdout\fP.
.PP
При написанні скриптів, де потрібно розпаковувати файли, рекомендуємо завжди
використовувати \fBxz\fP із відповідними аргументами (\fBxz \-d\fP або \fBxz \-dc\fP),
замість \fBunxz\fP і \fBxzcat\fP.
.
.SH ОПИС
\fBxz\fP інструмент загального призначення для стискання даних із синтаксисом
командного рядка, подібним для \fBgzip\fP(1) і \fBbzip2\fP(1). Власним форматом
файлів є \fB.xz\fP, але передбачено підтримку застарілого формату \fB.lzma\fP,
який було використано у LZMA Utils, та необроблених потоків стиснених даних
без заголовків формату контейнера. Крім того, передбачено підтримку
розпаковування формату \fB.lz\fP, який використано у \fBlzip\fP.
.PP
\fBxz\fP стискає або розпаковує кожен \fIфайл\fP відповідно до вибраного режиму
дій. Якщо \fIфайли\fP не задано або якщо \fIфайлом\fP є \fB\-\fP, \fBxz\fP читатиме дані
зі стандартного джерела вхідних даних і записуватиме оброблені дані до
стандартного виведення. \fBxz\fP відмовить (покаже повідомлення про помилку і
пропустить \fIфайл\fP) у записів стиснених даних до стандартного виведення,
якщо це термінал. Так само, \fBxz\fP відмовить у читанні стиснених даних зі
стандартного джерела вхідних даних, якщо це термінал.
.PP
Якщо не вказано \fB\-\-stdout\fP, \fIфайли\fP, відмінні від \fB\-\fP, буде записано до
нового файла, чию назву буде визначено з назви початкового \fIфайла\fP:
.IP \(bu 3
При стисканні суфікс формату файла призначення (\fB.xz\fP або \fB.lzma\fP) буде
дописано до назви початкового файла для отримання назви файла призначення.
.IP \(bu 3
При розпаковуванні суфікс \fB.xz\fP, \fB.lzma\fP або \fB.lz\fP буде вилучено з назви
файла для отримання назви файла призначення. Крім того, \fBxz\fP розпізнає
суфікси \fB.txz\fP і \fB.tlz\fP і замінює їх на суфікс \fB.tar\fP.
.PP
Якщо файл призначення вже існує, буде показано повідомлення про помилку, а
\fIфайл\fP буде пропущено.
.PP
Окрім випадку запису до стандартного виведення, \fBxz\fP покаже попередження і
пропустить обробку \fIфайла\fP, якщо буде виконано будь\-яку з таких умов:
.IP \(bu 3
\fIФайл\fP не є звичайним файлом. Програма не переходитиме за символічними
посиланнями, а отже, не вважатиме їх звичайними файлами.
.IP \(bu 3
На \fIфайл\fP існує декілька жорстких посилань.
.IP \(bu 3
Для \fIфайла\fP встановлено setuid, setgid або «липкий» біт.
.IP \(bu 3
Режим дій встановлено у значення «стискання», і \fIфайл\fP вже має суфікс назви
формату файла призначення (\fB.xz\fP або \fB.txz\fP при стисканні до формату
\&\fB.xz\fP, і \fB.lzma\fP або \fB.tlz\fP при стисканні до формату \fB.lzma\fP).
.IP \(bu 3
Режим дій встановлено у значення «розпаковування», і \fIфайл\fP не має суфікса
назви жодного з підтримуваних форматів (\fB.xz\fP, \fB.txz\fP, \fB.lzma\fP, \fB.tlz\fP
або \fB.lz\fP).
.PP
Після успішного стискання або розпаковування \fIфайла\fP, \fBxz\fP копіює дані
щодо власника, групи, прав доступу, часу доступу та моменту внесення змін з
початкового \fIфайла\fP до файла призначення. Якщо копіювання даних щодо групи
зазнає невдачі, права доступу буде змінено так, що файл призначення стане
недоступним для користувачів, які не мають права доступу до початкового
\fIфайла\fP. В \fBxz\fP ще не передбачено підтримки копіювання інших метаданих,
зокрема списків керування доступом або розширених атрибутів.
.PP
Щойно файл призначення буде успішно закрито, початковий \fIфайл\fP буде
вилучено, якщо не вказано параметра \fB\-\-keep\fP. Початковий \fIфайл\fP ніколи не
буде вилучено, якщо виведені дані буде записано до стандартного виведення
або якщо станеться помилка.
.PP
Надсилання \fBSIGINFO\fP або \fBSIGUSR1\fP до процесу \fBxz\fP призводить до
виведення даних щодо поступу до стандартного виведення помилок. Це має лише
обмежене використання, оскільки якщо стандартним виведенням помилок є
термінал, використання \fB\-\-verbose\fP призведе до показу автоматично
оновлюваного індикатора поступу.
.
.SS "Використання пам'яті"
Використання \fBxz\fP пам'яті може бути різним: від декількох сотень кілобайтів
до декількох гігабайтів, залежно від параметрів стискання. Параметри, які
використано при стисканні файла, визначають вимоги до об'єму пам'яті при
розпакуванні. Типово, засобу розпаковування потрібно від 5\ % до 20\ %
об'єму пам'яті, якого засіб стискання потребує при створенні
файла. Наприклад, розпаковування файла, який створено з використанням \fBxz \-9\fP, у поточній версії потребує 65\ МіБ пам'яті. Втім, можливе створення
файлів \fB.xz\fP, які потребуватимуть для розпаковування декількох гігабайтів
пам'яті.
.PP
Ймовірність високого рівня використання пам'яті може бути особливо
дошкульною для користувачів застарілих комп'ютерів. Щоб запобігти прикрим
несподіванкам, у \fBxz\fP передбачено вбудований обмежувач пам'яті, який типово
вимкнено. Хоча у деяких операційних системах передбачено спосіб обмежити
використання пам'яті процесами, сподівання на його ефективність не є аж
надто гнучким (наприклад, використання \fBulimit\fP(1) для обмеження
віртуальної пам'яті призводить до викривлення даних \fBmmap\fP(2)).
.PP
Обмежувач пам'яті можна увімкнути за допомогою параметра командного рядка
\fB\-\-memlimit=\fP\fIобмеження\fP. Часто, зручніше увімкнути обмежувач на типовому
рівні, встановивши значення для змінної середовища \fBXZ_DEFAULTS\fP,
наприклад, \fBXZ_DEFAULTS=\-\-memlimit=150MiB\fP. Можна встановити обмеження
окремо для стискання і розпакування за допомогою
\fB\-\-memlimit\-compress=\fP\fIlimit\fP and
\fB\-\-memlimit\-decompress=\fP\fIобмеження\fP. Використання цих двох параметрів поза
\fBXZ_DEFAULTS\fP не таке вже і корисне, оскільки одноразовий запуск \fBxz\fP не
може одночасно призводити до стискання та розпаковування, а набрати у
командному рядку \fB\-\-memlimit=\fP\fIобмеження\fP (або \fB\-M\fP \fIобмеження\fP)
набагато швидше.
.PP
Якщо під час розпаковування вказане обмеження буде перевищено, \fBxz\fP покаже
повідомлення про помилку, а розпаковування файла зазнає невдачі. Якщо
обмеження буде перевищено при стисканні, \fBxz\fP спробує масштабувати
параметри так, щоб не перевищувати обмеження (окрім випадків використання
\fB\-\-format=raw\fP або \fB\-\-no\-adjust\fP). Отже, дію буде виконано, якщо обмеження
не є надто жорстким. Масштабування параметрів буде виконано кроками, які не
збігаються із рівнями шаблонів стискання. Наприклад, якщо обмеження лише
трохи не вкладається у об'єм потрібний для \fBxz \-9\fP, параметри буде змінено
лише трохи, не до рівня \fBxz \-8\fP.
.
.SS "Поєднання і заповнення з файлами .xz"
Можна поєднати файли \fB.xz\fP без додаткової обробки. \fBxz\fP розпакує такі
файли так, наче вони є єдиним файлом \fB.xz\fP.
.PP
Можна додати доповнення між з'єднаними частинами або після останньої
частини. Доповнення має складатися із нульових байтів і мати розмір, який є
кратним до чотирьох байтів. Це може бути корисним, наприклад, якщо файл
\&\fB.xz\fP зберігається на носії даних, де розміри файла вимірюються у
512\-байтових блоках.
.PP
Поєднання та заповнення не можна використовувати для файлів \fB.lzma\fP або
потоків необроблених даних.
.
.SH ПАРАМЕТРИ
.
.SS "Цілочисельні суфікси і спеціальні значення"
У більшості місць, де потрібен цілочисельний аргумент, передбачено підтримку
необов'язкового суфікса для простого визначення великих цілих чисел. Між
цілим числом і суфіксом не повинно бути пробілів.
.TP 
\fBKiB\fP
Помножити ціле число на 1024 (2^10). Синонімами \fBKiB\fP є \fBKi\fP, \fBk\fP, \fBkB\fP,
\fBK\fP та \fBKB\fP.
.TP 
\fBMiB\fP
Помножити ціле число на 1048576 (2^20). Синонімами \fBMiB\fP є B, \fBMi\fP, \fBm\fP,
\fBM\fP та \fBMB\fP.
.TP 
\fBGiB\fP
Помножити ціле число на 1073741824 (2^30). Синонімами \fBGiB\fP є B, \fBGi\fP,
\fBg\fP, \fBG\fP та \fBGB\fP.
.PP
Можна скористатися особливим значенням \fBmax\fP для позначення максимального
цілого значення, підтримку якого передбачено для параметра.
.
.SS "Режим операції"
Якщо вказано декілька параметрів режиму дій, буде використано лише останній
з них.
.TP 
\fB\-z\fP, \fB\-\-compress\fP
Стиснути. Це типовий режим дій, якщо не вказано параметр режиму дій, а назва
команди неявним чином не визначає іншого режиму дій (наприклад, \fBunxz\fP
неявно визначає \fB\-\-decompress\fP).
.TP 
\fB\-d\fP, \fB\-\-decompress\fP, \fB\-\-uncompress\fP
Розпакувати.
.TP 
\fB\-t\fP, \fB\-\-test\fP
Перевірити цілісність стиснених файлів \fIфайли\fP. Цей параметр еквівалентний
до \fB\-\-decompress \-\-stdout\fP, але розпаковані дані буде відкинуто, замість
запису до стандартного виведення. Жодних файлів не буде створено або
вилучено.
.TP 
\fB\-l\fP, \fB\-\-list\fP
Вивести відомості щодо стиснених файлів \fIфайли\fP. Розпакування даних не
виконуватиметься, жодних файлів не буде створено або вилучено. У режимі
списку програма не може читати дані зі стандартного введення або з інших
джерел, де неможливе позиціювання.
.IP ""
У типовому списку буде показано базові відомості щодо файлів \fIфайли\fP, по
одному файлу на рядок. Щоб отримати докладніші відомості, скористайтеся
параметром \fB\-\-verbose\fP. Щоб розширити спектр відомостей, скористайтеся
параметром \fB\-\-verbose\fP двічі, але зауважте, що це може призвести до
значного уповільнення роботи, оскільки отримання додаткових відомостей
потребує великої кількості позиціювань. Ширина області докладного виведення
даних перевищує 80 символів, тому передавання конвеєром виведених даних,
наприклад, до \fBless\ \-S\fP, може бути зручним способом перегляду даних, якщо
термінал недостатньо широкий.
.IP ""
Виведені дані залежать від версії \fBxz\fP та використаної локалі. Для
отримання даних, які будуть придатні до обробки комп'ютером, слід
скористатися параметрами \fB\-\-robot \-\-list\fP.
.
.SS "Модифікатори режиму роботи"
.TP 
\fB\-k\fP, \fB\-\-keep\fP
Не вилучати вхідні файли.
.IP ""
Починаючи з версії \fBxz\fP 5.2.6, використання цього параметра також наказує
\fBxz\fP виконувати стискання або розпаковування, навіть якщо вхідними даними є
символічне посилання на звичайний файл, файл, який має декілька жорстких
посилань, або файл, для якого встановлено  setuid, setgid або липкий
біт. setuid, setgid та липкий біт не буде скопійовано до файла\-результату. У
попередніх версіях, ці дії виконувалися, лише якщо було використано параметр
\fB\-\-force\fP.
.TP 
\fB\-f\fP, \fB\-\-force\fP
Результатів використання цього параметра буде декілька:
.RS
.IP \(bu 3
Якщо файл\-результат вже існує, вилучити його до стискання або
розпаковування.
.IP \(bu 3
Виконувати стискання або розпаковування, навіть якщо вхідними даними є
символічне посилання на звичайний файл, файл, який має декілька жорстких
посилань, або файл, для якого встановлено  setuid, setgid або липкий біт
setuid, setgid та липкий біт не буде скопійовано до файла\-результату.
.IP \(bu 3
Якщо використано разом із \fB\-\-decompress\fP, \fB\-\-stdout\fP, і \fBxz\fP не зможе
розпізнати тип початкового файла, копіювати початковий файл без змін до
стандартного виведення. Це надає змогу користуватися \fBxzcat\fP \fB\-\-force\fP
подібно до \fBcat\fP(1) для файлів, які не було стиснено за допомогою
\fBxz\fP. Зауважте, що у майбутньому у \fBxz\fP може бути реалізовано підтримку
нових форматів стиснених файлів, замість копіювання їх без змін до
стандартного виведення. Можна скористатися \fB\-\-format=\fP\fIформат\fP для
обмеження стискання у \fBxz\fP єдиним форматом файлів.
.RE
.TP 
\fB\-c\fP, \fB\-\-stdout\fP, \fB\-\-to\-stdout\fP
Записати стиснені або розпаковані дані до стандартного виведення, а не до
файла. Неявним чином встановлює \fB\-\-keep\fP.
.TP 
\fB\-\-single\-stream\fP
Розпакувати лише перший потік даних \fB.xz\fP і без повідомлень проігнорувати
решту вхідних даних, які слідують за цим потоком. Зазвичай, такі зайві дані
наприкінці файла призводять до показу \fBxz\fP повідомлення про помилку.
.IP ""
\fBxz\fP ніколи не виконуватиме спроби видобути декілька потоків даних з файлів
\&\fB.lzma\fP або необроблених потоків даних, але використання цього параметра
все одно наказує \fBxz\fP ігнорувати можливі кінцеві дані після файла \fB.lzma\fP
або необробленого потоку даних.
.IP ""
Цей параметр нічого не змінює, якщо режимом дій не є \fB\-\-decompress\fP або
\fB\-\-test\fP.
.TP 
\fB\-\-no\-sparse\fP
Вимкнути створення розріджених файлів. Типово, якщо видобування виконується
до звичайного файла, \fBxz\fP намагається створити розріджений файл, якщо
розпаковані дані містять довгі послідовності двійкових нулів. Це також
працює, коли виконується запис до стандартного виведення, доки стандартне
виведення з'єднано зі звичайним файлом і виконуються певні додаткові умови,
які убезпечують роботу. Створення розріджених файлів може заощадити місце на
диску і пришвидшити розпаковування шляхом зменшення кількості дій введення
та виведення даних на диску.
.TP 
\fB\-S\fP \fI.suf\fP, \fB\-\-suffix=\fP\fI.suf\fP
При стисканні використати суфікс \fI.suf\fP для файлів призначення, замість
суфікса \fB.xz\fP або \fB.lzma\fP. Якщо записування виконується не до стандартного
виведення і початковий файл вже має суфікс назви \fI.suf\fP, буде показано
попередження, а файл буде пропущено під час обробки.
.IP ""
При розпаковуванні розпізнавати файли із суфіксом назви \fI.suf\fP, окрім
файлів із суфіксами назв \fB.xz\fP, \fB.txz\fP, \fB.lzma\fP, \fB.tlz\fP або \fB.lz\fP. Якщо
початковий файл мав суфікс назви \fI.suf\fP, для отримання назви файла
призначення цей суфікс буде вилучено.
.IP ""
При стисканні або розпакуванні необроблених потоків даних (\fB\-\-format=raw\fP)
суфікс слід вказувати завжди, якщо запис не виконується до стандартного
виведення, оскільки типового суфікса назви для необроблених потоків даних не
передбачено.
.TP 
\fB\-\-files\fP[\fB=\fP\fIфайл\fP]
Прочитати назви файлів для обробки з файла \fIфайл\fP; якщо \fIfile\fP не вказано,
назви файлів буде прочитано зі стандартного потоку вхідних даних. Назви
файлів має бути відокремлено символом нового рядка. Символ дефіса (\fB\-\fP)
буде оброблено як звичайну назву файла; він не позначатиме стандартного
джерела вхідних даних. Якщо також буде вказано назви файлів у аргументах
рядка команди, файли з цими назвами буде оброблено до обробки файлів, назви
яких було прочитано з файла \fIфайл\fP.
.TP 
\fB\-\-files0\fP[\fB=\fP\fIфайл\fP]
Те саме, що і \fB\-\-files\fP[\fB=\fP\fIфайл\fP], але файли у списку має бути
відокремлено нульовим символом.
.
.SS "Параметри базового формату файлів та стискання"
.TP 
\fB\-F\fP \fIformat\fP, \fB\-\-format=\fP\fIформат\fP
Вказати файл \fIформат\fP для стискання або розпакування:
.RS
.TP 
\fBauto\fP
Типовий варіант. При стисканні \fBauto\fP є еквівалентом \fBxz\fP. При
розпакуванні формат файла вхідних даних буде виявлено автоматично. Зауважте,
що автоматичне виявлення необроблених потоків даних (створених за допомогою
\fB\-\-format=raw\fP) неможливе.
.TP 
\fBxz\fP
Стиснути до формату \fB.xz\fP або приймати лише файли \fB.xz\fP при
розпаковуванні.
.TP 
\fBlzma\fP, \fBalone\fP
Стиснути дані до застарілого формату файлів \fB.lzma\fP або приймати лише файли
\&\fB.lzma\fP при розпаковуванні. Альтернативну назву \fBalone\fP може бути
використано для зворотної сумісності із LZMA Utils.
.TP 
\fBlzip\fP
Приймати лише файли \fB.lz\fP при розпакуванні. Підтримки стискання не
передбачено.
.IP ""
Передбачено підтримку версії формату \fB.lz\fP 0 та нерозширеної версії
1. Файли версії 0 було створено \fBlzip\fP 1.3 та старішими версіями. Такі
файли не є поширеними, але їх можна знайти у файлових архівах, оскільки
певну незначну кількість пакунків із початковим кодом було випущено у цьому
форматі. Також можуть існувати особисті файли у цьому форматі. Підтримку
розпаковування для формату версії 0 було вилучено у \fBlzip\fP 1.18.
.IP ""
\fBlzip\fP 1.4 і пізніші версії створюють файли у форматі версії 1. Розширення
синхронізації позначки витирання до формату версії 1 було додано у \fBlzip\fP
1.6. Це розширення використовують не часто, його підтримки у \fBxz\fP не
передбачено (програма повідомлятиме про пошкоджені вхідні дані).
.TP 
\fBraw\fP
Стиснути або розпакувати потік необроблених даних (лез заголовків). Цей
параметр призначено лише для досвідчених користувачів. Для розпаковування
необроблених потоків даних слід користуватися параметром  \fB\-\-format=raw\fP і
явно вказати ланцюжок фільтрування, який за звичайних умов мало б бути
збережено у заголовках контейнера.
.RE
.TP 
\fB\-C\fP \fIперевірка\fP, \fB\-\-check=\fP\fIперевірка\fP
Вказати тип перевірки цілісності. Контрольну суму буде обчислено на основі
нестиснених даних і збережено у файлі \fB.xz\fP. Цей параметр працюватиме, лише
якщо дані стиснено до файла у форматі \fB.xz\fP; для формату файлів \fB.lzma\fP
підтримки перевірки цілісності не передбачено. Перевірку контрольної суми
(якщо така є) буде виконано під час розпаковування файла \fB.xz\fP.
.IP ""
Підтримувані типи \fIперевірок\fP:
.RS
.TP 
\fBnone\fP
Не обчислювати контрольну суму взагалі. Зазвичай, не варто цього робити. Цим
варіантом слід скористатися, якщо цілісність даних буде перевірено в інший
спосіб.
.TP 
\fBcrc32\fP
Обчислити CRC32 за допомогою полінома з IEEE\-802.3 (Ethernet).
.TP 
\fBcrc64\fP
Обчислити CRC64 за допомогою полінома з ECMA\-182. Це типовий варіант,
оскільки він дещо кращий за CRC32 при виявленні пошкоджених файлів, а
різниця у швидкості є незрачною.
.TP 
\fBsha256\fP
Обчислити SHA\-256. Цей варіант дещо повільніший за CRC32 і CRC64.
.RE
.IP ""
Цілісність заголовків \fB.xz\fP завжди перевіряють за допомогою CRC32. Таку
перевірку не можна змінити або скасувати.
.TP 
\fB\-\-ignore\-check\fP
Не перевіряти цілісність стиснених даних при розпаковуванні. Значення CRC32
у заголовках \fB.xz\fP буде у звичайний спосіб перевірено попри цей параметр.
.IP ""
\fBНе користуйтеся цим параметром, якщо ви не усвідомлюєте наслідків ваших дій.\fP Можливі причини скористатися цим параметром:
.RS
.IP \(bu 3
Спроба отримання даних з пошкодженого файла .xz.
.IP \(bu 3
Пришвидшення розпакування. Це, здебільшого, стосується SHA\-256 або файлів із
надзвичайно високим рівнем пакування. Не рекомендуємо користуватися цим
параметром з цією метою, якщо цілісність файлів не буде перевірено у якийсь
інший спосіб.
.RE
.TP 
\fB\-0\fP ... \fB\-9\fP
Вибрати рівень стискання. Типовим є \fB\-6\fP. Якщо буде вказано декілька рівнів
стискання, програма використає останній вказаний. Якщо вже було вказано
нетиповий ланцюжок фільтрів, встановлення рівня стискання призведе до
нехтування цим нетиповим ланцюжком фільтрів.
.IP ""
Різниця між рівнями є суттєвішою, ніж у \fBgzip\fP(1) і \fBbzip2\fP(1). Вибрані
параметри стискання визначають вимоги до пам'яті під час розпаковування,
отже використання надто високого рівня стискання може призвести до проблем
під час розпаковування файла на застарілих комп'ютерах із невеликим обсягом
оперативної пам'яті. Зокрема, \fBне варто використовувати \-9 для усього\fP, як
це часто буває для \fBgzip\fP(1) і \fBbzip2\fP(1).
.RS
.TP 
\fB\-0\fP ... \fB\-3\fP
Це дещо швидші набори налаштувань. \fB\-0\fP іноді є швидшим за \fBgzip \-9\fP,
забезпечуючи набагато більший коефіцієнт стискання. Вищі рівні часто мають
швидкість, яку можна порівняти з \fBbzip2\fP(1) із подібним або кращим
коефіцієнтом стискання, хоча результати значно залежать від типу даних, які
стискають.
.TP 
\fB\-4\fP ... \fB\-6\fP
Стискання від доброго до дуже доброго рівня із одночасним підтриманням
помірного рівня споживання пам'яті засобом розпаковування, навіть для
застарілих системи. Типовим є значення \fB\-6\fP, яке є добрим варіантом для
поширення файлів, які мають бути придатними до розпаковування навіть у
системах із лише 16\ МіБ оперативної пам'яті. (Також можна розглянути
варіанти \fB\-5e\fP і \fB\-6e\fP. Див. \fB\-\-extreme\fP.)
.TP 
\fB\-7 ... \-9\fP
Ці варіанти подібні до \fB\-6\fP, але із вищими вимогами щодо пам'яті для
стискання і розпаковування. Можуть бути корисними лише для стискання файлів
з розміром, що перевищує 8\ МіБ, 16\ МіБ та 32\ МіБ, відповідно.
.RE
.IP ""
На однаковому обладнанні швидкість розпакування є приблизно сталою кількістю
байтів стиснених даних за секунду. Іншими словами, чим кращим є стискання,
тим швидшим буде, зазвичай, розпаковування. Це також означає, що об'єм
розпакованих виведених даних, які видає програма за секунду, може коливатися
у широкому діапазоні.
.IP ""
У наведеній нижче таблиці підсумовано можливості шаблонів:
.RS
.RS
.PP
.TS
tab(;);
c c c c c
n n n n n.
Шаблон;DictSize;CompCPU;CompMem;DecMem
\-0;256 КіБ;0;3 МіБ;1 МіБ
\-1;1 МіБ;1;9 МіБ;2 МіБ
\-2;2 МіБ;2;17 МіБ;3 МіБ
\-3;4 МіБ;3;32 МіБ;5 МіБ
\-4;4 МіБ;4;48 МіБ;5 МіБ
\-5;8 МіБ;5;94 МіБ;9 МіБ
\-6;8 МіБ;6;94 МіБ;9 МіБ
\-7;16 МіБ;6;186 МіБ;17 МіБ
\-8;32 МіБ;6;370 МіБ;33 МіБ
\-9;64 МіБ;6;674 МіБ;65 МіБ
.TE
.RE
.RE
.IP ""
Описи стовпчиків:
.RS
.IP \(bu 3
DictSize є розміром словника LZMA2. Використання словника, розмір якого
перевищує розмір нестисненого файла, — проста витрата пам'яті. Ось чому не
варто використовувати шаблони \fB\-7\fP ... \fB\-9\fP, якщо у них немає реальної
потреби. Для \fB\-6\fP та нижчих рівнів об'єм витраченої пам'яті, зазвичай,
такий низький, що цей фактор ні на що не впливає.
.IP \(bu 3
CompCPU є спрощеним представленням параметрів LZMA2, які впливають на
швидкість стискання. Розмір словника також впливає на швидкість, тому, хоча
значення CompCPU є однаковим для рівнів \fB\-6\fP ... \fB\-9\fP, обробка на вищих
рівнях все одно є трошки повільнішою. Що отримати повільніше і, ймовірно,
краще стискання, див. \fB\-\-extreme\fP.
.IP \(bu 3
CompMem містить вимоги до пам'яті засобу стискання у однопотоковому
режимі. Значення можуть бути дещо різними для різних версій \fBxz\fP. Вимоги до
пам'яті деяких майбутніх багатопотокових режимів можуть бути набагато
вищими, ніж вимоги у однопотоковому режимі.
.IP \(bu 3
У DecMem містяться вимоги до пам'яті при розпаковуванні. Тобто параметри
засобу стискання визначають вимоги до пам'яті при розпаковуванні. Точний
об'єм пам'яті, яка потрібна для розпаковування, дещо перевищує розмір
словника LZMA2, але значення у таблиці було округлено до наступного цілого
значення МіБ.
.RE
.TP 
\fB\-e\fP, \fB\-\-extreme\fP
Використати повільніший варіант вибраного рівня стискання (\fB\-0\fP ... \fB\-9\fP)
у сподіванні отримати трохи кращий коефіцієнт стискання, але, якщо не
поталанить, можна його і погіршити. Не впливає на використання пам'яті при
розпаковуванні, але використання пам'яті при стисканні дещо збільшиться на
рівнях \fB\-0\fP ... \fB\-3\fP.
.IP ""
Оскільки існує два набори налаштувань із розмірами словників 4\ МіБ та 8\ МіБ, у наборах \fB\-3e\fP і \fB\-5e\fP використано трошки швидші параметри (нижче
CompCPU), ніж у наборах \fB\-4e\fP і \fB\-6e\fP, відповідно. Тому двох однакових
наборів у списку немає.
.RS
.RS
.PP
.TS
tab(;);
c c c c c
n n n n n.
Шаблон;DictSize;CompCPU;CompMem;DecMem
\-0e;256 КіБ;8;4 МіБ;1 МіБ
\-1e;1 МіБ;8;13 МіБ;2 МіБ
\-2e;2 МіБ;8;25 МіБ;3 МіБ
\-3e;4 МіБ;7;48 МіБ;5 МіБ
\-4e;4 МіБ;8;48 МіБ;5 МіБ
\-5e;8 МіБ;7;94 МіБ;9 МіБ
\-6e;8 МіБ;8;94 МіБ;9 МіБ
\-7e;16 МіБ;8;186 МіБ;17 МіБ
\-8e;32 МіБ;8;370 МіБ;33 МіБ
\-9e;64 МіБ;8;674 МіБ;65 МіБ
.TE
.RE
.RE
.IP ""
Наприклад, передбачено загалом чотири набори налаштувань із використанням
словника у 8\ МіБ, порядок яких від найшвидшого до найповільнішого є таким:
\fB\-5\fP, \fB\-6\fP, \fB\-5e\fP і \fB\-6e\fP.
.TP 
\fB\-\-fast\fP
.PD 0
.TP 
\fB\-\-best\fP
.PD
Це дещо оманливі альтернативні варіанти для \fB\-0\fP і \fB\-9\fP,
відповідно. Реалізовано лише для забезпечення зворотної сумісності із LZMA
Utils. Намагайтеся не користуватися цими варіантами параметрів.
.TP 
\fB\-\-block\-size=\fP\fIрозмір\fP
При стисканні до формату \fB.xz\fP поділити вхідні дані на блоки у \fIрозмір\fP
байтів. Ці блоки буде стиснуто незалежно один від одного, що допоможе у
багатопотоковій обробці і зробить можливим обмежене розпакування для доступу
до будь\-яких даних. Цим параметром слід типово користуватися для
перевизначення типового розміру блоку у багатопотоковому режимі обробки, але
цим параметром можна також скористатися в однопотоковому режимі обробки.
.IP ""
У багатопотоковому режимі для кожного потоку буде отримано для буферів
вхідних і вихідних даних майже утричі більше за \fIрозмір\fP байтів. Типовий
\fIрозмір\fP утричі більший за розмір словника LZMA2 або дорівнює 1 МіБ, буде
вибрано більше значення. Типовим добрим значенням буде значення, яке у
2\(en4 рази перевищує розмір словника LZMA2 або дорівнює принаймні 1
МіБ. Використання значення \fIрозмір\fP, яке є меншим за розмір словника LZMA2,
має наслідком марну витрату оперативної пам'яті, оскільки його використання
призводить до того, що буфер словника LZMA2 ніколи не буде використано
повністю. Розміри блоків зберігатимуться у заголовках блоків, які майбутня
версія \fBxz\fP використовуватиме для багатопотокового розпаковування.
.IP ""
У однопотоковому режимі поділ на блоки типово не
виконуватиметься. Встановлення значення для цього параметра не впливатиме на
використання пам'яті. У заголовках блоків не зберігатимуться дані щодо
розміру, отже файли, які створено в однопотоковому режимі не будуть
ідентичними до файлів, які створено у багатопотоковому режимі. Те, що у
заголовках блоків не зберігатимуться дані щодо розміру також означає, що
майбутні версії \fBxz\fP не зможуть розпаковувати такі файли у багатопотоковому
режимі.
.TP 
\fB\-\-block\-list=\fP\fIрозміри\fP
При стисканні у форматі \fB.xz\fP починати новий блок після вказаної кількості
інтервалів нестиснених даних.
.IP ""
Значення \fIрозмірів\fP розпакованих блоків слід задавати у форматі списку
відокремлених комами значень. Якщо розмір пропущено (дві або декілька
послідовних коми), буде використано розмір попереднього блоку.
.IP ""
Якщо файл вхідних даних є більшим за розміром за суму \fIрозмірів\fP, останнє
значення у \fIрозмірах\fP буде повторено до кінця файла. Особливе значення \fB0\fP
може бути використано як останнє значення, щоб позначити, що решту файла має
бути закодовано як єдиний блок.
.IP ""
Якщо вказати \fIрозміри\fP, які перевищують розмір блоку кодувальника (або
типове значення у режимі із потоками обробки, або значення, яке встановлено
за допомогою \fB\-\-block\-size=\fP\fIрозмір\fP), засіб кодування створить додаткові
блоки, зберігаючи межі, які вказано у \fIрозмірах\fP. Наприклад, якщо вказати
\fB\-\-block\-size=10MiB\fP \fB\-\-block\-list=5MiB,10MiB,8MiB,12MiB,24MiB\fP, а файл
вхідних даних має розмір 80 МіБ, буде отримано такі 11 блоків: 5, 10, 8, 10,
2, 10, 10, 4, 10, 10 і 1 МіБ.
.IP ""
У багатопотоковому режимі розмір блоків буде збережено у заголовках
блоків. Програма не зберігатиме ці дані у однопотоковому режимі, отже
закодований результат не буде ідентичним до отриманого у багатопотоковому
режимі.
.TP 
\fB\-\-flush\-timeout=\fP\fIчас_очікування\fP
При стискання, якщо з моменту попереднього витирання мине понад
\fIчас_очікування\fP мілісекунд (додатне ціле значення) і читання додаткових
даних буде заблоковано, усі вхідні дані у черзі обробки буде витерто з
кодувальника і зроблено доступним у потоці вихідних даних. Це може бути
корисним, якщо \fBxz\fP використовують для стискання даних, які передають
потоком мережею. Невеликі значення аргументу \fIчас_очікування\fP зроблять дані
доступними на боці отримання із малою затримкою, а великі значення аргумент
\fIчас_очікування\fP уможливлять кращий коефіцієнт стискання.
.IP ""
Типово, цю можливість вимкнено. Якщо цей параметр вказано декілька разів,
буде використано лише останнє вказане значення. Особливим значенням
аргументу \fIчас_очікування\fP, рівним \fB0\fP, можна скористатися для вимикання
цієї можливості явним чином.
.IP ""
Ця можливість недоступна у системах, які не є системами POSIX.
.IP ""
.\" FIXME
\fBЦя можливість усе ще є експериментальною.\fP У поточній версії, \fBxz\fP не
може розпаковувати потік даних у режимі реального часу через те, у який
спосіб \fBxz\fP виконує буферизацію.
.TP 
\fB\-\-memlimit\-compress=\fP\fIобмеження\fP
Встановити обмеження на використання пам'яті при стисканні. Якщо цей
параметр вказано декілька разів, враховано буде лише останнє вказане
значення.
.IP ""
Якщо параметри стискання перевищують \fIобмеження\fP, \fBxz\fP спробує скоригувати
параметри так, щоб обмеження не було перевищено, і покаже повідомлення про
те, що було виконано автоматичне коригування. Коригування буде виконано у
такому порядку: зменшення кількості потоків обробки, перемикання у
однопотоковий режим, якщо хоч в одному потоці багатопотокового режиму буде
перевищено \fIобмеження\fP, і нарешті, зменшення розміру словника LZMA2.
.IP ""
При стисканні з використанням \fB\-\-format=raw\fP, або якщо було вказано
\fB\-\-no\-adjust\fP, може бути зменшена лише кількість потоків обробки, оскільки
це може бути зроблено без впливу на стиснені виведені дані.
.IP ""
Якщо \fIобмеження\fP не може бути виконано за допомогою коригувань, які описано
вище, буде показано повідомлення про помилку, а \fBxz\fP завершить роботу зі
станом виходу 1.
.IP ""
Аргумент \fIобмеження\fP можна вказати у декілька способів:
.RS
.IP \(bu 3
Значенням \fIобмеження\fP може бути додатне ціле значення у байтах. Можна
скористатися цілочисельним суфіксом, подібним до \fBMiB\fP. Приклад:
\fB\-\-memlimit\-compress=80MiB\fP
.IP \(bu 3
Аргумент \fIобмеження\fP може бути задано у відсотках від загальної фізичної
пам'яті системи (RAM). Це може бути корисним особливо при встановленні
змінної середовища \fBXZ_DEFAULTS\fP у скрипті ініціалізації системи, який є
спільним для різних комп'ютерів. У такий спосіб можна вказати вищий рівень
обмеження для систем із більшим об'ємом пам'яті. Приклад:
\fB\-\-memlimit\-compress=70%\fP
.IP \(bu 3
Аргументу \fIобмеження\fP може бути повернуто типове значення встановленням
значення \fB0\fP. У поточній версії це еквівалентно до встановлення значення
аргументу \fIобмеження\fP \fBmax\fP (без обмеження на використання пам'яті).
.RE
.IP ""
Для 32\-бітової версії \fBxz\fP передбачено особливий випадок: якщо \fIобмеження\fP
перевищуватиме \fB4020\ МіБ\fP, для \fIобмеження\fP буде встановлено значення
\fB4020\ MiB\fP. На MIPS32 замість цього буде використано \fB2000\ MiB\fP. (Це не
стосується значень \fB0\fP і \fBmax\fP. Подібної можливості для розпаковування не
існує.) Це може бути корисним, коли 32\-бітовий виконуваний файл має доступ
до простору адрес у 4\ ГіБ (2 GiB на MIPS32), хоча, сподіваємося, не
зашкодить і в інших випадках.
.IP ""
Див. також розділ \fBВикористання пам'яті\fP.
.TP 
\fB\-\-memlimit\-decompress=\fP\fIобмеження\fP
Встановити обмеження пам'яті на розпаковування. це також вплине на режим
\fB\-\-list\fP. Якщо дія є неможливою без перевищення \fIобмеження\fP, \fBxz\fP покаже
повідомлення про помилку і розпаковування файла не
відбудеться. Див. \fB\-\-memlimit\-compress=\fP\fIобмеження\fP, щоб дізнатися більше
про те, як можна задати \fIобмеження\fP.
.TP 
\fB\-\-memlimit\-mt\-decompress=\fP\fIобмеження\fP
Встановити обмеження використання пам'яті для багатопотокового
розпаковування. Це може вплинути лише на кількість потоків обробки; це
ніколи не призводитиме до відмови \fBxz\fP у розпаковуванні файла. Якщо
\fIобмеження є надто низьким\fP, щоб уможливити будь\-яку багатопотокову
обробку, \fIобмеження\fP буде проігноровано, і \fBxz\fP продовжить обробку в
однопотоковому режимі. Зауважте, що якщо використано також
\fB\-\-memlimit\-decompress\fP, цей параметр буде застосовано до обох режимів,
однопотокового та багатопотокового, а отже, задіяне \fIобмеження\fP для
багатопотокового режиму ніколи не перевищуватиме обмеження, яке встановлено
за допомогою \fB\-\-memlimit\-decompress\fP.
.IP ""
На відміну від інших параметрів обмеження використання пам'яті,
\fB\-\-memlimit\-mt\-decompress=\fP\fIобмеження\fP містить специфічне для системи
типове значення \fIобмеження\fP. Можна скористатися \fBxz \-\-info\-memory\fP для
перегляду поточного значення.
.IP ""
Цей параметр і його типове значення існують, оскільки без будь\-яких обмежень
засіб розпакування зі підтримкою потокової обробки міг би намагатися
отримати величезний об'єм пам'яті для деяких файлів вхідних даних. Якщо
типове \fIобмеження\fP є надто низьким для вашої системи, не вагайтеся і
збільшуйте \fIобмеження\fP, але ніколи не встановлюйте для нього значення, яке
є більшим за придатний до користування об'єм оперативної пам'яті, оскільки
за відповідних файлів вхідних даних \fBxz\fP спробує скористатися цим об'ємом
пам'яті, навіть із низькою кількістю потоків обробки. Вичерпання об'єму
оперативної пам'яті або використання резервної пам'яті на диску не покращить
швидкодію системи під час розпаковування.
.IP ""
Див. \fB\-\-memlimit\-compress=\fP\fIобмеження\fP, щоб ознайомитися із можливими
способами визначення \fIобмеження\fP. Встановлення для \fIобмеження\fP значення
\fB0\fP відновлює типове специфічне для системи значення \fIобмеження\fP.
.TP 
\fB\-M\fP \fIобмеження\fP, \fB\-\-memlimit=\fP\fIобмеження\fP, \fB\-\-memory=\fP\fIобмеження\fP
Є еквівалентом визначення \fB\-\-memlimit\-compress=\fP\fIобмеження\fP
\fB\-\-memlimit\-decompress=\fP\fIобмеження\fP
\fB\-\-memlimit\-mt\-decompress=\fP\fIобмеження\fP.
.TP 
\fB\-\-no\-adjust\fP
Показати повідомлення про помилку і завершити роботу, якщо не вдасться
виконати умови щодо обмеження використання пам'яті без коригування
параметрів, які впливають на стиснених виведених даних. Тобто це забороняє
\fBxz\fP перемикати кодувальник з багатопотокового режиму на однопотоковий
режим і зменшувати розмір словника LZMA2. Навіть якщо використано цей
параметр, кількість потоків може бути зменшено для виконання обмеження на
використання пам'яті, оскільки це не вплине на результати стискання.
.IP ""
Автоматичне коригування завжди буде вимкнено при створенні потоків
необроблених даних (\fB\-\-format=raw\fP).
.TP 
\fB\-T\fP \fIпотоки\fP, \fB\-\-threads=\fP\fIпотоки\fP
Вказати кількість потоків обробки, якими слід скористатися. Встановлення для
аргументу \fIпотоки\fP особливого значення \fB0\fP наказує \fBxz\fP використати не
більше потоків обробки, ніж передбачено підтримку у процесорах
системи. Справжня кількість потоків може бути меншою за значення \fIпотоки\fP,
якщо файл вхідних даних не є достатньо великим для поділу на потоки обробки
при заданих параметрах або якщо використання додаткових потоків призведе до
перевищення обмеження на використання пам'яті.
.IP ""
Засоби стискання в однопотоковому та багатопотоковому режимі дають різні
результати. Однопотоковий засіб стискання дасть найменший розмір файла, але
лише результати роботи багатопотокового засобу стискання може бути
розпаковано з використанням декількох потоків. Встановлення для аргументу
\fIпотоки\fP значення \fB1\fP призведе до використання однопотокового
режиму. Встановлення для аргументу \fIпотоки\fP будь\-якого іншого значення,
включно з \fB0\fP, призведе до використання багатопотокового засобу стискання,
навіть якщо у системі передбачено підтримки лише одного апаратного потоку
обробки даних. (Версія \fBxz\fP 5.2.x у цьому випадку використовувала
однопотоковий режим.)
.IP ""
Щоб скористатися багатопотоковим режимом із лише одним потоком обробки,
встановіть для аргументу \fIпотоки\fP значення \fB+1\fP. Префікс \fB+\fP не впливає
на значення, окрім \fB1\fP. Обмеження на використання пам'яті можуть перемкнути
\fBxz\fP в однопотоковий режим, якщо не використано параметр
\fB\-\-no\-adjust\fP. Підтримку \fB+\fP prefix було додано у версії \fBxz\fP 5.4.0.
.IP ""
Якщо було вказано автоматичне визначення кількості потоків і не вказано
обмеження на використання пам'яті, буде використано специфічне для системи
типове м'яке обмеження для можливого обмеження кількості потоків обробки. Це
обмеження є м'яким у сенсі того, що його буде проігноровано, якщо кількість
потоків зрівняється з одиницею, а отже, м'яке обмеження ніколи не
запобігатиму у \fBxz\fP стисканню або розпаковуванню. Це типове м'яке обмеження
не перемкне \fBxz\fP з багатопотокового режиму на однопотоковий режим. Активні
обмеження можна переглянути за допомогою команди \fBxz \-\-info\-memory\fP.
.IP ""
У поточній версії єдиним способом поділу на потоки обробки є поділ вхідних
даних на блоки і стискання цих блоків незалежно один від одного. Типовий
розмір блоку залежить від рівня стискання. Його може бути перевизначено за
допомогою параметра \fB\-\-block\-size=\fP\fIрозмір\fP.
.IP ""
Розпакування з потоками обробки працює лише для файлів, які містять декілька
блоків із даними щодо розміру у заголовках блоків. Цю умову задовольняють
усі достатньо великі файли, які стиснено у багатопотоковому режимі, але не
задовольняють будь\-які файли, які було стиснуто у однопотоковому режимі,
навіть якщо було використано параметр \fB\-\-block\-size=\fP\fIрозмір\fP.
.
.SS "Нетипові ланцюжки фільтрів засобу стискання"
Нетиповий ланцюжок фільтрування уможливлює докладне визначення параметрів
стискання замість використання параметрів, які пов'язано із наперед
визначеними рівнями стискання. Якщо вказано нетиповий ланцюжок фільтрів,
параметри рівнів стискання (\fB\-0\fP \&...\& \fB\-9\fP і \fB\-\-extreme\fP), які
передують їм у рядку команди, буде знехтувано. Якщо параметр рівня стискання
вказано після одного або декількох параметрів нетипового ланцюжка фільтрів,
буде використано рівень стискання, а попередніми параметрами ланцюжка
фільтрування буде знехтувано.
.PP
Ланцюжок фільтрів можна порівняти із конвеєром у командному рядку. При
стисканні нестиснені вхідні дані потрапляють до першого фільтра, виведені
ним дані йдуть до наступного фільтра (якщо такий є). Виведені останнім
фільтром дані буде записано до стисненого файла. Максимальна кількість
фільтрів у ланцюжку дорівнює чотирьом, але у типовому ланцюжку фільтрів
використовують один або два фільтри.
.PP
У багатьох фільтрів є обмеження на місце перебування у ланцюжку фільтрів:
деякі фільтри можуть працювати, лише якщо вони є останніми у ланцюжку,
деякі, лише якщо не останніми, а деякі працюють у будь\-якій позиції
ланцюжка. Залежно від фільтра, це обмеження є наслідком структури фільтра
або існує для запобігання проблем із захистом.
.PP
Нетиповий ланцюжок фільтрів визначають за допомогою одного або декількох
параметрів фільтрування у бажаному для ланцюжка фільтрування порядку. Тобто
порядок параметрів фільтрування впливає на результат! При декодуванні
необробленого потоку даних (\fB\-\-format=raw\fP) ланцюжок фільтрів визначають у
тому самому порядку, який використовують для стискання даних.
.PP
Фільтри приймають специфічні для фільтрів \fIпараметри\fP у форматі списку
значень, які відокремлено комами. Зайві коми у \fIпараметрах\fP буде
проігноровано. У кожного параметра є типове значення, отже, вам слід вказати
лише ті параметри, значення яких ви хочете змінити.
.PP
Щоб переглянути увесь ланцюжок фільтрів та \fIпараметри\fP, скористайтеся
командою \fBxz \-vv\fP (тобто, скористайтеся \fB\-\-verbose\fP двічі). Це працює
також для перегляду параметрів ланцюжка фільтрів, який використано у рівнях
стискання.
.TP 
\fB\-\-lzma1\fP[\fB=\fP\fIпараметри\fP]
.PD 0
.TP 
\fB\-\-lzma2\fP[\fB=\fP\fIпараметри\fP]
.PD
Додати фільтр LZMA1 або LZMA2 до ланцюжка фільтрів. Ці фільтри може бути
використано лише як останній фільтр у ланцюжку.
.IP ""
LZMA1 є застарілим фільтром, підтримку якого збережено майже лише через
використання формату файлів \fB.lzma\fP, у яких передбачено підтримку лише
LZMA1. LZMA2 є оновленою версією LZMA1, у якій виправлено деякі практичні
вади LZMA1. У форматі \fB.xz\fP використано LZMA2 і взагалі не передбачено
підтримки LZMA1. Швидкість стискання та коефіцієнт стискання для LZMA1 і
LZMA2 є практично однаковими.
.IP ""
LZMA1 і LZMA2 спільно використовують той самий набір \fIпараметрів\fP:
.RS
.TP 
\fBpreset=\fP\fIшаблон\fP
Скинути усі \fIпараметри\fP LZMA1 або LZMA2 до параметрів \fIшаблона\fP. Аргумент
\fIшаблон\fP складається з цілого числа, після якого може бути однолітерний
модифікатор шаблона. Ціле число може належати лише діапазону від \fB0\fP до
\fB9\fP, що відповідає параметрам командного рядка \fB\-0\fP \&...\& \fB\-9\fP. Єдиним
підтримуваним модифікатором у поточній версії є \fBe\fP, щоб відповідає
параметру \fB\-\-extreme\fP. Якщо аргумент \fBшаблон\fP не вказано, типові значення
\fIпараметрів\fP LZMA1 або LZMA2 буде взято із шаблона \fB6\fP.
.TP 
\fBdict=\fP\fIрозмір\fP
Параметр \fIрозміру\fP словника (буфера журналу) визначає, скільки байтів
нещодавно оброблених нестиснених даних слід зберігати у пам'яті. Алгоритм
намагається знайти повторювані послідовності байтів (відповідники) у
нестиснених даних і замінити їх на посилання на дані зі словника. Чим
більшим є словник, тим вищою є ймовірність відшукати відповідник. Отже,
збільшення \fIрозміру\fP словника, зазвичай, покращує коефіцієнт стискання, але
використання словника, розмір якого перевищу є розмір нестисненого файла є
простоюю витратою пам'яті.
.IP ""
\fIРозмір\fP типового словника складає від 64\ КіБ до 64\ МіБ. Мінімальним є
розмір 4\ КіБ. Максимальним розміром для стискання у поточній версії 1.5\ ГіБ (1536\ МіБ). У засобі розпаковування вже передбачено підтримку словників
на один байт менших за 4\ ГіБ, що є максимальним значенням для форматів
потоків даних LZMA1 і LZMA2.
.IP ""
Аргумент \fIрозміру\fP словника і засіб пошуку відповідників (\fImf\fP) разом
визначають параметри використання пам'яті для кодувальника LZMA1 або
LZMA2. Для розпаковування потрібен такий самий (або більший) \fIрозмір\fP
словника, що і для стискання, отже, використання пам'яті для засобу
розпаковування буде визначено за розміром словника, який було використано
для стискання. У заголовках \fB.xz\fP зберігається \fIрозмір\fP словника або як
2^\fIn\fP, або як 2^\fIn\fP + 2^(\fIn\fP\-1), отже, ці \fIрозміри\fP є дещо пріоритетними
для стискання. Інші \fIрозміри\fP буде отримано округленням при зберіганні у
заголовках \fB.xz\fP.
.TP 
\fBlc=\fP\fIlc\fP
Визначає кількість буквальних контекстних бітів. Мінімальною кількістю є 0,
а максимальною — 4. Типовою кількістю є 3. Крім того, сума \fIlc\fP і \fIlp\fP має
не перевищувати 4.
.IP ""
Усі байти, які не може бути закодовано як відповідності, буде закодовано як
літерали. Тобто літерали є просто 8\-бітовими байтами, які буде закодовано по
одному за раз.
.IP ""
При кодуванні літералів роблять припущення, що найвищі біти \fIlc\fP
попереднього нестисненого байта корелюють із наступним байтом. Наприклад, у
типовому тексті англійською за літерою у верхньому регістрі йде літера у
нижньому регістрі, а за літерою у нижньому регістрі, зазвичай, йде інша
літера у нижньому регістрі. У наборі символів US\-ASCII найвищими трьома
бітами є 010 для літер верхнього регістру і 011 для літер нижнього
регістру. Якщо \fIlc\fP дорівнює принаймні 3, при кодуванні літералів можна
отримати перевагу встановлення цієї властивості для нестиснених даних.
.IP ""
Зазвичай, типового значення (3) достатньо. Якщо вам потрібне максимальне
стискання, спробуйте \fBlc=4\fP. Іноді це трохи допомагає, а іноді, робить
стискання гіршим. Якщо стискання стане гіршим, спробуйте також \fBlc=2\fP.
.TP 
\fBlp=\fP\fIlp\fP
Визначає кількість буквальних позиційних бітів. Мінімальною кількістю є 0, а
максимальною — 4. Типовою кількістю є 0.
.IP ""
\fILp\fP впливає на те, яке вирівнювання у нестиснених даних слід припускати
при кодуванні літералів. Див. \fIpb\fP нижче, щоб дізнатися більше про
вирівнювання.
.TP 
\fBpb=\fP\fIpb\fP
Визначає кількість позиційних бітів. Мінімальною кількістю є 0, а
максимальною — 4. Типовою кількістю є 2.
.IP ""
\fIPb\fP впливає на те, який тип вирівнювання загалом припускатиметься для
нестиснених даних. Типовим є чотирибайтове вирівнювання (2^\fIpb\fP=2^2=4),
яке, зазвичай, є добрим варіантом, якщо немає кращих припущень.
.IP ""
Якщо вирівнювання є відомим, встановлення відповідним чином \fIpb\fP може трохи
зменшити розмір файла. Наприклад, у текстових файлах із однобайтовим
вирівнюванням (US\-ASCII, ISO\-8859\-*, UTF\-8), встановлення значення \fBpb=0\fP
може трохи поліпшити стискання. Для тексту UTF\-16 добрим варіантом є
\fBpb=1\fP. Якщо вирівнювання є непарним числом, наприклад 3 байти, найкращим
вибором, ймовірно, є \fBpb=0\fP.
.IP ""
Хоча прогнозоване вирівнювання можна скоригувати за допомогою \fIpb\fP і \fIlp\fP,
у LZMA1 і LZMA2 дещо пріоритетним є 16\-байтове вирівнювання. Це, ймовірно,
слід враховувати при компонуванні форматів файлів, які, ймовірно, часто
будуть стискатися з використанням LZMA1 або LZMA2.
.TP 
\fBmf=\fP\fImf\fP
Засіб пошуку відповідників має значний вплив на швидкість, використання
пам'яті та коефіцієнт стискання кодувальника. Зазвичай, засоби пошуку
відповідників на основі ланцюжка хешів є швидшими за засоби пошуку
відповідників на основі двійкового дерева. Типовий засіб залежить від
\fIшаблона\fP: для 0 використовують \fBhc3\fP, для 1\(en3 — \fBhc4\fP, а для решти
використовують \fBbt4\fP.
.IP ""
Передбачено підтримку вказаних нижче засобів пошуку відповідників. Наведені
нижче формули обчислення використання пам'яті є грубими наближеннями, які є
найближчими до реальних значень, якщо значенням \fIсловник\fP є степінь двійки.
.RS
.TP 
\fBhc3\fP
Ланцюжок хешів із 2\- та 3\-байтовим хешуванням
.br
Мінімальне значення \fIпріоритетності\fP: 3
.br
Використання пам'яті:
.br
\fIdict\fP * 7.5 (якщо \fIdict\fP <= 16 МіБ);
.br
\fIdict\fP * 5.5 + 64 МіБ (якщо \fIdict\fP > 16 МіБ)
.TP 
\fBhc4\fP
Ланцюжок хешів із 2\-, 3\- та 4\-байтовим хешуванням
.br
Мінімальне значення \fIпріоритетності\fP: 4
.br
Використання пам'яті:
.br
\fIdict\fP * 7.5 (якщо \fIdict\fP <= 32 МіБ);
.br
\fIdict\fP * 6.5 (якщо \fIdict\fP > 32 МіБ)
.TP 
\fBbt2\fP
Двійкове дерево із 2\-байтовим хешуванням
.br
Мінімальне значення \fIпріоритетності\fP: 2
.br
Використання пам'яті: \fIdict\fP * 9.5
.TP 
\fBbt3\fP
Двійкове дерево із 2\- і 3\-байтовим хешуванням
.br
Мінімальне значення \fIпріоритетності\fP: 3
.br
Використання пам'яті:
.br
\fIdict\fP * 11.5 (якщо \fIdict\fP <= 16 МіБ);
.br
\fIdict\fP * 9.5 + 64 МіБ (якщо \fIdict\fP > 16 МіБ)
.TP 
\fBbt4\fP
Двійкове дерево із 2\-, 3\- і 4\-байтовим хешуванням
.br
Мінімальне значення \fIпріоритетності\fP: 4
.br
Використання пам'яті:
.br
\fIdict\fP * 11.5 (якщо \fIdict\fP <= 32 МіБ);
.br
\fIdict\fP * 10.5 (якщо \fIdict\fP > 32 МіБ)
.RE
.TP 
\fBmode=\fP\fIрежим\fP
Параметр \fIрежиму\fP стискання визначає спосіб, який буде використано для
аналізу даних, які створено засобом пошуку відповідників. Підтримуваними
\fIрежимами\fP є \fBfast\fP (швидкий) і \fBnormal\fP (нормальний). Типовим є режим
\fBfast\fP для \fIшаблонів\fP 0\(en3 і режим \fBnormal\fP для \fIшаблонів\fP 4\(en9.
.IP ""
Зазвичай, із засобом пошуку відповідників на основі ланцюжка хешів
використовують \fBfast\fP, а із засобом пошуку відповідників на основі
двійкового дерева використовують \fBnormal\fP. Так само налаштовано і
\fIшаблони\fP.
.TP 
\fBnice=\fP\fIпріоритетність\fP
Вказати, яка довжина є пріоритетною для відповідності. Щойно буде виявлено
відповідність у принаймні \fIпріоритетність\fP байтів, алгоритм зупинятиме
пошук можливих кращих відповідників.
.IP ""
\fIПріоритетністю\fP може бути число до 2\(en273 байтів. Вищі значення дають
кращий коефіцієнт стискання за рахунок швидкості. Типове значення залежить
від \fIшаблона\fP.
.TP 
\fBdepth=\fP\fIглибина\fP
Вказати максимальну глибину пошуку у засобі пошуку відповідності. Типовим є
особливе значення 0, яке наказує засобу стискання визначити прийнятну
\fIглибину\fP на основі \fImf\fP і \fIпріоритетності\fP.
.IP ""
Прийнятним значенням \fIглибини\fP для ланцюжків хешів є 4\(en100 і 16\(en1000
для двійкових дерев. Використання дуже високих значень для \fIглибини\fP може
зробити кодувальник дуже повільним для деяких файлів. Не встановлюйте
значення \fIглибини\fP, що перевищує 1000, якщо ви не готові перервати
стискання, якщо воно триватиме надто довго.
.RE
.IP ""
При декодуванні необроблених потоків даних (\fB\-\-format=raw\fP), LZMA2 потребує
лише \fIрозміру\fP словника. LZMA1 потребує також \fIlc\fP, \fIlp\fP і \fIpb\fP.
.TP 
\fB\-\-x86\fP[\fB=\fP\fIпараметри\fP]
.PD 0
.TP 
\fB\-\-arm\fP[\fB=\fP\fIпараметри\fP]
.TP 
\fB\-\-armthumb\fP[\fB=\fP\fIпараметри\fP]
.TP 
\fB\-\-arm64\fP[\fB=\fP\fIпараметри\fP]
.TP 
\fB\-\-powerpc\fP[\fB=\fP\fIпараметри\fP]
.TP 
\fB\-\-ia64\fP[\fB=\fP\fIпараметри\fP]
.TP 
\fB\-\-sparc\fP[\fB=\fP\fIпараметри\fP]
.PD
Додати фільтр гілок/викликів/переходів (branch/call/jump або BCJ) до
ланцюжка фільтрів. Цими фільтрами можна скористатися, лише якщо вони не є
останнім фільтром у ланцюжку фільтрів.
.IP ""
Фільтр BCJ перетворює відносні адреси у машинному коді на їхні абсолютні
відповідники. Це не змінює розміру даних, але підвищує резервування, що може
допомогти LZMA2 створити файл \fB.xz\fP на 0\(en15\ % менше. Фільтри BCJ завжди
є придатними до обернення, тому використання фільтра BCJ до помилкового типу
даних не спричинятиме втрати даних, хоча може дещо погіршити коефіцієнт
стискання. Фільтри BCJ є дуже швидкими і такими, що використовують незначний
об'єм пам'яті.
.IP ""
Ці фільтри BCJ мають відомі проблеми, які пов'язано із рівнем стискання:
.RS
.IP \(bu 3
У деяких типах файлів, де зберігається виконуваний код, (наприклад, в
об'єктних файлах, статичних бібліотеках та модулях ядра Linux) адреси в
інструкціях заповнено значеннями заповнювача. Ці фільтри BCJ виконуватимуть
перетворення адрес, яке зробить стискання для цих файлів гіршим.
.IP \(bu 3
Якщо фільтр BCJ застосовано до архіву, може так статися, що він погіршить
коефіцієнт стискання порівняно із варіантом без фільтра BCJ. Наприклад, якщо
є подібні або навіть однакові виконувані файли, фільтрування, ймовірно,
зробить ці файли менш подібними, а отже, зробить стискання гіршим. Вміст
файлів, які не є виконуваними, у тому самому архіві також може вплинути на
результат. На практиці, варто спробувати варіанти з фільтром BCJ і без
нього, щоб визначитися із тим, що буде кращим у кожній ситуації.
.RE
.IP ""
Різні набори інструкцій мають різне вирівнювання: виконуваний файл має бути
вирівняно на кратне до цього значення у вхідних даних, щоб фільтр спрацював.
.RS
.RS
.PP
.TS
tab(;);
l n l
l n l.
Фільтр;Вирівнювання;Нотатки
x86;1;32\-бітова або 64\-бітова x86
ARM;4;
ARM\-Thumb;2;
ARM64;4;Найкращим є вирівнювання за
;;4096 байтами
PowerPC;4;Лише зворотний порядок байтів
IA\-64;16;Itanium
SPARC;4;
.TE
.RE
.RE
.IP ""
Оскільки фільтровані BCJ дані, зазвичай, стискають за допомогою LZMA2,
коефіцієнт стискання можна трохи поліпшити, якщо параметри LZMA2 буде
встановлено так, щоб вони відповідали вирівнюванню вибраного фільтра
BCJ. Наприклад, з фільтром IA\-64 варто встановити \fBpb=4\fP або навіть
\fBpb=4,lp=4,lc=0\fP з LZMA2 (2^4=16). Фільтр x86 є винятком; його, зазвичай,
варто поєднувати із типовим чотирибайтовим вирівнюванням LZMA2 при стисканні
виконуваних файлів x86.
.IP ""
У всіх фільтрах BCJ передбачено підтримку тих самих \fIпараметрів\fP:
.RS
.TP 
\fBstart=\fP\fIзсув\fP
Встановити початковий \fIзсув\fP, який буде використано при перетворенні між
відносною та абсолютною адресами. Значення \fIзсув\fP має бути кратним до
вирівнювання фільтра (див. таблицю вище).  Типовим зсувом є нульовий. На
практиці, типове значення є прийнятним; визначення нетипового значення
\fIзсув\fP майже завжди нічого корисного не дає.
.RE
.TP 
\fB\-\-delta\fP[\fB=\fP\fIпараметри\fP]
Додати дельта\-фільтр до ланцюжка фільтрів. Дельта\-фільтр може бути
використано, лише якщо він не є останнім у ланцюжку фільтрів.
.IP ""
У поточній версії передбачено підтримку обчислення лише простої побітової
дельти. Це може бути корисним при стисканні, наприклад, нестиснутих
растрових зображень або нестиснутих звукових даних PCM. Втім, спеціалізовані
алгоритми можуть давати значно кращі результати за дельту + LZMA2. Це
правило особливо стосується звукових даних, які стискає швидше і краще,
наприклад, \fBflac\fP(1).
.IP ""
Підтримувані \fIпараметри\fP:
.RS
.TP 
\fBdist=\fP\fIвідстань\fP
Вказати \fIвідстань\fP обчислень різниці у байтах. Значення \fIвідстань\fP має
потрапляти у діапазон 1\(en256. Типовим значенням є 1.
.IP ""
Наприклад, з \fBdist=2\fP та восьмибайтовими вхідними даними A1 B1 A2 B3 A3 B5
A4 B7, результатом буде A1 B1 01 02 01 02 01 02.
.RE
.
.SS "Інші параметри"
.TP 
\fB\-q\fP, \fB\-\-quiet\fP
Придушити попередження та сповіщення. Вкажіть цей параметр двічі, щоб
придушити також повідомлення про помилки. Цей параметр не впливає на стан
виходу з програми. Тобто, навіть якщо було придушено попередження, стан
виходу вказуватиме на те, що попередження були.
.TP 
\fB\-v\fP, \fB\-\-verbose\fP
Докладний режим повідомлень. Якщо стандартне виведення помилок з'єднано із
терміналом, \fBxz\fP показуватиме індикатор поступу. Використання \fB\-\-verbose\fP
двічі призведе до ще докладнішого виведення.
.IP ""
Індикатор поступу показує такі дані:
.RS
.IP \(bu 3
Частку завершеності буде показано, якщо відомий розмір файла вхідних
даних. Тобто, для каналів даних частку не може бути показано.
.IP \(bu 3
Об'єм стиснених виведених даних (стискання) або оброблених (розпаковування).
.IP \(bu 3
Об'єм незапакованих даних (стискання) або виведених даних (розпаковування).
.IP \(bu 3
Коефіцієнт стискання, який обчислено діленням об'єму оброблених стиснутих
даних на об'єм оброблених нестиснутих даних.
.IP \(bu 3
Швидкість стискання або розпаковування. Обчислюється як об'єм нестиснутих
даних (стискання) або виведених даних (розпаковування) за секунду. Його буде
показано за декілька секунд з моменту, коли \fBxz\fP почала обробляти файл.
.IP \(bu 3
Витрачений час у форматі Х:СС або Г:ХХ:СС.
.IP \(bu 3
Оцінку часу, що лишився, буде показано, лише якщо розмір файла вхідних даних
є відомим, і минуло принаймні декілька секунд з моменту, коли \fBxz\fP почала
обробляти файл. Час буде показано у менш точному форматі, без двокрапок,
наприклад, 2 хв. 30 с.
.RE
.IP ""
Якщо стандартним виведенням помилок не є термінал, \fB\-\-verbose\fP призведе до
того, що \fBxz\fP виведе назву файла, стиснений розмір, нестиснений розмір,
коефіцієнт стискання та, можливо, також швидкість та витрачений час у одному
рядку до стандартного виведення помилок після стискання або розпаковування
файла. Швидкість та витрачений час буде включено, лише якщо дія триває
принаймні декілька секунд. Якщо дію не буде завершено, наприклад, через
втручання користувача, буде також виведено частку виконання, якщо відомий
розмір файла вхідних даних.
.TP 
\fB\-Q\fP, \fB\-\-no\-warn\fP
Не встановлювати стан виходу 2, навіть якщо було виявлено відповідність
умові, яка варта попередження. Цей параметр не впливає на рівень докладності
повідомлень, отже, слід використати \fB\-\-quiet\fP і \fB\-\-no\-warn\fP, щоб програма
не показувала попереджень і не змінювала стан виходу.
.TP 
\fB\-\-robot\fP
Виводити повідомлення у придатному для обробки комп'ютером форматі. Цей
формат призначено для полегшення написання оболонок, які використовуватимуть
\fBxz\fP замість liblzma, що може бути зручним для різноманітних
скриптів. Виведені дані з цим параметром має бути стабільним для усіх
випусків \fBxz\fP. Докладніший опис можна знайти у розділі \fBРЕЖИМ РОБОТА\fP.
.TP 
\fB\-\-info\-memory\fP
Вивести у придатному для читання людиною форматі, скільки фізичної пам'яті
(RAM) та скільки потоків процесора є за даними \fBxz\fP у системі, обмеження
для стискання та розпаковування, а потім успішно завершити роботу.
.TP 
\fB\-h\fP, \fB\-\-help\fP
Вивести повідомлення про помилку з описом найбільш типових використаних
параметрів і успішно завершити роботу.
.TP 
\fB\-H\fP, \fB\-\-long\-help\fP
Вивести довідкове повідомлення з описом усіх можливостей \fBxz\fP і успішно
завершити роботу
.TP 
\fB\-V\fP, \fB\-\-version\fP
Вивести номер версії \fBxz\fP та liblzma у зручному для читання форматі. Щоб
отримати дані, зручні для обробки на комп'ютері, вкажіть \fB\-\-robot\fP до
\fB\-\-version\fP.
.
.SH "РЕЖИМ РОБОТА"
Режим робота активують за допомогою параметра \fB\-\-robot\fP. Він спрощує
обробку виведених \fBxz\fP даних іншими програмами. У поточній версії підтримку
\fB\-\-robot\fP передбачено лише разом із \fB\-\-version\fP, \fB\-\-info\-memory\fP та
\fB\-\-list\fP. У майбутньому підтримку параметра буде передбачено для стискання
та розпаковування.
.
.SS Версія
\fBxz \-\-robot \-\-version\fP виведе назву версії \fBxz\fP і liblzma у такому
форматі:
.PP
\fBXZ_VERSION=\fP\fIXYYYZZZS\fP
.br
\fBLIBLZMA_VERSION=\fP\fIXYYYZZZS\fP
.TP 
\fIX\fP
Основна версія.
.TP 
\fIYYY\fP
Проміжна версія. Непарні номери буде використано для стабільних
версій. Непарні номери є номерами тестових версій.
.TP 
\fIZZZ\fP
Рівень латання для стабільних випусків або просто лічильник для випусків,
які перебувають у розробці.
.TP 
\fIS\fP
Стабільність. 0 — alpha, 1 — beta, а 2 означає «стабільна версія». \fIS\fP має
завжди дорівнювати 2, якщо \fIYYY\fP є парним.
.PP
\fIXYYYZZZS\fP є тим самим в обох рядках, якщо \fBxz\fP і liblzma належать до
одного випуску XZ Utils.
.PP
Приклади: 4.999.9beta — це \fB49990091\fP, а 5.0.0 — це \fB50000002\fP.
.
.SS "Дані щодо обмеження пам'яті"
\fBxz \-\-robot \-\-info\-memory\fP виводить один рядок з декількома відокремленими
табуляціями стовпчиками:
.IP 1. 4
Загальний об'єм фізичної пам'яті (RAM) у байтах.
.IP 2. 4
Обмеження на використання пам'яті для стискання у байтах
(\fB\-\-memlimit\-compress\fP). Особливе значення \fB0\fP вказує на типові
налаштування, якими для однопотокового режиму є налаштування без обмеження
на використання пам'яті.
.IP 3. 4
Обмеження на використання пам'яті для розпакування у байтах
(\fB\-\-memlimit\-decompress\fP). Особливе значення \fB0\fP вказує на типові
налаштування, якими для однопотокового режиму є налаштування без обмеження
на використання пам'яті.
.IP 4. 4
Починаючи з \fBxz\fP 5.3.4alpha: використання пам'яті для багатопотокового
розпаковування у байтах (\fB\-\-memlimit\-mt\-decompress\fP). Ніколи не дорівнює
нулеві, оскільки буде використано специфічне для системи типове значення,
яке показано у стовпчику 5, якщо обмеження не встановлено явним чином. Також
ніколи не перевищуватиме значення у стовпчику 3, навіть якщо було вказано
більше значення за допомогою \fB\-\-memlimit\-mt\-decompress\fP.
.IP 5. 4
Починаючи з \fBxz\fP 5.3.4alpha: специфічне для системи типове обмеження на
використання пам'яті, яке використовують для обмеження кількості потоків при
стисканні з автоматичною кількістю потоків (\fB\-\-threads=0\fP) і без визначення
обмеження на використання пам'яті (\fB\-\-memlimit\-compress\fP). Це значення
також використовують як типове значення для \fB\-\-memlimit\-mt\-decompress\fP.
.IP 6. 4
Починаючи з \fBxz\fP 5.3.4alpha: кількість доступних потоків обробки процесора.
.PP
У майбутньому у виведенні \fBxz \-\-robot \-\-info\-memory\fP може бути більше
стовпчиків, але у виведеному буде не більше за один рядок.
.
.SS "Режим списку"
У \fBxz \-\-robot \-\-list\fP використано табуляції для поділу виведених
даних. Першим стовпчиком у кожному рядку є рядок, що вказує на тип
відомостей, які можна знайти у цьому рядку:
.TP 
\fBназва\fP
Це завжди перший рядок на початку списку файла. Другим стовпчиком у рядку є
назва файла.
.TP 
\fBфайл\fP
У цьому рядку містяться загальні відомості щодо файла \fB.xz\fP. Цей рядок
завжди виводять після рядка \fBname\fP.
.TP 
\fBstream\fP
Цей тип рядка використовують, лише якщо було вказано \fB\-\-verbose\fP. Буде
стільки рядків \fBstream\fP, скільки потоків у файлі \fB.xz\fP.
.TP 
\fBblock\fP
Цей тип рядка використовують, лише якщо було вказано \fB\-\-verbose\fP. Буде
стільки рядків \fBblock\fP, скільки блоків у файлі \fB.xz\fP. Рядки \fBblock\fP буде
показано після усіх рядків \fBstream\fP; різні типи рядків не перемежовуються.
.TP 
\fBsummary\fP
Цей тип рядків використовують, лише якщо \fB\-\-verbose\fP було вказано
двічі. Цей рядок буде виведено після усіх рядків \fBblock\fP. Подібно до рядка
\fBfile\fP, рядок \fBsummary\fP містить загальні відомості щодо файла \fB.xz\fP.
.TP 
\fBtotals\fP
Цей рядок завжди є найостаннішим рядком у виведеному списку. У ньому буде
показано загальні кількості та розміри.
.PP
Стовпчики у рядках \fBфайла\fP:
.PD 0
.RS
.IP 2. 4
Кількість потоків у файлі
.IP 3. 4
Загальна кількість блоків у потоках
.IP 4. 4
Розмір стисненого файла
.IP 5. 4
Розмір нестисненого файла
.IP 6. 4
Коефіцієнт стискання, наприклад, \fB0.123\fP. Якщо коефіцієнт перевищує 9.999,
замість коефіцієнта буде показано дефіси (\fB\-\-\-\fP).
.IP 7. 4
Список відокремлених комами назв перевірок цілісності. Наведені нижче рядки
використовують для відомих типів перевірок: \fBNone\fP, \fBCRC32\fP, \fBCRC64\fP і
\fBSHA\-256\fP. Для невідомих типів перевірок буде використано \fBUnknown\-\fP\fIN\fP,
де \fIN\fP є ідентифікатором перевірки у форматі десяткового числа (одна або
дві цифри).
.IP 8. 4
Загальний розмір доповнення потоку у файлі
.RE
.PD
.PP
Стовпчики у рядках \fBstream\fP:
.PD 0
.RS
.IP 2. 4
Номер потоку (перший потік має номер 1)
.IP 3. 4
Кількість блоків у потоці
.IP 4. 4
Зсув початку стисненого
.IP 5. 4
Зсув початку нестисненого
.IP 6. 4
Стиснений розмір (не включає доповнення потоку)
.IP 7. 4
Нестиснутий розмір
.IP 8. 4
Рівень стискання
.IP 9. 4
Назва перевірки цілісності
.IP 10. 4
Розмір доповнення потоку
.RE
.PD
.PP
Стовпчики у рядках \fBblock\fP:
.PD 0
.RS
.IP 2. 4
Номер потоку, що містить цей блок
.IP 3. 4
Номер блоку відносно початку потоку (перший блок має номер 1)
.IP 4. 4
Номер блоку відносно початку файла
.IP 5. 4
Зсув початку стисненого відносно початку файла
.IP 6. 4
Зсув початку нестисненого відносно початку файла
.IP 7. 4
Загальний стиснений розмір блоку (включено з заголовками)
.IP 8. 4
Нестиснутий розмір
.IP 9. 4
Рівень стискання
.IP 10. 4
Назва перевірки цілісності
.RE
.PD
.PP
Якщо \fB\-\-verbose\fP було вказано двічі, до рядків \fBblock\fP буде включено
додаткові стовпчики. Ці стовпчики не буде показано, якщо вказано одинарний
параметр \fB\-\-verbose\fP, оскільки отримання цих відомостей потребує багатьох
позиціювань, а ця процедура може бути повільною:
.PD 0
.RS
.IP 11. 4
Значення перевірки цілісності у шістнадцятковій формі
.IP 12. 4
Розмір заголовка блоку
.IP 13. 4
Прапорці блоку: \fBc\fP вказує, що наявний стиснений розмір, а \fBu\fP вказує, що
наявний нестиснений розмір. Якщо прапорець не встановлено, буде показано
(\fB\-\fP) замість підтримання фіксованого розміру рядка. У майбутньому
наприкінці рядка може бути додано нові прапорці.
.IP 14. 4
Розмір справжніх стиснених даних у блоці (це включає заголовок блоку,
доповнення блоку та поля перевірок)
.IP 15. 4
Об'єм пам'яті (у байтах), який потрібен для розпаковування цього блоку за
допомогою цієї версії \fBxz\fP
.IP 16. 4
Ланцюжок фільтрів. Зауважте, що більшість параметрів, які використано під
час стискання, не є наперед відомим, оскільки у заголовках \fB.xz\fP
зберігаються лише параметри, які потрібні для розпаковування.
.RE
.PD
.PP
Стовпчики у рядках \fBsummary\fP:
.PD 0
.RS
.IP 2. 4
Об'єм пам'яті (у байтах), який потрібен для розпаковування цього файла за
допомогою цієї версії \fBxz\fP
.IP 3. 4
\fByes\fP або \fBno\fP вказує, якщо усі заголовки блоків містять одразу стиснений
розмір та розпакований розмір
.PP
\fIПочинаючи з\fP \fBxz\fP \fI5.1.2alpha:\fP
.IP 4. 4
Мінімальна версія \fBxz\fP, яка потрібна для розпаковування файла
.RE
.PD
.PP
Стовпчики рядка \fBtotals\fP:
.PD 0
.RS
.IP 2. 4
Кількість потоків
.IP 3. 4
Кількість блоків
.IP 4. 4
Стиснутий розмір
.IP 5. 4
Нестиснутий розмір
.IP 6. 4
Середній коефіцієнт стискання
.IP 7. 4
Список відокремлених комами назв перевірок цілісності, результати яких
наявні у файлах
.IP 8. 4
Розмір доповнення потоку
.IP 9. 4
Кількість файлів. Наявний тут для зберігання такого самого порядку
стовпчиків, що і у попередніх рядках \fBfile\fP.
.PD
.RE
.PP
Якщо \fB\-\-verbose\fP було вказано двічі, до рядка \fBtotals\fP буде включено
додаткові стовпчики:
.PD 0
.RS
.IP 10. 4
Максимальний об'єм пам'яті (у байтах), який потрібен для розпаковування
файлів за допомогою цієї версії \fBxz\fP
.IP 11. 4
\fByes\fP або \fBno\fP вказує, якщо усі заголовки блоків містять одразу стиснений
розмір та розпакований розмір
.PP
\fIПочинаючи з\fP \fBxz\fP \fI5.1.2alpha:\fP
.IP 12. 4
Мінімальна версія \fBxz\fP, яка потрібна для розпаковування файла
.RE
.PD
.PP
У майбутніх версіях може бути додано нові типи рядків і нові стовпчики до
наявних типів рядків, але наявні стовпчики мають лишитися незмінними.
.
.SH "СТАН ВИХОДУ"
.TP 
\fB0\fP
Усе добре.
.TP 
\fB1\fP
Сталася помилка.
.TP 
\fB2\fP
Сталося щось варте попередження, але справжніх помилок не сталося.
.PP
Зауваження (не попередження або помилки), які виведено до стандартного
виведення помилок, не впливають на стан виходу.
.
.SH СЕРЕДОВИЩЕ
\fBxz\fP обробляє списки відокремлених пробілами параметрів зі змінних
середовища \fBXZ_DEFAULTS\fP і \fBXZ_OPT\fP, перш ніж обробляти параметри з рядка
команди. Зауважте, що буде оброблено лише параметри зі змінних середовища;
усі непараметричні записи буде без повідомлень проігноровано. Обробку буде
виконано за допомогою функції \fBgetopt_long\fP(3), яку також використовують
для аргументів рядка команди.
.TP 
\fBXZ_DEFAULTS\fP
Специфічні для користувача або загальносистемні типові параметри. Зазвичай,
їх встановлюють у скрипті ініціалізації оболонки для типового вмикання
обмеження на використання пам'яті у \fBxz\fP. Окрім скриптів ініціалізації
оболонки і подібних особливих випадків, не слід встановлювати або
скасовувати встановлення значення \fBXZ_DEFAULTS\fP у скриптах.
.TP 
\fBXZ_OPT\fP
Цю змінну призначено для передавання параметрів до \fBxz\fP, якщо неможливо
встановити параметри безпосередньо у рядку команди \fBxz\fP. Це трапляється,
якщо \fBxz\fP запущено скриптом або інструментом, наприклад, GNU \fBtar\fP(1):
.RS
.RS
.PP
.nf
\f(CWXZ_OPT=\-2v tar caf foo.tar.xz foo\fP
.fi
.RE
.RE
.IP ""
Скрипти можуть використовувати \fBXZ_OPT\fP, наприклад, для встановлення
специфічних типових параметрів стискання. Втім, рекомендуємо дозволити
користувачам перевизначати \fBXZ_OPT\fP, якщо це має якісь причини. Наприклад,
у скриптах \fBsh\fP(1) можна скористатися чимось таким:
.RS
.RS
.PP
.nf
\f(CWXZ_OPT=${XZ_OPT\-"\-7e"} export XZ_OPT\fP
.fi
.RE
.RE
.
.SH "СУМІСНІСТЬ ІЗ LZMA UTILS"
Синтаксис рядка команди \fBxz\fP практично є надбудовою щодо \fBlzma\fP, \fBunlzma\fP
і \fBlzcat\fP з LZMA Utils 4.32.x. У більшості випадків можна замінити LZMA
Utils XZ Utils без порушення працездатності наявних скриптів. Втім, існують
певні несумісності, які іноді можуть спричиняти проблеми.
.
.SS "Рівні шаблонів стискання"
Нумерація у шаблонах рівнів стискання у \fBxz\fP не є тотожною до нумерації у
LZMA Utils. Найважливішою відмінністю є прив'язка розмірів словника до
різних шаблонів. Розмір словника грубо рівний використанню пам'яті у засобі
розпаковування.
.RS
.PP
.TS
tab(;);
c c c
c n n.
Рівень;xz;LZMA Utils
\-0;256 КіБ;н/д
\-1;1 МіБ;64 КіБ
\-2;2 МіБ;1 МіБ
\-3;4 МіБ;512 КіБ
\-4;4 МіБ;1 МіБ
\-5;8 МіБ;2 МіБ
\-6;8 МіБ;4 МіБ
\-7;16 МіБ;8 МіБ
\-8;32 МіБ;16 МіБ
\-9;64 МіБ;32 МіБ
.TE
.RE
.PP
Відмінності у розмірах словників також впливають на використання пам'яті
засобом стискання, але є і інші відмінності між LZMA Utils і XZ Utils, які
роблять різницю ще помітнішою:
.RS
.PP
.TS
tab(;);
c c c
c n n.
Рівень;xz;LZMA Utils 4.32.x
\-0;3 МіБ;н/д
\-1;9 МіБ;2 МіБ
\-2;17 МіБ;12 МіБ
\-3;32 МіБ;12 МіБ
\-4;48 МіБ;16 МіБ
\-5;94 МіБ;26 МіБ
\-6;94 МіБ;45 МіБ
\-7;186 МіБ;83 МіБ
\-8;370 МіБ;159 МіБ
\-9;674 МіБ;311 МіБ
.TE
.RE
.PP
Типовим рівнем стискання у LZMA Utils є \fB\-7\fP, а у XZ Utils — \fB\-6\fP, отже,
обидва комплекти програм типово використовують словник розміром у 8 МіБ.
.
.SS "Потокові і непотокові файл .lzma"
Розмір нестисненого файла може бути збережено у заголовку \fB.lzma\fP. LZMA
Utils зберігають дані при стисканні звичайних файлів. Альтернативним
підходом є позначення нестисненого розміру як невідомого і використання
позначки кінця вмісту для позначення місця, де засіб розпаковування має
зупинитися. У LZMA Utils цей спосіб використовують, якщо нестиснений розмір
є невідомим, що трапляється, наприклад, для конвеєрів обробки даних.
.PP
У \fBxz\fP передбачено підтримку розпаковування файлів \fB.lzma\fP з позначкою
кінця вмісту та без неї, але усі файли \fB.lzma\fP, які створено за допомогою
\fBxz\fP, використовують позначку кінця вмісту, а нестиснений розмір у
заголовку \fB.lzma\fP позначають як невідомий. Це може призвести до проблем у
деяких нетипових ситуаціях. Наприклад, розпакувальник \fB.lzma\fP у вбудованому
пристрої може працювати лише з файлами, для яких відомий нестиснений
розмір. Якщо ви зіткнулися з цією проблемою, вам слід скористатися LZMA
Utils або LZMA SDK для створення файлів \fB.lzma\fP із відомим розміром
нестиснених даних.
.
.SS "Непідтримувані файли .lzma"
У форматі \fB.lzma\fP можливі значення \fIlc\fP аж до 8 і значення \fIlp\fP аж до
4. LZMA Utils можуть розпаковувати файли із будь\-якими значеннями \fIlc\fP і
\fIlp\fP, але завжди створюють файли з \fBlc=3\fP і \fBlp=0\fP. Створення файлів з
іншими значеннями \fIlc\fP і \fIlp\fP є можливим за допомогою \fBxz\fP і LZMA SDK.
.PP
Реалізація фільтра LZMA1 у liblzma потребує, щоби сума \fIlc\fP і \fIlp\fP не
перевищувала 4. Отже, файли \fB.lzma\fP, у яких перевищено обмеження, не може
бути розпаковано за допомогою \fBxz\fP.
.PP
LZMA Utils створюють лише файли \fB.lzma\fP, які мають розмір словника у 2^\fIn\fP
(степінь 2), але приймають файли із будь\-яким розміром словника. liblzma
приймає лише файли \fB.lzma\fP, які мають розмір словника 2^\fIn\fP або 2^\fIn\fP +
2^(\fIn\fP\-1). Так зроблено для зменшення помилок при виявленні файлів
\&\fB.lzma\fP.
.PP
Ці обмеження не мають призводити до проблем на практиці, оскільки практично
усі файли \fB.lzma\fP було стиснено з використанням параметрів, які приймає
liblzma.
.
.SS "Кінцевий мотлох"
При розпаковуванні LZMA Utils без повідомлень ігнорують усі дані після
першого потоку \fB.lzma\fP. У більшості випадків це пов'язано із вадою у
програмі. Це також означає, що у LZMA Utils не передбачено підтримки
розпаковування з'єднаних файлів \fB.lzma\fP.
.PP
Якщо після першого потоку \fB.lzma\fP лишилися дані, \fBxz\fP вважатиме файл
пошкодженим, якщо не було використано \fB\-\-single\-stream\fP. Це може зашкодити
роботі скриптів, де зроблено припущення, що кінцеві зайві дані буде
проігноровано.
.
.SH ПРИМІТКИ
.
.SS "Стискання даних може бути різним"
Точні стиснені дані, які створено на основі того самого нестисненого файла
вхідних даних, можуть бути різними для різних версій XZ Utils, навіть якщо
використано однакові параметри стискання. Причиною цього є удосконалення у
кодувальнику (пришвидшення або краще стискання) без зміни формату
файлів. Виведені дані можуть бути різними навіть для різних збірок тієї
самої версії XZ Utils, якщо використано різні параметри збирання.
.PP
Написане вище означає, що після реалізації \fB\-\-rsyncable\fP файли\-результати
не обов'язково можна буде синхронізувати за допомогою rsyncable, якщо старий
і новий файли було стиснено за допомогою тієї самої версії xz. Цю проблему
можна усунути, якщо буде заморожено частину реалізації кодувальника, щоб
введені для rsync дані були стабільними між версіями xz.
.
.SS "Вбудовані розпакувальники .xz"
У вбудованих реалізаціях розпакувальника \fB.xz\fP, подібних до XZ Embedded, не
обов'язково передбачено підтримку файлів, які створено із типами
\fIперевірки\fP цілісності, відмінними від \fBnone\fP і \fBcrc32\fP. Оскільки типовим
є \fB\-\-check=crc64\fP, вам слід використовувати  \fB\-\-check=none\fP або
\fB\-\-check=crc32\fP при створенні файлів для вбудованих систем.
.PP
Поза вбудованими системами, в усіх засобах розпаковування формату \fB.xz\fP
передбачено підтримку усіх типів \fIперевірок\fP або принаймні можливість
розпакувати файл без перевірки цілісності, якщо підтримки певної
\fIперевірки\fP не передбачено.
.PP
У XZ Embedded передбачено підтримку BCJ, але лише з типовим початковим
зсувом.
.
.SH ПРИКЛАДИ
.
.SS Основи
Стиснути файл \fIfoo\fP до \fIfoo.xz\fP за допомогою типового рівня стискання
(\fB\-6\fP) і вилучити \fIfoo\fP, якщо стискання відбулося успішно:
.RS
.PP
.nf
\f(CWxz foo\fP
.fi
.RE
.PP
Розпакувати \fIbar.xz\fP до \fIbar\fP і не вилучати \fIbar.xz\fP, навіть якщо
розпаковування відбулося успішно:
.RS
.PP
.nf
\f(CWxz \-dk bar.xz\fP
.fi
.RE
.PP
Створити \fIbaz.tar.xz\fP з використанням шаблона \fB\-4e\fP (\fB\-4 \-\-extreme\fP),
який є повільнішими за типовий \fB\-6\fP, але потребує менше пам'яті для
стискання та розпаковування (48\ МіБ та 5\ МіБ, відповідно):
.RS
.PP
.nf
\f(CWtar cf \- baz | xz \-4e > baz.tar.xz\fP
.fi
.RE
.PP
Суміш стиснених і нестиснених файлів можна розпакувати до стандартного
виведення за допомогою єдиної команди:
.RS
.PP
.nf
\f(CWxz \-dcf a.txt b.txt.xz c.txt d.txt.lzma > abcd.txt\fP
.fi
.RE
.
.SS "Паралельне стискання багатьох файлів"
У GNU і *BSD можна скористатися \fBfind\fP(1) і \fBxargs\fP(1) для паралельного
стискання багатьох файлів:
.RS
.PP
.nf
\f(CWfind . \-type f \e! \-name '*.xz' \-print0 \e     | xargs \-0r \-P4 \-n16 xz \-T1\fP
.fi
.RE
.PP
Параметр \fB\-P\fP \fBxargs\fP(1) встановлює кількість паралельних процесів
\fBxz\fP. Найкраще значення параметра \fB\-n\fP залежить від того, скільки файлів
має бути стиснено. Якщо файлів мало, значенням, ймовірно, має бути 1. Якщо
файлів десятки тисяч, може знадобитися значення 100 або навіть більше, щоб
зменшити кількість процесів \fBxz\fP, які врешті створить \fBxargs\fP(1).
.PP
Параметр \fB\-T1\fP для \fBxz\fP тут для примусового встановлення однопотокового
режиму, оскільки для керування рівнем паралелізації використано \fBxargs\fP(1).
.
.SS "Режим робота"
Обчислити скільки байтів було заощаджено загалом після стискання декількох
файлів:
.RS
.PP
.nf
\f(CWxz \-\-robot \-\-list *.xz | awk '/^totals/{print $5\-$4}'\fP
.fi
.RE
.PP
Скрипту можуть знадобитися дані щодо того, що використано достатньо нову
версію \fBxz\fP. У наведеному нижче скрипті \fBsh\fP(1) виконано перевірку того,
що номер версії засобу \fBxz\fP є принаймні рівним 5.0.0. Цей спосіб є сумісним
зі старими тестовими версіями, де не передбачено підтримки параметра
\fB\-\-robot\fP:
.RS
.PP
.nf
\f(CWif ! eval "$(xz \-\-robot \-\-version 2> /dev/null)" ||         [ "$XZ_VERSION" \-lt 50000002 ]; then     echo "Your xz is too old." fi unset XZ_VERSION LIBLZMA_VERSION\fP
.fi
.RE
.PP
Встановити обмеження на використання пам'яті для розпаковування за допомогою
\fBXZ_OPT\fP, але якщо обмеження вже було встановлено, не збільшувати його:
.RS
.PP
.nf
\f(CWNEWLIM=$((123 << 20))\ \ # 123 MiB OLDLIM=$(xz \-\-robot \-\-info\-memory | cut \-f3) if [ $OLDLIM \-eq 0 \-o $OLDLIM \-gt $NEWLIM ]; then     XZ_OPT="$XZ_OPT \-\-memlimit\-decompress=$NEWLIM"     export XZ_OPT fi\fP
.fi
.RE
.
.SS "Нетипові ланцюжки фільтрів засобу стискання"
Найпростішим використанням ланцюжка фільтрів є налаштовування шаблона
LZMA2. Це може бути корисним, оскільки у шаблонах використано лише
підмножину потенційно корисних комбінацій параметрів стискання.
.PP
При налаштовуванні шаблонів LZMA2 корисними є стовпчики CompCPU таблиць з
описів параметрів \fB\-0\fP ... \fB\-9\fP і \fB\-\-extreme\fP. Ось відповідні частини з
цих двох таблиць:
.RS
.PP
.TS
tab(;);
c c
n n.
Шаблон;CompCPU
\-0;0
\-1;1
\-2;2
\-3;3
\-4;4
\-5;5
\-6;6
\-5e;7
\-6e;8
.TE
.RE
.PP
Якщо вам відомо, що певний файл потребує дещо більшого словника (наприклад,
32\ МіБ) для якісного стискання, але ви хочете стиснути його швидше за
команду \fBxz \-8\fP, можна внести зміни до шаблона із нижчим значенням CompCPU
(наприклад, 1) для використання більшого словника:
.RS
.PP
.nf
\f(CWxz \-\-lzma2=preset=1,dict=32MiB foo.tar\fP
.fi
.RE
.PP
Для певних файлів наведена вище команда може працювати швидше за \fBxz \-6\fP і
стискати дані значно краще. Втім, слід наголосити, переваги більшого
словника з одночасним низьким значенням CompCPU проявляються лише для деяких
файлів. Найочевиднішим випадком, коли великий словник є корисним, є випадок,
коли архів містить дуже подібні файли розміром у принаймні декілька
мегабайтів. Розмір словника має бути значно більшим за будь\-який окремий
файл, щоб у LZMA2 було використано усі переваги подібностей між послідовними
файлами.
.PP
Якщо дуже високий рівень використання пам'яті у засобі стискання або
розпаковування не є проблемою, і файли, який стискають має об'єм у принаймні
декілька десятків мегабайтів, може бути корисним використання навіть
більшого за 64 МіБ словника, який використано у \fBxz \-9\fP:
.RS
.PP
.nf
\f(CWxz \-vv \-\-lzma2=dict=192MiB big_foo.tar\fP
.fi
.RE
.PP
Використання \fB\-vv\fP (\fB\-\-verbose \-\-verbose\fP), подібно до наведеного вище
прикладу, може бути корисним для перегляду вимог з боку засобів стискання та
розпаковування до пам'яті. Пам'ятайте, що використання словника, розмір
якого перевищує розмір файла, який стискають, є простоюю витратою пам'яті,
отже наведену вище команду не варто використовувати для малих файлів.
.PP
Іноді час стискання не має значення, але використання пам'яті засобом
розпаковування має бути низьким для того, щоб, наприклад, уможливити
розпаковування файла у вбудованій системі. У наведеній нижче команді
використано \fB\-6e\fP (\fB\-6 \-\-extreme\fP) як основу і встановлено розмір словника
лише у 64\ КіБ. Файл\-результат можна розпакувати за допомогою XZ Embedded
(ось чому використано \fB\-\-check=crc32\fP) з використанням лише 100\ КіБ
пам'яті.
.RS
.PP
.nf
\f(CWxz \-\-check=crc32 \-\-lzma2=preset=6e,dict=64KiB foo\fP
.fi
.RE
.PP
Якщо вам потрібно витиснути зі стискання максимальну кількість байтів, може
допомогти коригування кількості бітів контексту літералів (\fIlc\fP) та
кількість позиційних бітів (\fIpb\fP). Також може допомогти коригування
кількості бітів позиції літералів (\fIlp\fP), але, зазвичай, важливішими є
\fIlc\fP і \fIpb\fP. Наприклад, в архівах зі початковим кодом міститься
здебільшого текст US\-ASCII, щось подібне до наведеного нижче може дещо (на
щось близьке до 0,1\ %) зменшити файл, порівняно із \fBxz \-6e\fP (спробуйте
також без \fBlc=4\fP):
.RS
.PP
.nf
\f(CWxz \-\-lzma2=preset=6e,pb=0,lc=4 source_code.tar\fP
.fi
.RE
.PP
Використання іншого фільтра разом із LZMA2 може покращити стискання для
певних типів файлів. Наприклад, для стискання бібліотеки спільного
користування x86\-32 або x86\-64 з використанням фільтра BCJ x86 скористайтеся
такою командою:
.RS
.PP
.nf
\f(CWxz \-\-x86 \-\-lzma2 libfoo.so\fP
.fi
.RE
.PP
Зауважте, що порядок параметрів фільтрування має значення. Якщо \fB\-\-x86\fP
вказано після \fB\-\-lzma2\fP, \fBxz\fP повідомить про помилку, оскільки після LZMA2
не може бути жодного фільтра, а також оскільки фільтр BCJ x86 не можна
використовувати як останній фільтр у ланцюжку.
.PP
Фільтр Delta разом із LZMA2 може дати добрі результати для растрових
зображень. Зазвичай, результати є кращими за формат PNG, у якого є декілька
більш досконалих фільтрів, ніж проста дельта, але там використовують для
стискання Deflate.
.PP
Зображення слід берегти у нестисненому форматі, наприклад, як нестиснений
TIFF. Параметр відстані фільтра Delta встановлюють так, щоб він збігався із
кількістю байтів на піксель у зображенні. Наприклад, для 24\-бітового
растрового зображення RGB слід вказати \fBdist=3\fP, а також добре передати
\fBpb=0\fP до LZMA2 для пристосовування до трибайтового вирівнювання:
.RS
.PP
.nf
\f(CWxz \-\-delta=dist=3 \-\-lzma2=pb=0 foo.tiff\fP
.fi
.RE
.PP
Якщо в один архів запаковано декілька зображень (наприклад, в архів
\&\fB.tar\fP), фільтр Delta також даватиме добрі результати, якщо у всіх
зображеннях однакова кількість байтів для кожного пікселя.
.
.SH "ДИВ. ТАКОЖ"
\fBxzdec\fP(1), \fBxzdiff\fP(1), \fBxzgrep\fP(1), \fBxzless\fP(1), \fBxzmore\fP(1),
\fBgzip\fP(1), \fBbzip2\fP(1), \fB7z\fP(1)
.PP
XZ Utils: <https://tukaani.org/xz/>
.br
Вбудовуваний XZ: <https://tukaani.org/xz/embedded.html>
.br
LZMA SDK: <https://7\-zip.org/sdk.html>
