.\"
.\" Authors: <AUTHORS>
.\"          <PERSON><PERSON>
.\"
.\" This file has been put into the public domain.
.\" You can do whatever you want with this file.
.\"
.\" (Note that this file is not based on gzip's zless.1.)
.\"
.\"*******************************************************************
.\"
.\" This file was generated with po4a. Translate the source file.
.\"
.\"*******************************************************************
.TH XZLESS 1 "27 вересня 2010 року" Tukaani "XZ Utils"
.SH НАЗВА
xzless, lzless — перегляд стиснених xz або lzma (текстових) фай<PERSON>
.SH "КОРОТКИЙ ОПИС"
\fBxzless\fP [\fIфайл\fP...]
.br
\fBlzless\fP [\fIфайл\fP...]
.SH ОПИС
\fBxzless\fP є фільтром, який показує текст зі стиснених файлів у
терміналі. Працює для файлів, які стиснуто за допомогою \fBxz\fP(1) або
\fBlzma\fP(1). Якщо не вказано жодного \fIфайла\fP, \fBxzless\fP читатиме дані зі
стандартного джерела вхідних даних.
.PP
Для показу виведених даних \fBxzless\fP використовує \fBless\fP(1). На відміну від
\fBxzmore\fP, вибір програми для поділу на сторінки не можна змінити за
допомогою змінної середовища. Команди засновано на \fBmore\fP(1) і \fBvi\fP(1). За
допомогою команд можна просуватися назад і вперед даними та шукати дані. Щоб
дізнатися більше, ознайомтеся із підручником з \fBless\fP(1).
.PP
Команду \fBlzless\fP реалізовано для забезпечення зворотної сумісності з LZMA
Utils.
.SH СЕРЕДОВИЩЕ
.TP 
\fBLESSMETACHARS\fP
Список символів, які є особливими символами командної
оболонки. Встановлюється \fBxzless\fP, якщо його ще не встановлено у
середовищі.
.TP 
\fBLESSOPEN\fP
Має значення рядка команди для виклику засобу розпаковування \fBxz\fP(1) для
обробки вхідних файлів \fBless\fP(1).
.SH "ДИВ. ТАКОЖ"
\fBless\fP(1), \fBxz\fP(1), \fBxzmore\fP(1), \fBzless\fP(1)
