.\"
.\" Original zdiff.1 for gzip: <PERSON><PERSON><PERSON><PERSON>
.\" Modifications for XZ Utils: <PERSON><PERSON>
.\"
.\" License: GNU GPLv2+
.\"
.\"*******************************************************************
.\"
.\" This file was generated with po4a. Translate the source file.
.\"
.\"*******************************************************************
.TH XZMORE 1 "30 червня 2013 року" Tukaani "XZ Utils"
.SH НАЗВА
xzmore, lzmore — перегляд стиснених xz або lzma (текстових) файлів
.SH "КОРОТКИЙ ОПИС"
\fBxzmore\fP [\fIфайл...\fP]
.br
\fBlzmore\fP [\fIфайл...\fP]
.SH ОПИС
\fBxzmore\fP є фільтром, за допомогою якого можна вивчати стиснені \fBxz\fP(1) або
\fBlzma\fP(1) текстові файли поекранно на терміналах із м'яким копіюванням.
.PP
Щоб скористатися засобом поділу на сторінки, відмінним від типового \fBmore\fP,
встановіть для змінної середовища \fBPAGER\fP значення назви бажаної
програми. Команду \fBlzmore\fP реалізовано для забезпечення зворотної
сумісності з LZMA Utils.
.TP 
\fBe\fP або \fBq\fP
Якщо виведено запит \-\-More\-\-(Наступний файл: \fIфайл\fP), ця команда наказує
\fBxzmore\fP завершити роботу.
.TP 
\fBs\fP
Якщо виведено запит \-\-More\-\-(Наступний файл: \fIфайл\fP), ця команда наказує
\fBxzmore\fP перейти до наступного файла і продовжити показ даних.
.PP
Список клавіатурних команд, якими можна скористатися під час перегляду
вмісту файла, наведено на сторінці засобу поділу файла на сторінки, яким ви
користуєтеся, зазвичай, на сторінці \fBmore\fP(1).
.SH "ДИВ. ТАКОЖ"
\fBmore\fP(1), \fBxz\fP(1), \fBxzless\fP(1), \fBzmore\fP(1)
