'\" t
.\"
.\" Authors: <AUTHORS>
.\"          <PERSON><PERSON>
.\"
.\" This file has been put into the public domain.
.\" You can do whatever you want with this file.
.\"
.\"*******************************************************************
.\"
.\" This file was generated with po4a. Translate the source file.
.\"
.\"*******************************************************************
.TH XZ 1 2023\-07\-17 Tukaani "XZ 유틸리티"
.
.SH 이름
xz, unxz, xzcat, lzma, unlzma, lzcat \- .xz 파일과 .lzma 파일을 압축 또는 압축 해제합니다
.
.SH 요약
\fBxz\fP [\f\fI옵션\fP...\fP] [\fI<파일>...\fP]
.
.SH "명령 별칭"
\fBunxz\fP 명령은 \fBxz \-\-decompress\fP 명령과 동일합니다.
.br
\fBxzcat\fP 명령은 \fBxz \-\-decompress \-\-stdout\fP 명령과 동일합니다.
.br
\fBlzma\fP 명령은 \fBxz \-\-format=lzma\fP 명령과 동일합니다.
.br
\fBunlzma\fP 명령은 \fBxz \-\-format=lzma \-\-decompress\fP 명령과 동일합니다.
.br
\fBlzcat\fP 명령은 \fBxz \-\-format=lzma \-\-decompress \-\-stdout\fP 명령과 동일합니다.
.PP
파일 압축을 해제해야 하는 셸 스크립트를 작성할 때, \fBunxz\fP 와 \fBxzcat\fP 이름 대신 \fBxz\fP 명령과 적절한 인자
값(\fBxz \-d\fP 또는 \fBxz \-dc\fP)의 사용을 추천드립니다.
.
.SH 설명
\fBxz\fP는 \fBgzip\fP(1)  과 \fBbzip2\fP(1)  과 비슷한 명령행 문법을 지닌 범용 데이터 압축 도구입니다.  자체 파일
형식은 \fB.xz\fP 형식이나, LZMA 유틸리티에서 사용하는 예전 \fB.lzma\fP 형식과 형식 헤더가 없는 RAW 압축 스트림도
지원합니다.  게다가, \fBlzip\fP에서 활용하는 \fB.lz\fP 형식 압축 해제도 지원합니다.
.PP
각 \fI파일\fP 에 대한 \fBxz\fP 압축 또는 압축 해제는 선택 동작 모드에 따릅니다.  \fI<파일>\fP 값이 주어졌거나
\fI<파일>\fP 값이 \fB\-\fP이면, \fBxz\fP 명령에서 표준 입력을 읽고 처리한 데이터를 표준 출력에 기록합니다.
\fBxz\fP 에서는 터미널에서 활용할 경우 압축 데이터를 표준 압축으로 기록하는 동작을 거절(오류를 출력하고 \fI<파일>\fP을
건너뜀)합니다.  이와 비슷하게, \fBxz\fP 유틸리티를 터미널에서 실행하면 표준 입력의 압축 데이터 읽기를 거절합니다.
.PP
\fB\-\-stdout\fP 을 지정하지 않는 한, \fB\-\fP가 아닌 \fI<파일>\fP을 원본 \fI<파일>\fP 이름에서
가져온 새 파일 이름으로 기록합니다:
.IP \(bu 3
압축할 때, 대상 파일 형식의 접미사(\fB.xz\fP or \fB.lzma\fP)  는 원본 파일 이름 뒤에 붙어 대상 파일이름이 됩니다.
.IP \(bu 3
압축 해제할 때, \fB.xz\fP, \fB.lzma\fP, \fB.lz\fP 접미사를 파일 이름에서 제거하고 대상 파일 이름을 알아냅니다.
\fBxz\fP에서는 \fB.txz\fP, \fB.tlz\fP 접미사도 인식하며, \fB.tar\fP 접미사로 치환합니다.
.PP
대상 파일이 이미 있으면, 오류를 나타내고 \fI<파일>\fP을 건너뜁니다.
.PP
표준 출력으로 기록하기 전에는, \fBxz\fP는 경고를 나타내며, 다음 조건에 만족할 경우 \fI<파일>\fP을 건너뜁니다:
.IP \(bu 3
\fI<파일>\fP이 일반 파일이 아닐 때.  심볼릭 링크는 따라가지 않기에, 일반 파일로 간주하지 않습니다.
.IP \(bu 3
\fI<파일>\fP이 하나 이상의 하드 링크일 떄.
.IP \(bu 3
\fI<파일>\fP에 setuid, setgid, 끈적이 비트 집합이 붙어있을 떄.
.IP \(bu 3
동작 모드를 압축으로 설정하고, \fI<파일>\fP은 대상 파일 형식의 접미사를 이미 붙였을 때(\fB.xz\fP 형식으로 압축하면
\&\fB.xz\fP 또는 \fB.txz\fP, \fB.lzma\fP 형식으로 압축하면 \fB.lzma\fP 또는 \fB.tlz\fP).
.IP \(bu 3
동작 모드를 압축 해제로 설정하고, \fI<파일>\fP에 지원 파일 형식 접미사(\fB.xz\fP, \fB.txz\fP, \fB.lzma\fP,
\&\fB.tlz\fP, \fB.lz\fP)를 붙이지 않았을 때.
.PP
\fI<파일>\fP 의 압축 또는 압축 해제를 성공하고 나면, \fBxz\fP는 소유자, 소유그룹, 권한, 접근 시각, 수정 시각
정보를 원본 \fI<파일>\fP에서 대상 파일로 그대로 복사합니다.  그룹 정보 복사에 실패하면, 권한을 수정하여 원본
\fI<파일>\fP에 접근 권한이 없는 사용자가 대상 파일로 접근하지 못하게 합니다.  \fBxz\fP는 아직 접근 제어 목록이나
확장 속성 등의 기타 메타데이터를 복사하는 기능은 지원하지 않습니다.
.PP
대상 파일을 온전히 닫고 나면, \fB\-\-keep\fP 옵션을 지원하지 않았을 경우 원본 \fI<파일>\fP을 제거합니다.  원본
\fI<파일>\fP은 출력을 표준 출력으로 기록했거나 오류가 발생했을 경우 제거하지 않습니다.
.PP
\fBxz\fP 프로세스에 \fBSIGINFO\fP 시그널 또는 \fBSIGUSR1\fP 시그널을 보내면 표준 출력으로 진행 정보를 출력합니다.  표준
오류가 터미널일 경우일 경우에만 제한하며 \fB\-\-verbose\fP 옵션을 지정하면 진행 표시줄을 자동으로 나타냅니다.
.
.SS "메모리 사용"
\fBxz\fP 메모리 사용은 수백 킬로바이트로 시작하여 수 기가바이트까지 압축 설정에 따라 다릅니다.  압축 해제 프로그램이 필요로 하는
메모리 공간을 결정하는 파일 압축시에 설정 값을 활용합니다.  보통 압축 해제 프로그램은 파일을 만들 때, 압축 프로그램 메모리 사용량의
5% 에서 20% 정도 필요합니다.  예를 들면, \fBxz \-9\fP로 압축한 파일 압축 해제시 현재 65MiB 메모리 용량이 필요합니다.
여전하게도, 압축 해제시 수 기가 바이트의 메모리가 필요한 \fB.xz\fP 파일에도 가능한 이야기입니다.
.PP
특히 이전 시스템 사용자의 경우 메모리 사용량이 엄청나게 늘어나는 점에 짜증이 날 수 있습니다.  이런 불편한 상황을 피하기 위해,
\fBxz\fP에 기본적으로 비활성 상태인 내장 메모리 사용 제한 기능을 넣었습니다.  일부 운영체제에서 처리 중 메모리 사용을 제한하는
수단을 제공하긴 하지만, 여기에 의지하기에는 충분히 유연하지 않습니다(예를 들면, \fBulimit\fP(1)을 사용하면 가상 메모리를
제한하여  \fBmmap\fP(2)을 먹통으로 만듭니다).
.PP
메모리 사용 제한 기능은 \fB\-\-memlimit=\fP\fI<제한용량>\fP 명령행 옵션으로 사용할 수 있습니다.  종종
\fBXZ_DEFAULTS=\-\-memlimit=150MiB\fP와 같이 \fBXZ_DEFAULTS\fP 환경 변수를 설정하여 제한 기능을 켜는게 더
편합니다.  \fB\-\-memlimit\-compress=\fP\fI<제한용량>\fP 옵션과
\fB\-\-memlimit\-decompress=\fP\fI<제한용량>\fP 옵션을 활용하여 압축 및 압축 해제시 별도로 한계 값을
설정할 수 있습니다.  이 두 가지 옵션의 \fBXZ_DEFAULTS\fP 환경 변수 밖에서의 사용은, \fBxz\fP를 단일 실행할 때 압축 및
압축 해제 동작을 동시에 수행하지 않으며, 앞서 언급한 두가지 옵션을 명령행에 입력하기에는
\fB\-\-memlimit=\fP\fI<제한용량>\fP(또는 \fB\-M\fP \fI<제한용량>\fP)이 더 짧기 때문에 별로 쓸모가
없습니다.
.PP
압축 해제시 메모리 사용 제한 지정 한계를 초과하면, \fBxz\fP 유틸리티에서 오류를 나타내며 파일 압축 해제는 실패합니다.  압축을
실행할 때 사용 제한 지정 한계를 넘어서면 \fBxz\fP에서는 설정 값을 줄여서 어쨌든 한계를 넘지 못하게
합니다(\fB\-\-format=raw\fP 옵션 또는 \fB\-\-no\-adjust\fP 옵션 사용시 제외).  설정 한계 값이 엄청 작지 않은 이상 이
방식대로 처리하면 어쨌든 실패하지 않습니다.  설정 값조정은 압축 래벨 사전 설정과 일치하지 않을 때 단계적으로 진행하는데, 이를테면,
\fBxz \-9\fP 명령 수행에 필요한 양보다 한계 값이 약간 작으면, 설정 값을 \fBxz \-8\fP에 못미치게 약간 줄여서 진행합니다.
.
.SS ".xz 파일 결합 및 패딩"
\&\fB.xz\fP 파일을 있는 그대로 합칠 수 있습니다.  \fBxz\fP는 \fB.xz\fP 파일을 단독 파일일 때 처럼 압축해제합니다.
.PP
결합 부분과 마지막 부분 뒤에 패딩을 추가할 수 있습니다.  패딩은 널 바이트로 구성해야 하며 패딩 길이는 4바이트로 구성해야 합니다.
512 바이트 블록으로 파일 크기를 이루는 매체에 \fB.xz\fP 파일을 저장했을 경우에 요긴할 수 있습니다.
.PP
\&\fB.lzma\fP 파일 또는 원시 스트림의 경우 결합과 패딩을 허용하지 않습니다.
.
.SH 옵션
.
.SS "정수 접두사와 별도 값"
정수 인자값이 필요한 대부분 위치에서는, 큰 정수값을 나타내기 쉽게 하도록 추가 접미사를 지원합니다.  정수와 접미사 사이에 어떤 공백이
있으면 안됩니다.
.TP 
\fBKiB\fP
1,024 (2^10) 배수 정수값. \fBKi\fP, \fBk\fP, \fBkB\fP, \fBK\fP, \fBKB\fP 단위를 \fBKiB\fP 동의어로 받아들입니다.
.TP 
\fBMiB\fP
1,048,576 (2^20) 배수 정수값. \fBMi\fP, \fBm\fP, \fBM\fP, \fBMB\fP 단위를 \fBMiB\fP 동의어로 받아들입니다.
.TP 
\fBGiB\fP
1,073,741,824 (2^30) 배수 정수값.  \fBGi\fP, \fBg\fP, \fBG\fP, \fBGB\fP 단위를 \fBGiB\fP 동의어로
받아들입니다.
.PP
특수 값 \fBmax\fP는 옵션에서 지원하는 정수 최대 값을 나타낼 때 사용할 수 있습니다.
.
.SS "동작 모드"
여러 동작 모드를 보여드리겠습니다만, 마지막에 주어진 동작 모드로 동작합니다.
.TP 
\fB\-z\fP, \fB\-\-compress\fP
압축합니다.  어떤 동작 모드 옵션도 지정하지 않고 다른 동작 모드를 명령행에 따로 지정하지 않았다면 이 동작 모드는 기본입니다(예:
\fBunxz\fP 는 \fB\-\-decompress\fP를 암시).
.TP 
\fB\-d\fP, \fB\-\-decompress\fP, \fB\-\-uncompress\fP
압축을 해제합니다.
.TP 
\fB\-t\fP, \fB\-\-test\fP
압축 \fI<파일>\fP의 무결성을 시험해봅니다.  이 옵션은 압축 해제 데이터를 표준 출력으로 기록하는 대신 버린다는 점을
제외하고 \fB\-\-decompress \-\-stdout\fP과 동일합니다.  어떤 파일도 만들거나 제거하지 않습니다.
.TP 
\fB\-l\fP, \fB\-\-list\fP
압축 \fI<파일>\fP 정보를 출력합니다.  압축 해제 출력을 내보내지 않으며, 어떤 파일도 만들거나 제거하지 않습니다.  이
조회 모드에서, 프로그램은 표준 입력 또는 기타 탐색 불가능한 원본에서 압축 데이터를 읽을 수 없습니다.
.IP ""
\fI<파일>\fP 기본 정보를 파일 당 한 줄 씩 기본으로 보여줍니다.  더 자세한 정보를 보려면 \fB\-\-verbose\fP
옵션을 사용하십시오.  더 자세한 정보는 \fB\-\-verbose\fP 옵션을 두번 사용하면 되지만, 추가 정보를 더 많이 가져오면서 탐색
횟수가 늘어나는 문제로 인해 느려질 수 있습니다.  세부 출력 너비는 80 문자를 초과하며, 예를 들어 출력을 파이핑한다면, 터미널이
충분히 너비가 넓지 못할 경우 \fBless\ \-S\fP 명령이 편리할 수 있습니다.
.IP ""
정확한 출력은 \fBxz\fP 버전과 다른 로캘에 따라 바뀔 수 있습니다.  기계 판독용 출력시 \fB\-\-robot \-\-list\fP 옵션을
사용합니다.
.
.SS "동작 수정자"
.TP 
\fB\-k\fP, \fB\-\-keep\fP
입력 파일을 삭제하지 않습니다.
.IP ""
\fBxz\fP 5.2.6 부터는 이 옵션으로 입력 파일이 일반 파일을 참조하는 심볼릭 링크나 하나 이상의 하드 링크, 내지는 setuid,
setgid, 끈적이 비트 세트를 설정한 상태라도 압축하거나 압축을 풀 수 있습니다. setuid, setgid, 끈적이 비트는 대상
파일에 복사하지 않습니다.  이전 버전에서는 \fB\-\-force\fP 옵션을 지정했을 때만 가능했습니다.
.TP 
\fB\-f\fP, \fB\-\-force\fP
이 옵션은 몇가지 동작에 영향을 줍니다:
.RS
.IP \(bu 3
대상 파일이 이미 있으면, 압축 또는 압축 해제 전 삭제합니다.
.IP \(bu 3
입력 파일이 일반 파일을 참조하는 심볼릭 링크나 하나 이상의 하드 링크, 내지는 setuid, setgid, 끈적이 비트 세트를 설정한
상태라도 압축 또는 압축 해제를 진행합니다.  setuid, setgid, 끈적이 비트는 대상 파일에 복사하지 않습니다
.IP \(bu 3
\fB\-\-decompress\fP \fB\-\-stdout\fP 옵션을 같이 사용하는 상황에서 \fBxz\fP 명령이 원본 파일의 형식을 알아내지 못할 때,
원본 파일의 사본을 표준 출력으로 보냅니다.  이렇게 하면 \fBxzcat\fP \fB\-\-force\fP 명령을 \fBxz\fP 명령으로 압축하지 않은
파일에 대해 \fBcat\fP(1) 을 사용하는 것처럼 사용할 수 있습니다.  참고로 나중에, \fBxz\fP에서 \fBxz\fP로 하여금 여러 형식의
파일을 표준 출력으로 복사하는 대신 압축을 해제하도록 새 압축 파일 형식을 지원할 예정입니다.
\fB\-\-format=\fP\fI<형식>\fP 옵션은 \fBxz\fP 명령에 단일 파일 형식만 압축 해제하도록 제한할 때 사용할 수
있습니다.
.RE
.TP 
\fB\-c\fP, \fB\-\-stdout\fP, \fB\-\-to\-stdout\fP
파일 대신 표준 출력으로 압축 또는 압축 해제한 데이터를 기록합니다.  \fB\-\-keep\fP를 생략했습니다.
.TP 
\fB\-\-single\-stream\fP
처음 \fB.xz\fP 스트림만 압축 해제하며, 스트림에 뒤따라오는 나머지 입력 데이터는 조용히 무시합니다.  보통 뒤따라오는 쓰레기 값에
대해서는 \fBxz\fP 에서 오류를 나타냅니다.
.IP ""
\fBxz\fP는 \fB.lzma\fP 파일 또는 원시 스트림에서 온 하나 이상의 스트림에 대해 압축 해제동작을 취하지 않지만, 이 옵션을 사용하면
\fBxz\fP에서 \fB.lzma\fP 파일 또는 원시 스트림을 처리한 다음에 뒤따라오는 데이터를 무시하도록 합니다.
.IP ""
이 옵션은 동작 모드가 \fB\-\-decompress\fP 또는 \fB\-\-test\fP가 아니면 동작에 아무런 영향을 주지 않습니다.
.TP 
\fB\-\-no\-sparse\fP
희소 파일을 만들지 않습니다.  기본적으로 일반 파일로 압축 해제할 경우 \fBxz\fP 에서는 압축 해제한 파일에 이진 0값이 길게 늘어질
경우 희소 배열 파일을 만들려고 합니다.  표준 출력의 내용 길이만큼 연결한 일반 파일로 기록할 때도 동작하며 희소 파일을 만드는 동안
아무런 문제가 나타나지 않게 각각의 추가 조건을 만족합니다.  희소 파일을 만들면 디스크 공간을 절약할 수 있으며 디스크 입출력을 줄여
압축 해제 속도를 올릴 수 있습니다.
.TP 
\fB\-S\fP \fI.suf\fP, \fB\-\-suffix=\fP\fI.suf\fP
압축할 때, 대상 파일의 접두사를 \fB.xz\fP 또는 \fB.lzma\fP 대신 \fI.suf\fP로 사용하십시오.  표준 출력으로 기록하지 않고
원본 파일에 \fI.suf\fP 접두사가 붙어있으면, 경고를 나타내고 해당 파일을 건너뜁니다.
.IP ""
압축 해제할 때, \fI.suf\fP 접미사로 파일을 인식하기도 하고, \fB.xz\fP, \fB.txz\fP, \fB.lzma\fP, \fB.tlz\fP,
\&\fB.lz\fP 접미사가 붙은 파일도 인식합니다.  원본 파일에 \fI.suf\fP 접미사가 붙어있으면, 해당 접미사를 제거하여 대상 파일 이름을
알아냅니다.
.IP ""
원시 스트림 압축 및 압축 해제시(\fB\-\-format=raw\fP) 원시 스트림에 기본 접미사가 없기 때문에, 표준 출력으로 기록하지 않는
한 접미사를 반드시 지정해야 합니다.
.TP 
\fB\-\-files\fP[\fB=\fP\fI<파일>\fP]
\fI<파일>\fP에서 처리할 파일 이름을 읽습니다. \fI<파일>\fP을 생략하면 파일 이름은 표준 입력에서
불러옵니다.  파일 이름은 개행 문자로 끝나야 합니다.  대시 문자(\fB\-\fP)는 일반 파일 이름으로 취급하며 표준 입력을 의미하지
않습니다.  파일 이름을 명령행 인자로 지정하면, \fI<파일>\fP에서 파일 이름을 읽어들이기 전 해당 명령행 인자를 먼저
처리합니다.
.TP 
\fB\-\-files0\fP[\fB=\fP\fI<파일>\fP]
각 파일 이름이 널 문자로 끝나야 한다는 점만 제외하면 \fB\-\-files\fP[\fB=\fP\fI<파일>\fP] 옵션과 동일합니다.
.
.SS "기본 파일 형식 및 압축 옵션"
.TP 
\fB\-F\fP \fIformat\fP, \fB\-\-format=\fP\fI<형식>\fP
압축 또는 압축해제 파일 \fI<형식>\fP을 지정합니다:
.RS
.TP 
\fBauto\fP
기본 값입니다.  압축할 때, \fBauto\fP는 \fBxz\fP의 기본 동작과 동일합니다.  압축을 해제할 때, 입력 파일 형식을 자동으로
찾습니다.  참고로 원시 스트림(\fB\-\-format=raw\fP)의 경우 자동으로 찾을 수 없습니다.
.TP 
\fBxz\fP
\&\fB.xz\fP 파일 형식으로 압축하거나, 압축 해제시 \fB.xz\fP 파일만 받아들입니다.
.TP 
\fBlzma\fP, \fBalone\fP
이전 \fB.lzma\fP 파일 형식으로 압축하거나, 압축 해제시 \fB.lzma\fP 파일만 받아들입니다.  \fBalone\fP 대체 명령은 LZMA
유틸리티 하위 호환성을 목적으로 제공합니다.
.TP 
\fBlzip\fP
압축 해제시 \fB.lz\fP 파일만 받아들입니다.  압축은 지원하지 않습니다.
.IP ""
\&\fB.lz\fP 형식 버전 0과 비확장 버전 1을 지원합니다.  버전 0파일은 \fBlzip\fP 1.3 이전에서만 만듭니다.  일반적이진 않지만
일부 파일의 경우 이 형식과 관련된 원본 패키지로 보관한 파일을 찾을 수도 있습니다.  개인적으로 이 형식으로 압축한 오래된 개인 파일을
가지고 있을 수도 있습니다.  형식 버전 0 압축 해제 지원은 \fBlzip\fP 1.18에서 제거했습니다.
.IP ""
\fBlzip\fP 1.4 이상에서는 버전 1형식의 파일을 만듭니다.  형식 버전 1로의 동기화 제거 마커 확장은 \fBlzip\fP 1.6에
추가했습니다.  이 확장은 거의 쓰지 않으며 \fBxz\fP 에서 조차도 지원하지 않습니다(손상된 입력 파일로 진단함).
.TP 
\fBraw\fP
원시 스트림으로 압축하거나 압축을 해제합니다(헤더 없음).  고급 사용자 전용입니다.  원시 스트림을 디코딩하려면,
\fB\-\-format=raw\fP 옵션을 사용하고 분명하게 필터 체인을 지정하여 컨테이너 헤더에 필요한 정보를 저장하게 끔 해야합니다.
.RE
.TP 
\fB\-C\fP \fI<검사방식>\fP, \fB\-\-check=\fP\fI<검사방식>\fP
무결성 검사 방식을 지정합니다.  검사 방식은 \fB.xz\fP 파일에 저장하며 압축 해제 데이터를 계산합니다.  이 옵션은 \fB.xz\fP
형식으로 압축할 때만 효력이 있습니다: \fB.lzma\fP 형식은 무결성 겁사를 지원하지 않습니다.  무결성 검사는 \fB.xz\fP 파일 압축을
풀었을 때에 검사합니다.
.IP ""
지원 \fI검사\fP 형식:
.RS
.TP 
\fBnone\fP
어떤 경우에도 무결성 검사 계산을 수행하지 않습니다.  보통 바람직하지 못한 생각입니다.  데이터 무결성을 다른 방식으로라도 검증해야
하는 상황이면 쓸만할 수 있습니다.
.TP 
\fBcrc32\fP
IEEE\-802.3 (이더넷)의 다항 연산으로 CRC32를 계산합니다.
.TP 
\fBcrc64\fP
ECMA\-182의 다항식 연산으로 CRC64를 계산합니다.  이 동작이 기본 동작이기 때문에 CRC32가 깨진 파일을 찾을 때보다는 좀
낮은 편이며 속도 차이도 거의 없습니다.
.TP 
\fBsha256\fP
SHA\-256 해시를 계산합니다.  CRC32와 CRC64 보다는 좀 느립니다.
.RE
.IP ""
\&\fB.xz\fP 헤더 무결성은 항상 CRC32로 검증하빈다.  이를 바꾸거나 It is not possible to change or
disable it.
.TP 
\fB\-\-ignore\-check\fP
압축 데이터를 압축해제할 경우 압축 데이터의 무결성 검증을 진행하지 않습니다.  \fB.xz\fP 헤더의 CRC32 값은 그래도 여전히 보통
방식으로 검증합니다.
.IP ""
\fB이 옵션이 정확히 무슨 동작을 하는지 알기 전에는 사용하지 마십시오.\fP 이 옵션을 사용하는 타당한 이유로:
.RS
.IP \(bu 3
깨진 .xz 파일에서 데이터 복구를 시도합니다.
.IP \(bu 3
압축 해제 속도를 늘립니다.  SHA\-256 또는 압축 파일에 들어간 그 무언가를 엄청 빨리 처리합니다.  다른 방식으로 파일 무결성을
검증해야 하는 목적이 아니라면 이 옵션을 사용하지 않는게 좋습니다.
.RE
.TP 
\fB\-0\fP ... \fB\-9\fP
압축 사전 설정 수준을 선택합니다.  기본값은 \fB\-6\fP입니다.  다중 수준을 지정하면 가장 마지막 수준 옵션을 적용합니다.  개별 필터
체인을 이미 지정했다면, 압축 사전 설정 수준 값을 설정할 때 개별 필터 체인을 정리합니다.
.IP ""
사전 설정간 차이는 \fBgzip\fP(1)과 \fBbzip2\fP(1)을 사용할 때보다 더 비중을 차지합니다.  선택한 압축 설정은 압축 해제시
필요한 메모리 사용량을 셜정하므로 사전 설정 수준 값을 너무 높게 지정하면 RAM 용량이 적은 오래된 시스템에서 파일 압축 해제시 실패할
수 있습니다.  게다가, \fBgzip\fP(1)  과 \fBbzip2\fP(1)에서 처럼 종종 \fB모든 동작에 \-9를 몰래 활용하는건 바람직하지 않습니다\fP.
.RS
.TP 
\fB\-0\fP ... \fB\-3\fP
동작이 빠른 사전 설정 부류입니다. \fB\-0\fP은 때로는 \fBgzip \-9\fP 명령보다 압축율이 훨씬 우수하면서도 더 빠릅니다.  더 큰
값은 보통 \fBbzip2\fP(1) 명령과 비교했을 떄 압축 결과가 압축 데이터에 따라 달라지더라도, 비교할 법한 속도 또는 더 나은
압축율을 보입니다.
.TP 
\fB\-4\fP ... \fB\-6\fP
오래된 시스템에서 조차도 압축 해제 프로그램의 적절한 메모리 사용량을 보이면서 양호하거나 최적의 압축율을 보여줍니다.  \fB\-6\fP 옵션은
압축 해제시 메모리 사용량이 16MiB 밖에 안되기 때문에 파일을 배포할 때 최적의 선택인 기본 값입니다.  (\fB\-5e\fP 또는
\fB\-6e\fP도 역시 고려할 만합니다.  \fB\-\-extreme\fP을 참고하십시오.)
.TP 
\fB\-7 ... \-9\fP
\fB\-6\fP과 비슷하지만 압축 및 압축 해제시 요구 메모리 사용량이 더 높습니다.  압축 파일이 각각 8MiB, 16MiB, 32MiB
보다 클 경우에만 쓸만한 옵션입니다.
.RE
.IP ""
동일한 하드웨어에서, 압축 해제 속도는 압축한 데이터의 초당 정적 바이트 처리 수의 어림 평균입니다.  다시 말해, 압축율을 더 올리면,
압축 해제 속도도 역시 올라갑니다.  이는 곧 초당 비압축 데이터 출력 양이 달라질 수 있단 뜻입니다.
.IP ""
다음 표에 사전 설정 기능을 정리했습니다:
.RS
.RS
.PP
.TS
tab(;);
c c c c c
n n n n n.
Preset;DictSize;CompCPU;CompMem;DecMem
\-0;256 KiB;0;3 MiB;1 MiB
\-1;1 MiB;1;9 MiB;2 MiB
\-2;2 MiB;2;17 MiB;3 MiB
\-3;4 MiB;3;32 MiB;5 MiB
\-4;4 MiB;4;48 MiB;5 MiB
\-5;8 MiB;5;94 MiB;9 MiB
\-6;8 MiB;6;94 MiB;9 MiB
\-7;16 MiB;6;186 MiB;17 MiB
\-8;32 MiB;6;370 MiB;33 MiB
\-9;64 MiB;6;674 MiB;65 MiB
.TE
.RE
.RE
.IP ""
컬럼 설명:
.RS
.IP \(bu 3
DictSize는 LZMA2 딕셔너리 크기입니다.  압축 해제 파일의 크기보다 딕셔너리에서 사용하는 낭비 메모리 용량입니다.  실제로
필요하지 않은 \fB\-7\fP ... \fB\-9\fP 사전 설정값을 피해야 하는 적절한 이유이기도 합니다.  \fB\-6\fP 이하에서는 소모 메모리 양이
충분히 적거나 따로 신경쓸 필요가 없습니다.
.IP \(bu 3
CompCPU는 압축 속도에 영향을 주는 LZMA2 설정의 단순화 표기 값입니다.  딕셔너리 크기는 속도에도 영향을 주기 때문에
CompCPU는 \fB\-6\fP ... \fB\-9\fP 수준값과 동일한데, 고수준 값은 여전히 조금 더 느려질 수 있습니다.  느려지는 만큼
압축율은 가능한 한 더 좋아집니다. \fB\-\-extreme\fP을 참고하십시오.
.IP \(bu 3
CompMem은 단일\-스레드 모드에서 필요한 압축 프로그램의 메모리 점유 용량입니다.  \fBxz\fP 버전에 따라 다를 수 있습니다.
앞으로 도입할 다중\-스레드 모드의 메모리 사용량은 단일\-스레드 모드에서의 그것보다는 훨씬 늘어납니다.
.IP \(bu 3
DecMem은 압축 해제 프로그램의 메모리 점유용량입니다.  이는 곧, 압축 해제 프로그램에서 필요한 메모리 사용량을 압축 설정에서
결정한다는 의미가 들어있습니다.  정확한 압축 해제 프로그램의 메모리 사용량은 LZMA2 딕셔너리 크기 보다는 조금 많지만 테이블의 값은
MiB 용량으로 완전히 반올림한 값입니다.
.RE
.TP 
\fB\-e\fP, \fB\-\-extreme\fP
기대하는 만큼의 좀 더 나은 압축율을 확보하려 선택한 압축 사전 설정 수준의 느린 변형 옵션을 사용하지만, 재수 없는 와중에 골로 가는
경우가 생기기도 합니다.  압축 해제 프로그램의 메모리 사용에는 영향을 주지 않지만, 압축 프로그램의 메모리 사용량은 \fB\-0\fP
\&... \fB\-3\fP 사전 설정 수준에서 약간 더 올라갈 뿐입니다.
.IP ""
4MiB와 8MiB 두 가지 딕셔너리 용량 설정이 있기 때문에 \fB\-3e\fP 와 \fB\-5e\fP 사전 설정을 (CompCPU 수치를 낮춰서)
각각 \fB\-4e\fP 와 \fB\-6e\fP 보다 약간 더 빠르게 설정할 수 있습니다.  이런 식으로 두 사전 설정이 동일하지 않습니다.
.RS
.RS
.PP
.TS
tab(;);
c c c c c
n n n n n.
Preset;DictSize;CompCPU;CompMem;DecMem
\-0e;256 KiB;8;4 MiB;1 MiB
\-1e;1 MiB;8;13 MiB;2 MiB
\-2e;2 MiB;8;25 MiB;3 MiB
\-3e;4 MiB;7;48 MiB;5 MiB
\-4e;4 MiB;8;48 MiB;5 MiB
\-5e;8 MiB;7;94 MiB;9 MiB
\-6e;8 MiB;8;94 MiB;9 MiB
\-7e;16 MiB;8;186 MiB;17 MiB
\-8e;32 MiB;8;370 MiB;33 MiB
\-9e;64 MiB;8;674 MiB;65 MiB
.TE
.RE
.RE
.IP ""
예를 들면, 8MiB 딕셔너리를 활용하는 네가지 사전 설정이 있다고 할 때, 빠른 순으로 설정을 나열하자면, \fB\-5\fP, \fB\-6\fP,
\fB\-5e\fP, \fB\-6e\fP 입니다.
.TP 
\fB\-\-fast\fP
.PD 0
.TP 
\fB\-\-best\fP
.PD
이 옵션은 \fB\-0\fP 과 \fB\-9\fP의 별칭으로 각각 오해할 수 있습니다. LZMA 유틸리티의 하위 호환성을 목적으로 제공합니다.  이
옵션 사용은 피하십시오.
.TP 
\fB\-\-block\-size=\fP\fI<크기>\fP
\&\fB.xz\fP 형식으로 압축할 때, 입력 데이터를 \fI<크기>\fP 바이트 블록으로 입력 데이터를 쪼갭니다.  각각의 블록은
다중\-스레드 방식으로 처리할 수 있고 임의 접근 압축 해제 가능성을 제한할 수 있게 개별적으로 압축 처리합니다.  이 옵션은 보통
다중\-스레드 모드에서 기본 블록 크기를 지정할 때 사용하지만, 단일\-스레드 모드에서도 사용할 수 있습니다.
.IP ""
다중\-스레드 모드에서는 약 3배 용량의 \fI<크기>\fP 바이트만큼 각 스레드 별로 입출력 버퍼링용 공간을 할당합니다.  기본
\fI<크기>\fP는 LZMA2 딕셔너리 크기 또는 1MiB 중 가장 큰 쪽의 세 배입니다.  보통 바람직한 값으로 LZMA2
딕셔너리 크기나 최소한 1MiB의 2\(en4배입니다.  LZMA2 딕셔너리 크기보다 작은 \fI<크기>\fP 는 램의 소모적
사용 공간으로 할당하는데 LZMA2 딕셔너리 버퍼를 할당한 용량 크기 전체를 다 사용하지 않기 때문입니다.  블록 크기는 블록 헤더에
저장하며, 블록 헤더는 \fBxz\fP 차기 버전에서 다중\-스레드 압축 해제시 활용할 예정입니다.
.IP ""
단일\-스레드 모드에서는 기본적으로 블록 쪼개기를 하지 않습니다.  이 옵션을 설정한다고 해서 메모리 사용에 영향을 주지는 않습니다.
블록 헤더에 크기 정보를 저장하지 않기 때문에 단일\-스레드 모드에서 만든 파일은 다중\-스레드 모드에서 만든 파일과 동일하지 않습니다.
크기 정보의 누락은 또한 \fBxz\fP 차기 버전에서 다중\-스레드 모드에서 압축 해제가 불가능함을 의미하기도 합니다.
.TP 
\fB\-\-block\-list=\fP\fI<크기>\fP
\&\fB.xz\fP 형식으로 압축할 때, 압축하지 않은 데이터에 주어진 처리 시간 간격 이후에 새 블록 처리를 시작합니다.
.IP ""
압축하지 않은 블록 \fI<크기>\fP는 쉼표로 구분한 목록으로 지정합니다.  크기 값을 생략(둘 이상의 연속 쉼표)는 이전
블록 크기를 계속 사용하겠다는 의미입니다.
.IP ""
입력 파일이 \fI<크기>\fP의 합보다 크면, 마지막 \fI<크기>\fP 값을 파일 마지막까지 반복해서 사용합니다.
특별히 \fB0\fP 값을 마지막 값으로 사용하여 파일 나머지 부분을 단일 블록으로 인코딩해야 한다는 의미를 나타낼 수도 있습니다.
.IP ""
인코더 블록 크기를 초과하는 \fI<크기>\fP 값을 지정하면(스레드 모드 기본값 또는
\fB\-\-block\-size=\fP\fI<크기>\fP 옵션으로 지정한 값), 인코더는 \fI<크기>\fP 지정 용량 범위는
유지하면서 추가 블록을 만듭니다.  예를 들면 \fB\-\-block\-size=10MiB\fP
\fB\-\-block\-list=5MiB,10MiB,8MiB,12MiB,24MiB\fP 옵션을 지정하고 입력 파일을 80MiB 용량으로 전달하면,
각각 5, 10, 8, 10, 2, 10, 10, 4, 10, 10, 1 MiB 용량을 차지하는 블록 11개를 결과물로 내줍니다.
.IP ""
다중\-스레드 모드에서 블록 크기는 블록 헤더에 저장합니다.  단일\-스레드 모드에서는 저장하지 않기 때문에 인코딩 처리한 출력은
다중\-스레드 모드의 출력 결과물과는 다릅니다.
.TP 
\fB\-\-flush\-timeout=\fP\fI<제한시간>\fP
압축할 때, 이전 데이터를 소거하고 다음 입력을 블록 단위로 더 읽는데 \fI<제한시간>\fP 밀리초(양의 정수값)가 지났을
경우, 대기중이던 모든 입력 데이터를 인코더에서 소거한 다음 출력 스트림에 전달합니다.  이런 동작은 네트워크로 스트리밍한 데이터를
\fBxz\fP로 압축할 때 쓸만합니다.  \fI<제한시간>\fP 값을 적게 지정하면 적은 지연 시간에 데이터를 받아낼 수 있지만
\fI<제한시간>\fP 값을 크게 하면 압축율을 높일 수 있습니다.
.IP ""
이 기능은 기본적으로 꺼져있습니다.  이 옵션을 한번 이상 지정하면, 마지막 옵션의 값대로 동작합니다.  특별히
\fI<제한시간>\fP 값을 \fB0\fP으로 설정하면 이 설정을 완전히 끌 수 있습니다.
.IP ""
이 기능은 POSIX 시스템이 아닌 곳에서는 사용할 수 없습니다.
.IP ""
.\" FIXME
\fB이 기능은 여전히 시험중입니다\fP.  현재로서는, \fBxz\fP 버퍼링 처리 방식 때문에 \fBxz\fP의 실시간 스트림 압축 해제 기능 활용은
적절하지 않습니다.
.TP 
\fB\-\-memlimit\-compress=\fP\fI<제한용량>\fP
압축 수행시 메모리 사용 한계를 지정합니다.  이 옵션을 여러번 지정하면 마지막 값을 취합니다.
.IP ""
압축 설정이 \fI<제한용량>\fP을 초과하면, \fBxz\fP는 설정 값의 하향 조정을 시도하여 한계 값을 더이상 넘치지 않게 하고
자동 조절을 끝냈다는 알림을 표시합니다.  조정은 다음 순서대로 진행합니다. 스레드 수를 줄입니다. 다중\-스레드 모드에서 스레드 하나의
할당 한계치가 \fI<제한용량>\fP을 넘으면 단일\-스레드 모드로 전환합니다. 그 다음 마지막으로 LZMA2 딕셔너리 크기를
줄입니다.
.IP ""
\fB\-\-format=raw\fP 또는 \fB\-\-no\-adjust\fP 미지정 상황에서 압축할 때, 압축 데이터 출력에 영향을 주지 않고 스레드
처리 수만 줄일 수 있습니다.
.IP ""
\fI<제한용량>\fP 값이 아래 설명한 조건에 맞지 않으면, 오류가 나타나고 \fBxz\fP 명령은 종료 상태 1번을 반환하며
빠져나갑니다.
.IP ""
\fI<제한용량>\fP 값은 여러 방식으로 지정할 수 있습니다:
.RS
.IP \(bu 3
\fI<제한용량>\fP 값은 바이트 용량 절대값입니다.  정수 값을 사용하되 \fBMiB\fP와 같은 접미사를 사용하는게 좋습니다.
예: \fB\-\-memlimit\-compress=80MiB\fP
.IP \(bu 3
\fI<제한용량>\fP 값은 총 물리 메모리(RAM) 용량의 백분율로 지정할 수도 있습니다.  다른 컴퓨터끼리 공유하는 셸
초기화 스크립트의 \fBXZ_DEFAULTS\fP 환경 변수에 값을 설정할 때 특히 쓸만합니다.  이런 방식으로 설정하면 시스템의 메모리 설치
용량에 따라 자동으로 늘어납니다.  예: \fB\-\-memlimit\-compress=70%\fP
.IP \(bu 3
\fI<제한용량>\fP 값은 \fB0\fP 기본값으로 설정하여 초기화할 수 있습니다.  현재로서는 \fI<제한용량>\fP
값이 \fImax\fP(최대) (메모리 사용 한계 없음) 인 상태와 동일합니다.
.RE
.IP ""
\fBxz\fP 32비트 버전에서는 몇가지 특별한 경우가 있습니다. \fI<제한용량>\fP 값이 \fB4020MiB\fP를 넘으면
\fI<제한용량>\fP을 \fB4020MiB\fP로 고정합니다. MIPS32에서는 \fB2000MiB\fP로 대신 고정합니다.
(\fB0\fP과 \fBmax\fP는 이 경우에 해당하지 않습니다.  압축 해제시 비슷한 기능은 없습니다.)  이 경우 32비트 실행 파일이
4GiB(MIPS32의 경우 2GiB) 주소 영역에 접근할 때 매우 용이하며, 다른 경우에는 원하는대로 문제를 일으키지 않습니다.
.IP ""
\fB메모리 활용\fP 섹션도 참고하십시오.
.TP 
\fB\-\-memlimit\-decompress=\fP\fI<제한용량>\fP
압축 해제시 메모리 사용 한계 용량을 설정합니다.  \fB\-\-list\fP 모드에도 영향을 줍니다.  \fI<제한용량>\fP을 넘기지
않고서는 동작이 진행이 안될 경우, \fBxz\fP 에서는 오류를 나타내고 파일 압축 해제를 실패로
간주합니다. \fI<제한용량>\fP을 지정하는 가능한 방법에 대해서는
\fB\-\-memlimit\-compress=\fP\fI<제한용량>\fP 옵션을 참고하십시오.
.TP 
\fB\-\-memlimit\-mt\-decompress=\fP\fI<제한용량>\fP
다중\-스레드 모드 압축 해제시 메모리 사용 한계 용량을 설정합니다.  스레드 수에 영향을 줄 수도 있습니다.  \fBxz\fP에서 파일 압축
해제를 거부하게 하진 않습니다.  \fI<제한용량>\fP 수치가 다중\-스레드로 처리하기에 너무 낮다면,
\fI<제한용량>\fP 값을 무시하고 \fBxz\fP 동작을 단일\-스레드 모드로 계속 진행합니다.  참고로
\fB\-\-memlimit\-decompress\fP 옵션도 사용하면, 단일\-스레드 모드와 다중\-스레드 모드 두 경우에 모두 적용하기에,
다중\-스레드 모드에 적용할 \fI<제한용량>\fP 값은 \fB\-\-memlimit\-decompress\fP에 설정하는 제한 값보다 더
크면 안됩니다.
.IP ""
다른 메모리 사용 용량 제한 옵션과는 달리, \fB\-\-memlimit\-mt\-decompress=\fP\fI<제한용량>\fP 옵션은
시스템별 기본 \fI<제한용량>\fP 값을 지닙니다.  현재 설정 값은 \fBxz \-\-info\-memory\fP 명령으로 확인해볼 수
있습니다.
.IP ""
이 옵션과 기본 값은 한계 값을 주지 않으면 스레드 기반 압축 해제 프로그램이 일부 입력 파일에 대해 정신나간 수준의 메모리 용량을
할당해서 동작이 끝나버릴 수 있습니다.  기본 \fI<제한용량>\fP이 시스템의 사양에 비해 낮다면,
\fI<제한용량>\fP 값을 자유롭게 올리시되, \fBxz\fP 에서 적은 스레드 수에도 메모리 공간 할당을 시도하는 만큼, 입력
파일에 적절한 수준으로 가용 RAM 용량을 넘는 큰 값을 설정하지 마십시오. 메모리나 스와핑 영역 공간이 줄어들면 압축해제 성능을
개선하지 못합니다.
.IP ""
\fI<제한용량>\fP 값을 지정하는 가능한 방법을 보려면
\fB\-\-memlimit\-compress=\fP\fI<제한용량>\fP 옵션을 참고하십시오.  \fI<제한용량>\fP 값을
\fB0\fP으로 설정하면 \fI<제한용량>\fP 값이 시스템 지정 기본값으로 바뀝니다.
.TP 
\fB\-M\fP \fI<제한용량>\fP, \fB\-\-memlimit=\fP\fI<제한용량>\fP, \fB\-\-memory=\fP\fI<제한용량>\fP
\fB\-\-memlimit\-compress=\fP\fI<제한용량>\fP
\fB\-\-memlimit\-decompress=\fP\fI<제한용량>\fP
\fB\-\-memlimit\-mt\-decompress=\fP\fI<제한용량>\fP 지정과 동일합니다.
.TP 
\fB\-\-no\-adjust\fP
압축 출력 결과에 영향을 주는 설정을 조정하지 않고는 메모리 사용 용량 제한 조건이 맞지 않으면 오류를 표시하고 빠져나갑니다.  이
옵션은 \fBxz\fP가 다중\-스레드 모드에서 단일\-스레드 모드로 전환하고 LZMA2 딕셔너리 크기를 줄이는 동작을 막아줍니다.  심지어 이
옵션을 사용하면 메모리 사용 한계를 만족하도록 스레드 수를 줄여 압축 결과물 출력에 영향이 가지 않게 합니다.
.IP ""
원시 스트림(\fB\-\-format=raw\fP)을 만들 떄 자동 조정은 항상 꺼집니다.
.TP 
\fB\-T\fP \fI<스레드수>\fP, \fB\-\-threads=\fP\fI<스레드수>\fP
활용할 작업 스레드 수를 지정합니다.  \fI<스레드수>\fP 값을 \fB0\fP 값으로 설정하면, \fBxz\fP는 시스템에서 지원하는
최대 프로세서 스레드 수를 모두 확보합니다. 실제 스레드 수는 입력 파일이 주어진 설정대로 스레드 처리를 할 만큼 그렇게 크지 않을
경우, 내지는 더 많은 스레드를 사용했을 때 메모리 사용량 한계를 초과할 경우 \fI<스레드수>\fP 보다 적을 수 있습니다.
.IP ""
단일\-스레드와 다중\-스레드 압축 프로그램은 다른 출력 결과물을 냅니다.  단일\-스레드 압축 프로그램은 작은 파일 크기 결과물을 내놓지만,
다중\-스레드 압축 프로그램의 경우 다중\-스레드 압축 프로그램에서 내놓은 결과물은 다중\-스레드로만 압축을 해제할 수 있습니다.
\fI<스레드수>\fP를 \fB1\fP로 설정하면 단일\-스레드 모드를 사용합니다.  \fI<스레드수>\fP를 \fB0\fP과 다른
값으로 설정하면, 시스템에서 실제로 하드웨어 스레드가 1개만 지원한다 하더라도, 다중\-스레드 압축 프로그램을 사용합니다.  (\fBxz\fP
5.2.x에서는 이 경우 단일\-스레드 모드를 활용합니다.)
.IP ""
단일\-스레드로 다중\-스레드 모드를 사용하려면, \fI<스레드수>\fP를 \fB+1\fP로 설정하십시오. \fB+\fP 접두사는 \fB1\fP
이외의 값에는 영향을 주지 않습니다.  메모리 사용량 한계 설정은 \fBxz\fP을 \fB\-\-no\-adjust\fP 옵션을 쓰기 전까지는
단일\-스레드로 전환하게 합니다.  \fB+\fP 접두사 지원은 \fBxz\fP 5.4.0에 추가했습니다.
.IP ""
자동 스레드 수를 요청했고 메모리 사용 한계를 지정하지 않았다면, 시스템에 맞게끔 가능한 스레드 수를 제한하는 기본 소프트 제한 값을
사용합니다. 스레드 수가 한개가 되면 무시하는 이런 개념이 소프트 제한이기에, \fBxz\fP로 하여금 압축 동작 및 압축 해제 동작 수행시
멈추지 않습니다.  이 가본 소프트 제한 값은 \fBxz\fP 실행 도중 다중\-스레드 모드에서 단일\-스레드 모드로 바뀌게 하지는 않습니다.
활성 제한 값은 \fBxz \-\-info\-memory\fP 명령으로 볼 수 있습니다.
.IP ""
현재 스레딩 처리 방식은 입력을 블록 단위로 쪼개고 각각의 블록을 독립적으로 압축하는 동작을 취합니다.  기본 블록 크기는 압축 수준에
따라 다르며 \fB\-\-block\-size=\fP\fI<크기>\fP 옵션으로 재지정할 수 있습니다.
.IP ""
스레드 압축 해제 방식은 여러 블록이 블록 헤더에 넣은 크기 정보와 함께 들어간 파일에만 동작합니다.  다중\-스레드 모드에서 압축한
충분히 큰 모든 파일은 이 조건에 만족하지만, 단일\-스레드 모드에서 압축한 파일은
\fB\-\-block\-size=\fP\fI<크기>\fP 옵션을 지정하더라도 조건에 만족하지 않습니다.
.
.SS "개별 압축 필터 체인 설정"
개별 필터 체인은 사전 설정에 엮인 설정에 의존하는 대신 압축 설정을 세부적으로 하나하나 설정할 수 있게 합니다.  개별 필터 체인을
지정하면, 명령행에 앞서 지정한 사전 설정 옵션(\fB\-0\fP \&...\& \fB\-9\fP 과 \fB\-\-extreme\fP)은 무시합니다.  사전
설정 옵션을 하나 이상의 필터 체인 옵션 다음에 지정하면, 새 사전 설정을 취하며, 앞서 지정한 개별 필터 체인 옵션은 무시합니다.
.PP
필터 체인은 명령행 파이핑에 비교할 수 있습니다.  압축할 때, 압축하지 않은 입력을 첫번째 필터로 놓고, 출력 대상(이 있으면)을 다음
필터로 지정합니다.  최종 필터의 출력은 압축 파일로 기옥합니다.  체인의 최대 필터 수는 4이지만, 필터 체인상 필터 갯수는 보통
1~2개입니다.
.PP
수많은 필터가 필터 체인 상에서 제약점을 가지고 있습니다. 일부 필터는 체인의 마지막 필터로만 동작하며, 일부 다른 필터는 마지막이 아닌
필터로, 어떤 동작은 체인의 어떤 위치에든 둡니다.  필터에 따라, 이 제한은 필터 설계를 따르거나 보안 문제를 막기 위해 존재하기도
합니다.
.PP
개별 필터 체인은 필터 체인에서 원하는 순서대로 하나 이상의 필터 옵션을 사용하여 지정합니다.  이는, 필터 옵션 순서가 중요하다는
뜻입니다! 원시 스트림을 디코딩할 때(\fB\-\-format=raw\fP), 필터 체인은 압축할 때 지정했던 동일한 순서대로 지정합니다.
.PP
필터는 쉼표로 구분하는 필터별 \fI<옵션>\fP이 있습니다. \fI<옵션>\fP에 추가로 입력한 쉼표는 무시합니다.
모든 옵션 값에는 기본값이 있어, 값을 바꾸려면 지정해야합니다.
.PP
전체 필터 체인과 \fI<옵션>\fP을 보려면 \fBxz \-vv\fP (\fB\-\-verbose\fP 두 번)명령을 사용하십시오.  이
명령은 사전 설정이 사용하는 필터 체인 옵션도 볼 수 있습니다.
.TP 
\fB\-\-lzma1\fP[\fB=\fP\fI<옵션>\fP]
.PD 0
.TP 
\fB\-\-lzma2\fP[\fB=\fP\fI<옵션>\fP]
.PD
LZMA1 또는 LZMA2 필터를 필터 체인에 추가합니다.  이 필터는 필터 체인의 마지막 요소로만 사용할 수 있습니다.
.IP ""
LZMA1은 고전 필터로, LZMA1만 지원하는 고전 \fB.lzma\fP 파일 형식에서만 지원합니다.  LZMA2는 LZMA1의 업데이트
버전으로 LZMA1의 실질적 문제를 해결했습니다.  \fB.xz\fP 형식은 LZMA2 필터를 사용하며 LZMA1 필터는 전적으로 지원하지
않습니다.  압축 속도와 압축율은 LZMA1과 LZMA2가 실질적으로 동일합니다.
.IP ""
LZMA1과 LZMA2는 동일한 \fI<옵션>\fP 집합을 공유합니다:
.RS
.TP 
\fBpreset=\fP\fI<사전설정>\fP
LZMA1 또는 LZMA2의 모든 \fI<옵션>\fP을 \fI<사전설정>\fP으로
초기화합니다. \fI<사전설정>\fP 값은 정수 값으로 이루어져 있으며, 사전 설정에 변형을 줄 떄 단일 문자가 따라올 수도
있습니다.  정수 값은 \fB0\fP에서 \fB9\fP 까지이며, 명령행 옵션에서 \fB\-0\fP \&...\& \fB\-9\fP로 대응합니다.  변형 옵션으로
지원하는 문자는 현재 \fBe\fP 뿐이며, \fB\-\-extreme\fP에 대응합니다.  \fI<사전설정>\fP 값을 지정하지 않으면,
LZMA1 또는 LZMA2 기본값을 사전 설정 \fB6\fP에서 가져온 \fI<옵션>\fP으로 취합니다.
.TP 
\fBdict=\fP\fI<크기>\fP
딕셔너리(기록 버퍼)  \fI<크기>\fP는 최근 처리한 비압축 데이터를 바이트 단위로 메모리에 얼마나 유지하는지 나타냅니다.
알고리즘은 비압축 데이터상 바이트 시퀀스(일치 항목) 반복 탐색을 시도하며, 해당 부분을 딕셔너리의 현재 참조로 치환합니다. 딕셔너리가
크면 일치하는 항목을 찾을 기회가 더 많아집니다.  따라서, 딕셔너리 \fI<크기>\fP를 더욱 크게 설정하면 압축율을 증가할
수는 있지만, 압축하지 않은 파일보다 딕셔너리가 크면 메모리 낭비율이 올라갑니다.
.IP ""
보통 딕셔너리 \fI<크기>\fP는 64KiB 에서 64MiB 정도 됩니다. 최소 4KiB 입니다. 압축시 최대 용량은 현재
1.5GiB(1536MiB)로 나타납니다.  압축 해제 프로그램에도 4GiB 미만으로 딕셔너리 크기를 이미 지원하며 4GiB 라는 수치는
LZMA1과 LZMA2 스트림 형식의 최대값입니다.
.IP ""
딕셔너리 \fI<크기>\fP와 검색기(\fImf\fP)는 LZMA1 또는 LZMA 인코더의 메모리 사용량을 함께 결정합니다.
동일한(또는 더 큰) 딕셔너리 \fI<크기>\fP가 데이터를 압축했을 때만큼 압축 해제할 떄 필요하기 때문에, 디코더의 메모리
사용량은 압축할 때의 딕셔너리 크기로 결정합니다.  \fB.xz\fP 헤더에는 딕셔너리 \fI<크기>\fP를 2^\fIn\fP 또는
2^\fIn\fP + 2^(\fIn\fP\-1) 으로 저장하기에, 이 \fI<크기>\fP 값을 압축할 때 선호하는 편입니다.  다른
\fI<크기>\fP 값은 \fB.xz\fP 헤더에 저장할 때 반올림합니다.
.TP 
\fBlc=\fP\fIlc\fP
리터럴 컨텍스트 비트 수를 지정합니다.  최소 값은 0이고 최대 값은 4입니다. 기본 값은 3입니다.  추가로, \fIlc\fP 값과
\fIlp\fP 값의 합은 4를 넘으면 안됩니다.
.IP ""
조건이 일치하지 않아 인코딩할 수 없는 모든 바이트는 리터럴로 인코딩합니다.  이 말인 즉슨, 간단히 8비트 바이트로서의 리터럴을 한번에
하나씩 인코딩합니다.
.IP ""
리터럴 코딩을 할 때 이전 비압축 바이트와 다음 바이트와의 관련성을 가진 가장 많은 \fIlc\fP 비트 수를 가정합니다.  예를 들면, 보통
영문 문장의 경우 대문자 다음에 종종 소문자가 오고, 소문자 다음에 다른 소문자가 따라옵니다. US\-ASCII 문자 세트에서는 가장 긴
비트 3개는 대문자에 대해 010, 소문자에 대해 011입니다.  \fIlc\fP 값이 최소한 3이면, 리터럴 코딩시 비압축 데이터에 대해
이런 속성의 장점을 취할 수 있습니다.
.IP ""
(어쨌거나) 기본값 (3)은 보통 적절합니다.  최대 압축을 원한다면 \fBlc=4\fP 값을 시험해보십시오.  때로는 약간 도움이 되기도
하겠지만, 오히려 결과가 안좋을 수도 있습니다.  결과가 엄한 방향으로 간다면, \fBlc=2\fP 값도 시험해보십시오.
.TP 
\fBlp=\fP\fIlp\fP
리터럴 위치 비트 수를 지정하빈다.  최소 값은 0이고 최대 값은 4입니다. 기본 값은 0입니다.
.IP ""
\fIlp\fP 값은 리터럴 인코딩 진행시 비압축 데이터 정렬 방식 고려에 영향을 줍니다.  정렬 방식에 대한 자세한 정보는 하단 \fIpb\fP를
참고하십시오.
.TP 
\fBpb=\fP\fIpb\fP
위치 비트 수를 지정합니다.  최소 값은 0이며 최대 값은 4입니다.  기본값은 2입니다.
.IP ""
\fIpb\fP 값은 보통 압축하지 않은 데이터에 어떤 정렬 방식을 고려하느냐에 영향을 줍니다.  기본적으로 4바이트
정렬(2^\fIpb\fP=2^2=4)을 의미하는데, 이보다 더 나은 추측 값이 없어서 종종 최적의 선택으로 간주합니다.
.IP ""
정렬 상태를 알지 못할 경우, \fIpb\fP 설정 값이 파일 크기를 조금 줄일 수 있습니다.  예를 들면, 텍스트 파일이 단일 바이트 단위로
정돈된 상태(US\-ASCII, ISO\-8859\-*, UTF\-8)라면, \fBpb=0\fP 설정 값으로 압축율을 조금 개선할 수 있습니다.
UTF\-16 텍스트의 경우, \fBpb=1\fP 설정 값이 좋은 선택입니다.  정렬 바이트가 3 바이트 같은 홀수 바이트일 경우,
\fBpb=0\fP 설정 값이 최적의 선택일지도 모릅니다.
.IP ""
가정 정렬을 \fIpb\fP 값과 \fIlp\fP 값으로 조정하긴 하지만, LZMA1과 LZMA2는 여전히 16바이트 정렬 방식으로 선호합니다.
LZMA1 또는 LZMA2로 종종 압축하는 파일 형식이라고 하면 고려해볼만 합니다.
.TP 
\fBmf=\fP\fImf\fP
일치 검색기는 인코더 속도, 메모리 사용량, 압축율에 주된 영향을 줍니다.  보통 해시 체인 검색기는 이진 트리 검색기보다 빠르긴
합니다.  기본 값은 \fI<사전설정>\fP에 따라 다릅니다. 0은 \fBhc3\fP을, 1\(en3은 \fBhc4\fP를, 나머지는
\fBbt4\fP를 활용합니다.
.IP ""
다음 검색 필터를 지원합니다.  메모리 사용 공식은 \fIdict\fP 값이 2의 승수일 경우 실제에 가까운 근사치입니다.
.RS
.TP 
\fBhc3\fP
2바이트, 3바이트 해싱 체인
.br
\fInice\fP 최소값: 3
.br
메모리 사용:
.br
\fIdict\fP * 7.5 (조건: \fIdict\fP <= 16 MiB);
.br
\fIdict\fP * 5.5 + 64 MiB (조건: \fIdict\fP > 16 MiB)
.TP 
\fBhc4\fP
2바이트, 3바이트, 4바이트 해싱 체인
.br
\fInice\fP 최소값: 4
.br
메모리 사용:
.br
\fIdict\fP * 7.5 (조건: \fIdict\fP <= 32 MiB);
.br
\fIdict\fP * 6.5 (조건: \fIdict\fP > 32 MiB)
.TP 
\fBbt2\fP
2바이트 해싱 이진 트리
.br
\fInice\fP 최소값: 2
.br
메모리 사용: \fIdict\fP * 9.5
.TP 
\fBbt3\fP
2바이트, 3바이트 해싱 이진트리
.br
\fInice\fP 최소값: 3
.br
메모리 사용:
.br
\fIdict\fP * 11.5 (조건: \fIdict\fP <= 16 MiB);
.br
\fIdict\fP * 9.5 + 64 MiB (조건: \fIdict\fP > 16 MiB)
.TP 
\fBbt4\fP
2바이트, 3바이트, 4바이트 해싱 이진 트리
.br
\fInice\fP 최소값: 4
.br
메모리 사용:
.br
\fIdict\fP * 11.5 (조건: \fIdict\fP <= 32 MiB);
.br
\fIdict\fP * 10.5 (조건: \fIdict\fP > 32 MiB)
.RE
.TP 
\fBmode=\fP\fI<모드>\fP
압축 \fI<모드>\fP 값은 일치 검색기에서 생산하는 데이터 분석 방식을 지정합니다.  지원하는 \fI<모드>\fP는
\fBfast\fP와 \fBnormal\fP 입니다. 기본값은 \fI<사전설정>\fP값 0\(en3에 대해 \fBfast\fP,
\fI<사전설정>\fP값 4\(en9에 대해 \fBnormal\fP입니다.
.IP ""
보통 \fBfast\fP는 해시 체인 검색기에서 사용하며 \fBnormal\fP은 이진 트리 검색기에서 사용합니다.  이 동작은 또한
\fI<사전설정>\fP 값이 할 일이기도 합니다.
.TP 
\fBnice=\fP\fInice\fP
일치하는 nice 길이를 지정합니다.  최소한 \fInice\fP 바이트 정도 일치하면, 알고리즘이 가능한 최선의 부분을 찾는 동작을
멈춥니다.
.IP ""
\fInice\fP 값은 2\(en273 바이트입니다.  값이 클 수록 속도 면에서는 손해를 보겠지만 압축율은 더욱 올라갑니다.  기본 값은
\fI<사전설정>\fP값에 따라 다릅니다.
.TP 
\fBdepth=\fP\fI<깊이>\fP
일치 검색기에서의 최대 검색 깊이를 지정합니다.  기본값은 특별한 값 0으로 지정하며, 이 값으로 압축 프로그램이 \fImf\fP 와
\fInice\fP간 적절한 \fI<깊이>\fP 값을 결정합니다.
.IP ""
적절한 해시 체인 \fI<깊이>\fP 값은 이진 트리에서 4\(en100 그리고 16\(en1000 입니다. 상당히 큰 값을
\fI<깊이>\fP 값으로 사용하면 일부 파일에 대해 인코더가 매우 느리게 동작할 수가 있습니다.  압축 시간이 너무 오래걸려서
동작을 중간에 끊을 준비가 되지 않은 이상 \fI<깊이>\fP 설정 값은 1000을 넘지 않게하십시오.
.RE
.IP ""
원시 스트림(\fB\-\-format=raw\fP)을 디코딩할 때, LZMA2는 딕셔너리 \fI<크기>\fP만 필요합니다.  LZMA1는
\fIlc\fP, \fIlp\fP, \fIpb\fP 값이 모두 필요합니다.
.TP 
\fB\-\-x86\fP[\fB=\fP\fI<옵션>\fP]
.PD 0
.TP 
\fB\-\-arm\fP[\fB=\fP\fI<옵션>\fP]
.TP 
\fB\-\-armthumb\fP[\fB=\fP\fI<옵션>\fP]
.TP 
\fB\-\-arm64\fP[\fB=\fP\fI<옵션>\fP]
.TP 
\fB\-\-powerpc\fP[\fB=\fP\fI<옵션>\fP]
.TP 
\fB\-\-ia64\fP[\fB=\fP\fI<옵션>\fP]
.TP 
\fB\-\-sparc\fP[\fB=\fP\fI<옵션>\fP]
.PD
브랜치/호출/점프(BCJ) 필터를 필터 체인에 추가합니다.  이 필터는 필터 체인의 비종결 필터로만 사용할 수 있습니다.
.IP ""
BCJ 필터는 머신 코드의 상대 주소를 절대 주소로 변환합니다.  데이터 크기를 바꾸지는 않지만 LZMA2에서 \fB.xz\fP 파일을
0\(en15% 정도 줄여주게 하는 중복성이 늘어납니다.  BCJ 필터는 언제든 뒤집을 수 있어, 데이터에 적절하지 않은 BCJ 필터
형식을 활용하면, 그냥 가만히 두면 압축율이 약간 떨어지게 한다 하더라도, 데이터를 잃을 수가 있습니다.  BCJ 필터는 굉장히 빠르며
메모리 공간을 적게 활용합니다.
.IP ""
이 BCJ 필터에는 압축율 관련 몇가지 문제가 있습니다:
.RS
.IP \(bu 3
실행 코드가 들어있는 몇가지 파일 형식(예: 목적 파일, 정적 라이브러리, 리눅스 커널 모듈)의 경우 필터 값으로 채운 명령 주소가
있습니다.  여기 BCJ 필터의 경우 파일의 압축율을 떨어뜨리는 주소 변환을 수행합니다.
.IP \(bu 3
BCJ 필터를 아카이브에 적용하면, BCJ 필터를 사용하지 않았을 때보다 압축율이 떨어질 수가 있습니다.  예를 들면, 유사하거나 동일한
실행 파일 여럿이 있으면 필터를 사용하여 파일을 덜 비슷하게 만들어 압축율이 떨어지게 합니다.  동일한 아카이브 파일에서 비 실행 파일의
내용에 대해서도 비슷한 일이 벌어질 수 있습니다.  실제로 하나는 BCJ 필터를 걸고 하나는 제외하여 각 경우에 대해 어떤 경우가 결과가
우수한 지 살펴보겠습니다.
.RE
.IP ""
다른 명령 세트는 다른 정렬 상태에 놓여있습니다.  실행 파일은 필터가 제대로 동작하게 하려면 입력 데이터에 있는 이 값의 배수로
정돈해야합니다.
.RS
.RS
.PP
.TS
tab(;);
l n l
l n l.
필터;정렬;참고
x86;1;32\-bit 또는 64\-bit x86
ARM;4;
ARM\-Thumb;2;
ARM64;4;4096 바이트 정렬이 가장 좋습니다
PowerPC;4;빅엔디안 전용
IA\-64;16;Itanium
SPARC;4;
.TE
.RE
.RE
.IP ""
BCJ 필터를 사용한 데이터는 LZMA2로 보통 압축하기 때문에 LZMA2 옵션을 선택한 BCJ 필터의 정렬기준에 맞추도록 설정하면
압축율을 좀 더 개선할 수 있습니다.  예를 들면, IA\-64 필터에서는 \fBpb=4\fP 또는 LZMA2에 대해
\fBpb=4,lp=4,lc=0\fP (2^4=16) 값이 바람직합ㄴ디ㅏ.  x86 필터는 예외로, x86 실행 파일을 압축할 경우
LZMA2의 기본 4바이트 정렬을 따르는게 좋습니다.
.IP ""
모든 BCJ 필터는 동일한 \fI옵션\fP을 지원합니다:
.RS
.TP 
\fBstart=\fP\fI<오프셋>\fP
상대 주소와 절대 주소를 변환할 때 사용할 시작 \fI<오프셋>\fP을 지정합니다.  \fI<오프셋>\fP에는 필터
정렬 배수여야 합니다(상단 테이블 참조).  기본값은 0입니다.  실제로 기본값이 낫습니다. 개별 \fI<오프셋>\fP 지정
값은 거의 쓸모가 없습니다.
.RE
.TP 
\fB\-\-delta\fP[\fB=\fP\fI<옵션>\fP]
필터 체인에 델타 필터를 추가합니다.  델타 필터는 필터 체인에서 마지막에 지정하지 않은 필터로만 사용할 수 있습니다.
.IP ""
현재로서는 바이트 단위 단순 델타계산 결과만 보여줍니다.  예를 들면, 압축하지 않은 비트맵 그림 또는 압축하지 않은 PCM 오디오를
압축할 때 쓸만합니다.  그러나 특별한 목적으로 활용하는 알고리즘은 델타 + LZMA2 보다 더 나은 결과를 가져다 주기도 합니다.
이는 특히 오디오의 경우 맞는 이야기인데, \fBflac\fP(1)의 경우 더 빠르고 우수한 압축율을 보여줍니다.
.IP ""
지원 \fI옵션\fP:
.RS
.TP 
\fBdist=\fP\fI<차이>\fP
바이트 단위 델터 계산 \fI<차이>\fP를 지정합니다.  \fI<차이>\fP값은 1\(en256 이어야합니다.  기본
값은 1입니다.
.IP ""
예를 들어, \fBdist=2\fP 옵션과 A1 B1 A2 B3 A3 B5 A4 B7 입력 값을 주면, 출력 값은 A1 B1 01 02 01
02 01 02 입니다.
.RE
.
.SS "기타 옵션"
.TP 
\fB\-q\fP, \fB\-\-quiet\fP
경고 및 알림을 끕니다.  두 번 지정하면 오류 메시지 표시도 끕니다.  이 옵션은 종료 상태에 영향을 주지 않습니다.  경고 표시를
끄더라도, 종료 상태에서는 여전히 경고가 나타났음을 알려줍니다.
.TP 
\fB\-v\fP, \fB\-\-verbose\fP
출력 내용이 많아집니다.  표준 오류를 터미널에 연결했다면 \fBxz\fP는 진행 표시를 나타냅니다.  \fB\-\-verbose\fP를 두번 지정하면
더 많은 내용을 표시합니다.
.IP ""
진행 표시에서는 다음 정보를 나타냅니다:
.RS
.IP \(bu 3
입력 파일의 크기를 알고 있을 경우 완료 백분율.  파이프 처리시에는 백분율을 나타낼 수 없습니다.
.IP \(bu 3
산출 압축 데이터 용량 (압축) 또는 소모 공간 용량 (압축 해제).
.IP \(bu 3
비압축 데이터 소모 용량 (압축) 또는 산출 용량 (압축 해제).
.IP \(bu 3
압축 데이터 산출 용량을 비압축 데이터 처리 용량으로 나누어 계산한 압축율.
.IP \(bu 3
압축 또는 압축 해제 속도.  초당 비압축 데이터 소모량(압축) 또는 산출 용량(압축 해제)를 측정한 값입니다.  \fBxz\fP에서 파일
처리를 시작한 몇 초 후 나타납니다.
.IP \(bu 3
경과 시간 형식은 M:SS 또는 H:MM:SS 입니다.
.IP \(bu 3
추산 여분 시간은 \fBxz\fP가 파일을 처리하기 시작한 이후 입력 파일의 크기를 알고 몇 초가 지난 후에야 보여줍니다.  시간은 콜론
문자를 사용하지 않고 덜 자세한 형식으로, 예를 들면, 2분 30초 와 같은 형식으로 보여줍니다.
.RE
.IP ""
표준 오류가 터미널이 아니라면 \fB\-\-verbose\fP는 \fBxz\fP에서 파일 이름, 압축 크기, 압축 해제 용량, 압축율, 그리고
가능하다면 파일을 압축 또는 압축 해제한 후 표준 오류로 속도와 걸린 시간을 나타내도록 합니다.  속도와 걸린 시간 정보는 동작을
처리하는데 최소한 몇초 정도 소요했을 경우에만 들어갑니다.  동작이 끝나지 않았다면, 이를테면 사용자의 중단 요청이 있었을 경우 입력
파일의 크기를 알고 있을 때 압축 백분율 정보도 들어갑니다.
.TP 
\fB\-Q\fP, \fB\-\-no\-warn\fP
경고로 알릴 만한 상황을 만났다 하더라도 종료 상태 2번을 설정하지 않습니다.  이 옵션은 출력 수준에 영향을 주지 않기 때문에,
\fB\-\-quiet\fP 옵션과 \fB\-\-no\-warn\fP 옵션을 경고 표시를 막고 종료 상태를 바꾸지 않을 목적으로 사용합니다.
.TP 
\fB\-\-robot\fP
머신에서 해석할 형식으로 메시지를 나타냅니다.  liblzma 대신 \fBxz\fP를 활용하려는 다양상 스크립트로서의 프론트엔드를 쉽게
작성하도록 하기 위함입니다.  이 옵션을 지정한 출력은 \fBxz\fP 릴리스가 어떻게 되든 안정 버전이란 의미입니다.  자세한 내용은
\fB로봇 모드\fP 섹션을 참고하십시오.
.TP 
\fB\-\-info\-memory\fP
압축 및 압축 해제시 물리 메모리 용량 (RAM), \fBxz\fP에서 파악하는 프로세서 스레드 갯수, 메모리 사용량 한계를 파악하기 쉬운
형식으로 나타내고 무사히 나갑니다.
.TP 
\fB\-h\fP, \fB\-\-help\fP
보통 사용하는 옵션을 설명하는 도움말 메시지를 출력한 후, 완전히 빠져나갑니다.
.TP 
\fB\-H\fP, \fB\-\-long\-help\fP
\fBxz\fP의 모든 기능을 설명하는 도움말 메시지를 출력한 후, 완전히 빠져나갑니다
.TP 
\fB\-V\fP, \fB\-\-version\fP
\fBxz\fP와 liblzma 버전 번호를 가독 형식으로 출력합니다.  기계 해석 가능 형식을 가져오려면 \fB\-\-version\fP 앞에
\fB\-\-robot\fP을 지정하십시오.
.
.SH "로봇 모드"
로봇 모드는 \fB\-\-robot\fP 옵션으로 동작합니다.  \fBxz\fP 출력을 다른 프로그램에서 해석하기 쉽게 해줍니다.  현재로서는
\fB\-\-robot\fP 옵션은  \fB\-\-version\fP, \fB\-\-info\-memory\fP, \fB\-\-list\fP 옵션하고만 사용할 수 있습니다.
앞으로는 압축 및 압축 해제 동작에 대해서도 지원합니다.
.
.SS 버전
\fBxz \-\-robot \-\-version\fP 은 \fBxz\fP 와 liblzma의 버전 번호를 다음 형식으로 나타냅니다:
.PP
\fBXZ_VERSION=\fP\fIXYYYZZZS\fP
.br
\fBLIBLZMA_VERSION=\fP\fIXYYYZZZS\fP
.TP 
\fIX\fP
주 버전.
.TP 
\fIYYY\fP
부 버전.  짝수가 안정 버전입니다.  홀수는 알파 또는 베타 버전입니다.
.TP 
\fIZZZ\fP
안정 릴리스의 패치 수준 또는 개발 릴리스의 횟수입니다.
.TP 
\fIS\fP
안정도.  0은 알파 버전, 1은 베타 버전을 나타내며, 2는 안정 버전을 나타냅니다.  \fIS\fP는 \fIYYY\fP 값이 짝수라 해도 항상
2여야 합니다.
.PP
\fBxz\fP 명령과 liblzma이 동일한 XZ 유틸리티 릴리스에서 나왔다면 두 행의 \fIXYYYZZZS\fP 값은 같습니다.
.PP
예제: 4.999.9beta는 \fB49990091\fP이며, 5.0.0은 \fB50000002\fP입니다.
.
.SS "메모리 제한 정보"
\fBxz \-\-robot \-\-info\-memory\fP 명령은 탭으로 나뉜 여러 컬럼을 단일 행으로 나타냅니다:
.IP 1. 4
물리 메모리(RAM)의 바이트 단위 총량.
.IP 2. 4
압축 진행시 바이트 단위 메모리 사용 한계값 (\fB\-\-memlimit\-compress\fP).  특수 값 \fB0\fP은 단일\-스레드 모드에서
제한을 두지 않는 기본 설정임을 나타냅니다.
.IP 3. 4
압축 해제시 바이트 단위 메모리 사용 한계값 (\fB\-\-memlimit\-decompress\fP).  특수 값 \fB0\fP은 단일\-스레드 모드에서
제한을 두지 않는 기본 설정임을 나타냅니다.
.IP 4. 4
\fBxz\fP 5.3.4alpha 이후: 다중\-스레드 기반 압축 해제시 바이트 단위 메모리
사용량(\fB\-\-memlimit\-mt\-decompress\fP).  분명하게 제한을 걸어두지 않았을 경우 5번째 컬럼에 나타난 시스템별
기본값을 사용하기 때문에 0 값을 지정하면 안됩니다.  또한 \fB\-\-memlimit\-mt\-decompress\fP로 세번째 컬럼 값보다 더
크게 지정을 한다 할지라도 이 값이 세번째 컬럼 값보다 크면 안됩니다.
.IP 5. 4
\fBxz\fP 5.3.4alpha 이후: A system\-specific default memory usage limit that is
used to limit the number of threads when compressing with an automatic
number of threads (\fB\-\-threads=0\fP)  and no memory usage limit has been
specified (\fB\-\-memlimit\-compress\fP).  This is also used as the default value
for \fB\-\-memlimit\-mt\-decompress\fP.
.IP 6. 4
\fBxz\fP 5.3.4alpha 이후: Number of available processor threads.
.PP
차후, \fBxz \-\-robot \-\-info\-memory\fP 출력에는 더 많은 내용이 들어가지만, 한 줄 이상은 넘어가지 않습니다.
.
.SS "목록 모드"
\fBxz \-\-robot \-\-list\fP 명령은 탭으로 구분한 출력 형태를 활용합니다.  모든 행의 첫번째 컬럼에는 해당 행에서 찾을 수
있는 정보의 형식을 나타냅니다:
.TP 
\fB이름\fP
이 행은 항상 파일 목록 시작 부분의 첫번째 줄에 있습니다.  이 행의 두번째 컬럼에 파일 이름이 들어있습니다.
.TP 
\fB파일\fP
이 행에는 \fB.xz\fP 파일의 전반적인 정보가 들어있습니다.  이 행은 항상 \fB이름\fP 행 다음에 있습니다.
.TP 
\fB스트림\fP
이 행 형식은 \fB\-\-verbose\fP 옵션을 지정했을 때만 사용합니다.  \fB.xz\fP 파일의 \fB스트림\fP 행 수만큼 나타납니다.
.TP 
\fB블록\fP
이 행 형식은 \fB\-\-verbose\fP 옵션을 지정했을 때만 사용합니다.  \fB.xz\fP 파일의 블록 수만큼 \fB블록\fP 행이 나타납니다.
\fB블록\fP 행은 모든 \fB스트림\fP 행 다음에 나타납니다. 다른 형식의 행이 끼어들지는 않습니다.
.TP 
\fB요약\fP
이 행 형식은 \fB\-\-verbose\fP 옵션을 두번 지정했을 때만 사용합니다.  이 행은 모든 \fB블록\fP 행 다음에 출력합니다.
\fB파일\fP 행과 비슷하게, \fB요약\fP 행에는 \fB.xz\fP 파일의 전반적인 정보가 담겨있습니다.
.TP 
\fB총계\fP
이 행은 목록 출력의 가장 마지막에 항상 나타납니다.  총 갯수와 크기를 나타냅니다.
.PP
\fB파일\fP 행 컬럼:
.PD 0
.RS
.IP 2. 4
파일 스트림 갯수
.IP 3. 4
스트림의 블록 총 갯수
.IP 4. 4
파일 압축 크기
.IP 5. 4
파일 압축 해제 크기
.IP 6. 4
예를 들면, \fB0.123\fP과 같은 압축율 입니다.  비율이 9.999라면, 대시 문자 3개 (\fB\-\-\-\fP)를 비율 값 대신 나타냅니다.
.IP 7. 4
쉼표로 구분한 무결성 검사 이름 목록입니다.  \fBNone\fP, \fBCRC32\fP, \fBCRC64\fP, \fBSHA\-256\fP 문자열을 알려진
검사 형식으로 사용합니다.  알 수 없는 검사 형식에 대해서는 \fBUnknown\-\fP\fIN\fP을 사용하며, 여기서 \fIN\fP은 (한 두자리)
정수형 숫자값으로 이루어진 검사 ID 입니다.
.IP 8. 4
파일의 스트림 패딩 총 길이
.RE
.PD
.PP
\fB스트림\fP 행 컬럼:
.PD 0
.RS
.IP 2. 4
스트림 번호 (첫 스트림은 1번)
.IP 3. 4
스트림의 블록 총 갯수
.IP 4. 4
압축 시작 오프셋
.IP 5. 4
비압축 시작 오프셋
.IP 6. 4
압축 크기 (스트림 패딩 미포함)
.IP 7. 4
압축 해제 용량
.IP 8. 4
압축율
.IP 9. 4
무결성 검사 이름
.IP 10. 4
스트림 패딩 길이
.RE
.PD
.PP
\fB블록\fP 행 컬럼:
.PD 0
.RS
.IP 2. 4
이 블록이 들어간 스트림 갯수
.IP 3. 4
스트림 시작 부분의 블록 번호 (첫번째 블록은 1번)
.IP 4. 4
파일 시작 부분의 블록 번호
.IP 5. 4
파일 시작 부분의 압축 시작 오프셋
.IP 6. 4
파일 시작 부분의 비압축 시작 오프셋
.IP 7. 4
총 블록 압축 크기 (헤더 포함)
.IP 8. 4
압축 해제 용량
.IP 9. 4
압축율
.IP 10. 4
무결성 검사 이름
.RE
.PD
.PP
\fB\-\-verbose\fP를 두 번 지정하면, 추가 컬럼을 \fB블록\fP 행에 넣습니다.  \fB\-\-verbose\fP 단일 지정시에는 이 정보를 볼
때 탐색을 여러번 수행해야 하기 때문에 실행 과정이 느려질 수 있어서 나타내지 않습니다.
.PD 0
.RS
.IP 11. 4
16진수 무결성 검사값
.IP 12. 4
블록 헤더 크기
.IP 13. 4
블록 플래그: \fBc\fP는 압축 크기가 현재 값임을 나타내고, \fBu\fP는 압축 전 원본 크기가 현재 값임을 나타냅니다.  플래그를 설정하지
않았다면, 문자열 길이를 유지할 목적으로 대시 \fB\-\fP 를 대신 나타냅니다.  새 플래그는 나중에 문자열 끝 부분에 추가할 예정입니다.
.IP 14. 4
블록에 압축 해서 넣은 데이터의 실제 츠기 (블록 헤더, 블록 패딩, 검사 필드 제외)
.IP 15. 4
이 \fBxz\fP 버전에서 이 블록의 압축을 해제할 때 필요한 (바이트 단위) 메모리 용량
.IP 16. 4
필터 체인.  대부분 사용하는 옵션은 압축 해제시 필요한 옵션만을 \fB.xz\fP 헤더에 저장하기 때문에 압축 시간에 알 수 없습니다.
.RE
.PD
.PP
\fB요약\fP 행 컬럼:
.PD 0
.RS
.IP 2. 4
이 \fBxz\fP 버전에서 이 파일 압축을 해제할 때 필요한 (바이트 단위) 메모리 용량
.IP 3. 4
모든 블록 헤더에 압축 크기와 압축 전 원본 크기 정보가 들어갔는지 여부를 나타내는 \fByes\fP 또는 \fBno\fP 값
.PP
\fBxz\fP \fI5.1.2alpha\fP \fI부터\fP:
.IP 4. 4
파일 압축 해제시 필요한 최소 \fBxz\fP 버전
.RE
.PD
.PP
\fB총계\fP 행 컬럼:
.PD 0
.RS
.IP 2. 4
스트림 갯수
.IP 3. 4
블록 갯수
.IP 4. 4
압축 크기
.IP 5. 4
압축 해제 용량
.IP 6. 4
평균 압축율
.IP 7. 4
파일에 들어 있어 쉼표로 구분한 무결성 검사 이름 목록
.IP 8. 4
스트림 패딩 길이
.IP 9. 4
파일 갯수.  \fB파일\fP 행의 컬럼 순서를 따라갑니다.
.PD
.RE
.PP
\fB\-\-verbose\fP 옵션을 두 번 지정하면, \fB총계\fP 행에 추가 컬럼이 들어갑니다:
.PD 0
.RS
.IP 10. 4
이 \fBxz\fP 버전에서 파일 압축을 해제할 떄 필요한 (바이트 단위) 최대 메모리 사용량
.IP 11. 4
모든 블록 헤더에 압축 크기와 압축 전 원본 크기 정보가 들어갔는지 여부를 나타내는 \fByes\fP 또는 \fBno\fP 값
.PP
\fBxz\fP \fI5.1.2alpha\fP \fI부터\fP:
.IP 12. 4
파일 압축 해제시 필요한 최소 \fBxz\fP 버전
.RE
.PD
.PP
차후 버전에서는 새 행 형식을 추가하고 기존 행 형식에 추가할 수 있는 새 컬럼을 넣기 까지는 알 수 있겠지만, 기존 컬럼은 바꾸지 않을
예정입니다.
.
.SH "종료 상태"
.TP 
\fB0\fP
모든 상태 양호.
.TP 
\fB1\fP
오류 발생.
.TP 
\fB2\fP
눈여겨볼 경고가 나타났지만, 실제 오류는 일어나지 않음.
.PP
표준 오류에 출력하는 알림(경고 또는 오류 아님)는 종료 상태에 영향을 주지 않습니다.
.
.SH 환경
\fBxz\fP는 빈칸으로 구분한 옵션 값 목록을 \fBXZ_DEFAULTS\fP, \fBXZ_OPT\fP 환경 변수에서 순서대로, 명령행에서 옵션을
해석하기 전에 불러옵니다.  참고로 환경 변수에서 옵션만 해석하며, 옵션이 아닌 부분은 조용히 무시합니다.  해석은
\fBgetopt_long\fP(3)으로 가능하며,  명령행 인자로 활용하기도 합니다.
.TP 
\fBXZ_DEFAULTS\fP
사용자별, 시스템 범위 기본 옵션입니다.  보통 \fBxz\fP의 메모리 사용량 제한을 기본으로 걸어둘 경우 셸 초기화 스크립트에
설정합니다.  셸 초기화 스크립트와 별도의 유사한 경우를 제외하고라면, 스크립트에서는 \fBXZ_DEFAULTS\fP 환경 변수를 설정하지
말거나 설정을 해제해야합니다.
.TP 
\fBXZ_OPT\fP
\fBxz\fP 명령행으로 옵션 설정 값을 직접 전달할 수 없을 경우 \fBxz\fP에 옵션을 전달하는 환경 변수입니다.  예를 들어, \fBxz\fP를
스크립트 또는 도구에서 실행할 경우 GNU \fBtar\fP(1) 라면:
.RS
.RS
.PP
.nf
\f(CWXZ_OPT=\-2v tar caf foo.tar.xz foo\fP
.fi
.RE
.RE
.IP ""
예를 들면, 스크립트에서 \fBXZ_OPT\fP 를 활용하여, 스크립트별로 기본 압축 옵션을 지정할 수 있습니다.  적절한 이유가 있다면
\fBXZ_OPT\fP 옵션 값을 사용자가 바꾸는걸 추천합니다.  예를 들면, \fBsh\fP(1)  스크립트에서 다음처럼 활용할 수도 있습니다:
.RS
.RS
.PP
.nf
\f(CWXZ_OPT=${XZ_OPT\-"\-7e"} export XZ_OPT\fP
.fi
.RE
.RE
.
.SH "LZMA 유틸리티 호환성"
\fBxz\fP의 명령행 문법은 실제로 LZMA 유틸리티 4.32.x에서 찾을 수 있는 \fBlzma\fP, \fBunlzma\fP \fBlzcat\fP의
상위 집합입니다.  대부분의 경우 LZMA 유틸리티를 XZ 유틸리티로 기존에 작성한 스크립트를 깨지 않고도 바꿀 수 있습니다.  몇가지
비호환성 문제 때문에 문제가 일어날 수는 있습니다.
.
.SS "압축 사전 설정 단계"
압축 수준 사전 설정의 번호 부여 방식은 \fBxz\fP와 LZMA 유틸리티가 동일하지 않습니다.  가장 중요한 차이는 다른 사전 설정에 대해
딕셔너리 크기를 어떻게 대응했느냐 여부입니다.  딕셔너리 크기는 압축 해제시 메모리 사용량과 거의 비슷합니다.
.RS
.PP
.TS
tab(;);
c c c
c n n.
단계;xz;LZMA 유틸리티
\-0;256 KiB;없음
\-1;1 MiB;64 KiB
\-2;2 MiB;1 MiB
\-3;4 MiB;512 KiB
\-4;4 MiB;1 MiB
\-5;8 MiB;2 MiB
\-6;8 MiB;4 MiB
\-7;16 MiB;8 MiB
\-8;32 MiB;16 MiB
\-9;64 MiB;32 MiB
.TE
.RE
.PP
딕셔너리 크기 차이는 압축 프로그램 메모리 사용에 영향을 주지만, LZMA 유틸리티와 XZ 유틸리티에서 사용량이 늘어나는 다른 차이점이
있습니다:
.RS
.PP
.TS
tab(;);
c c c
c n n.
단계;xz;LZMA 유틸리티 4.32.x
\-0;3 MiB;없음
\-1;9 MiB;2 MiB
\-2;17 MiB;12 MiB
\-3;32 MiB;12 MiB
\-4;48 MiB;16 MiB
\-5;94 MiB;26 MiB
\-6;94 MiB;45 MiB
\-7;186 MiB;83 MiB
\-8;370 MiB;159 MiB
\-9;674 MiB;311 MiB
.TE
.RE
.PP
XZ 유틸리티의 기본 사전 설정 수준값은 \fB\-6\fP이지만 LZMA 유틸리티의 기본 사전 설정 수준값은 \fB\-7\fP입니다. 두 프로그램의
딕셔너리 메모리 기본 사용량은 8MiB입니다.
.
.SS "스트림 vs 비스트림 .lzma 파일"
파일을 압축하지 않은 크기는 \fB.lzma\fP 헤더에 저장합니다.  LZMA 유틸리티는 일반 파일을 압축할 때 압축하지 않은 파일의 크기를
저장합니다.  이 대신 압축하지 않은 크기를 '알 수 없음' 으로 저장하고 압축 해제 프로그램이 멈춰야 할 지점에
end\-of\-payload 마커를 사용하는 방법도 있습니다.  LZMA 유틸리티는 파이프로 들어온 입력과 같이 압축하지 않은 파일의
크기를 알 수 없을 때 이런 방식을 활용합니다.
.PP
\fBxz\fP는 \fB.lzma\fP 파일을 end\-of\-payload 마커의 유무와 관계없이 압축 해제 방식을 모두 지원하지만, \fBxz\fP로
만든 모든 \fB.lzma\fP 파일은 end\-of\-payload 마커를 사용하며, \fB.lzma\fP 헤더에 압축하지 않은 파일 크기를 '알 수
없음'으로 표기합니다. 이 방식은 드문 상황에서 문제를 야기할 수 있습니다.  예를 들면, 임베디드 장치의 \fB.lzma\fP 압축 해제
프로그램은 압축을 해제했을 때 크기를 알아야 동작합니다.  이 문제를 만나면, LZMA 유틸리티 또는 LZMA SDK를 활용하여
\&\fB.lzma\fP 파일에 압축 전 파일 크기 정보를 저장해야합니다.
.
.SS "지원하지 않는 .lzma 파일"
\&\fB.lzma\fP 형식은 \fIlc\fP 값을 8까지 받아들이며, \fIlp\fP 값은 4까지 받아들입니다. LZMA 유틸리티는 어떤 \fIlc\fP 값과
\fIlp\fP 값을 받아들이고도 압축을 해제할 수 있지만, 파일을 만들 때는 늘 \fBlc=3\fP 값과 \fBlp=0\fP 값을 활용합니다.  다른
\fIlc\fP 값과 \fIlp\fP 값으로의 파일 압축은 \fBxz\fP와 LZMA SDK에서만 가능합니다.
.PP
liblzma의 LZMA1 필터 구현체에서는 \fIlc\fP 값과 \fIlp\fP 값의 합이 4를 넘어가면 안됩니다.  그래서 \fB.lzma\fP
파일의 경우 이 제한을 넘어가면 \fBxz\fP로 압축을 해제할 수 없습니다.
.PP
LZMA 유틸리티는 2^\fIn\fP (2의 승수)크기를 지닌 딕셔너리를 가진 \fB.lzma\fP 파일만 만들지만 받아들이는 파일의 딕셔너리
크기는 어떤 크기든 상관 없습니다.  liblzma에서는 2^\fIn\fP, 2^\fIn\fP + 2^(\fIn\fP\-1) 딕셔너리 크기를 가진
\&\fB.lzma\fP 파일 만 받아들입니다. 이로 인해 \fB.lzma\fP 파일을 확인할 때 거짓 양성율이 늘어납니다.
.PP
모든 \fB.lzma\fP 파일을 liblzma 에서 받아들일 수 있도록 압축하기 때문에 이 제한이 실제로는 문제가 되지 않습니다.
.
.SS "뒤따라오는 쓰레기 값"
압축 해제할 때, LZMA 유틸리티는 \fB.lzma\fP 스트림 처음 부분 다음 나머지를 다 조용히 무시합니다.  대부분의 경우,
버그입니다.  LZMA 유틸리티에서 \fB.lzma\fP 결합 파일 압축 해제를 지원하지 않음을 의미하기도 합니다.
.PP
\&\fB.lzma\fP 스트림 처음부분 바로 다음에 데이터가 남아있을 경우, \fBxz\fP 에서는 \fB\-\-single\-stream\fP 옵션을 사용하지
않으면 깨진 파일로 간주합니다.  이 동작으로 하여금 뒤따라오는 쓰레기 값을 무시하도록 간주하는 애매한 스크립트 동작을 깰 수가
있습니다.
.
.SH 참고
.
.SS "출력 결과물이 달라짐"
압축하지 않은 입력 파일로부터 얻어낸 정확한 압축 출력 결과물은 압축 옵션이 완전히 동일하더라도 XZ 유틸리티의 버전에 따라 달라질 수
있습니다.  파일 형식에 영향을 주지 않고 인코더 그 자체를 개선(더 빠르게 하거나 더 나은 압축율로)하기 때문입니다.  XZ 유틸리티
버전이 동일하더라도 빌드 옵션을 달리하여 빌드 상태가 제각각인 경우 출력 결과물이 달라질 수 있습니다.
.PP
\fB\-\-rsyncable\fP 기능을 넣었을 경우 동일한 xz 버전에서 이전 파일과 새 파일로 별도로 압축하지 않는 한 결과 파일을 (두
파일이 서로 다른 파일이 아니므로) rsync 처리할 필요가 없습니다.  이 문제는 인코더 구현체 기능 개발이 끝나서 xz 버전이
다르더라도 안정적인 rsync 가능한 출력 결과물을 유지할 수 있을 때여야 해결할 수 있습니다.
.
.SS "내장 .xz 압축 해제 프로그램"
XZ 임베디드와 같은 내장 \fB.xz\fP 압축 해제 구현체는 지원 파일의 무결성 \fI검사\fP 형식을 \fInone\fP과 \fIcrc32\fP 이외의
설정으로 만들 필요가 없습니다.  기본값이 \fB\-\-check=crc64\fP일 경우에만, 임베디드 시스템에서 파일을 만들 때
\fB\-\-check=none\fP 또는 \fB\-\-check=crc32\fP 옵션을 사용해야합니다.
.PP
임베디드 시스템이 아니라면, 모든 \fB.xz\fP 형식 압축 해제 프로그램에서는 모든 \fI검사\fP 형식을 지원하거나, 일부 \fI검사\fP 방식을
지원하지 않는다면, 최소한, 무결성 검사로 검증하지 않고 압축을 해제할 수 있습니다.
.PP
XZ 임베디드는 BCJ 필터를 지원하지만, 기본 시작 오프셋만 지정할 수 있습니다.
.
.SH 예제
.
.SS 기본
\fIfoo\fP 파일을 기본 압축 수준 (\fB\-6\fP) 으로 \fIfoo.xz\fP 파일에 압축해 넣고, 압축 과정이 무사히 끝나면 \fIfoo\fP를
삭제합니다:
.RS
.PP
.nf
\f(CWxz foo\fP
.fi
.RE
.PP
\fIbar.xz\fP를 \fIbar\fP 에 압축을 해제한 후 압축 해제가 무사히 끝나도 \fIbar.xz\fP를 삭제하지 않습니다:
.RS
.PP
.nf
\f(CWxz \-dk bar.xz\fP
.fi
.RE
.PP
기본 사전 설정 \fB\-6\fP 보다는 느리지만, 압축 및 압축 해제시 메모리를 적게 차지(각각 48\ Mib, 5\MiB)는 \fB\-4e\fP
사전 설정(\fB\-4 \-\-extreme\fP)을 활용하여 \fIbaz.tar.xz\fP 파일을 만듭니다:
.RS
.PP
.nf
\f(CWtar cf \- baz | xz \-4e > baz.tar.xz\fP
.fi
.RE
.PP
압축 및 비압축 파일을 단일 명령으로 표준 출력에 압축해제할 수 있습니다:
.RS
.PP
.nf
\f(CWxz \-dcf a.txt b.txt.xz c.txt d.txt.lzma > abcd.txt\fP
.fi
.RE
.
.SS "다중 파일 병렬 압축"
GNU와 *BSD에서는 \fBfind\fP(1)  명령과 \fBxargs\fP(1)  명령으로 여러 파일의 압축을 병렬 처리할 수 있습니다:
.RS
.PP
.nf
\f(CWfind . \-type f \e! \-name '*.xz' \-print0 \e     | xargs \-0r \-P4 \-n16 xz \-T1\fP
.fi
.RE
.PP
\fBxargs\fP(1)  의 \fB\-P\fP 옵션으로 \fBxz\fP 프로세스의 병렬 처리 갯수를 지정합니다.  \fB\-n\fP 옵션의 최적 값은 압축할
파일 수에 달려있습니다.  압축할 파일이 몇개밖에 없다면 1이어야합니다. 파일이 수천 수만개 정도 된다면 \fBxargs\fP(1)  이
어쨌든지간에 만들어낼 \fBxz\fP 프로세스의 겟수를 100으로 하거나 아니면 적당한 값을 지정하여 줄이는게 좋습니다.
.PP
\fBxz\fP에 \fB\-T1\fP옵션을 지정하면 단일\-스레드 모드로 강제합니다. \fBxargs\fP(1)  에서 병렬 처리 갯수를 제어할 수 있기
때문입니다.
.
.SS "로봇 모드"
여러 파일을 압축한 후 저장할 바이트 용량을 계산합니다:
.RS
.PP
.nf
\f(CWxz \-\-robot \-\-list *.xz | awk '/^totals/{print $5\-$4}'\fP
.fi
.RE
.PP
이 스크립트에서는 충분히 최신의 \fBxz\fP 명령을 사용하는지 알아보려 합니다.  다음 \fBsh\fP(1)  스크립트에서는 \fBxz\fP 도구의
버전 번호가 최소한 5.0.0인지 여부를 검사합니다.  이 방식은 \fB\-\-robot\fP 옵션을 지원하지 않는 오래된 베타 버전과도 호환성이
있습니다:
.RS
.PP
.nf
\f(CWif ! eval "$(xz \-\-robot \-\-version 2> /dev/null)" ||         [ "$XZ_VERSION" \-lt 50000002 ]; then     echo "Your xz is too old." fi unset XZ_VERSION LIBLZMA_VERSION\fP
.fi
.RE
.PP
\fBXZ_OPT\fP 환경 변수로 압축 해제시 메뢰 사용량 한계를 설정하지만, 한계 값을 이미 설정했다면, 값을 늘리지 않습니다:
.RS
.PP
.nf
\f(CWNEWLIM=$((123 << 20))\ \ # 123 MiB OLDLIM=$(xz \-\-robot \-\-info\-memory | cut \-f3) if [ $OLDLIM \-eq 0 \-o $OLDLIM \-gt $NEWLIM ]; then     XZ_OPT="$XZ_OPT \-\-memlimit\-decompress=$NEWLIM"     export XZ_OPT fi\fP
.fi
.RE
.
.SS "개별 압축 필터 체인 설정"
개별 설정 필터 체인의 초단순 사용방식은 LZMA2 사전 설정 값을 별도로 설정하는 방식입니다.  사전 설정은 잠재적으로 쓸만한 압축
설정 조합만 다루기 때문에 꽤 쓸모가 있을 수도 있습니다.
.PP
\fB\-0\fP ... \fB\-9\fP 옵션의 설명에서 테이블의 CompCPU 컬럼과 \fB\-\-extreme\fP 옵션은 LZMA2 사전 설정을
개별적으로 맞췄을 때 쓸만할 수도 있습니다.  여기 관련내용을 테이블 둘로 정리해서 모아보았습니다.
.RS
.PP
.TS
tab(;);
c c
n n.
Preset;CompCPU
\-0;0
\-1;1
\-2;2
\-3;3
\-4;4
\-5;5
\-6;6
\-5e;7
\-6e;8
.TE
.RE
.PP
어떤 파일을 압축할 때 상당히 큰 딕셔너리(예: 32MiB)가 필요 하다는걸 알아채셨지만, \fBxz \-8\fP 명령이 압축할 때보다 더 빠른
속도로 압축하려 한다면, 더 큰 딕셔너리 사용을 위해 더 낮은 CompCPU 사전 설정 값(예: 1)으로 수정할 수 있습니다:
.RS
.PP
.nf
\f(CWxz \-\-lzma2=preset=1,dict=32MiB foo.tar\fP
.fi
.RE
.PP
각 파일에 대해, 위 명령은 압축율이 더 좋아지면서도 \fBxz \-6\fP보다 더 빨라집니다.  그러나, CompCPU 값을 낮게 유지하는
대신 큰 딕셔너리에서 일부 파일을 강조해야 합니다.  큰 딕셔너리가 대부분의 도움을 주는 매우 명백한 상황에서는 최소한 몇 메가바이트의
매우 유사한 각 파일이 아카이브에 들어갑니다.  딕셔너리 크기는 LZMA2가 연속으로 존재하는 각 파일의 유사성으로부터 얻는 장점을 취할
수 있을 때 일부 개별 파일보다 훨씬 더 커집니다.
.PP
압축 프로그램과 압축 해제 프로그램에서 메모리를 엄청 많이 사용해도 상관 없고, 파일을 수백 메가 바이트 메모리 용량을 활용하여
압축한다면, \fBxz \-9\fP 명령에 64MiB 용량을 초과하는 딕셔너리를 사용할 수 있게 하는 방법도 쓸만할 지도 모릅니다:
.RS
.PP
.nf
\f(CWxz \-vv \-\-lzma2=dict=192MiB big_foo.tar\fP
.fi
.RE
.PP
위 예제에서와 같이 \fB\-vv\fP (\fB\-\-verbose \-\-verbose\fP) 옵션을 사용하면 압축 및 압축 해제 과정에서 필요한 메모리
용량을 살펴보는데 요긴할 수 있습니다.  압축 해제한 파일 크기보다 더 큰 딕셔너리를 사용하면 불필요한 메모리 소모량이 발생하여 위
명령이 작은 파일에는 쓸모 없음을 기억하십시오.
.PP
때로는 압축 시간이 딱히 상관이 없을 수도 있습니다만, 압축 해제시 메모리 사용량을 적게 유지해야 할 수도 있습니다. 예를 들면,
임베디드 시스템에서 파일 압축을 해제할 수도 있습니다.  다음 명령의 경우 \fB\-6e\fP (\fB\-6 \-\-extreme\fP) 옵션을 기반
옵션을 사용하며 딕셔너리 크기를 64KiB만 사용하도록 제한합니다.  결과 파일은 XZ 임베디드(이게 \fB\-\-check=crc32\fP
옵션이 있는 이유)로 100KiB 메모리 용량을 활용하여 풀어낼 수 있습니다.
.RS
.PP
.nf
\f(CWxz \-\-check=crc32 \-\-lzma2=preset=6e,dict=64KiB foo\fP
.fi
.RE
.PP
가능한 한 수 바이트를 더 쥐어 짜내고 싶을 때, 리터럴 문맥 비트 수(\fIlc\fP)와 위치 비트 수(\fIpb\fP)를 조정하면 도움이 될
수도 있습니다.  리터럴 위치 비트 수(\fIlp\fP)를 조금 건드리는 것 또한 도움이 될 지도 모르겠지만 보통 \fIlc\fP 값과 \fIpb\fP
값이 더 중요합니다.  예를 들면, 소스 코드 저장 파일에는 US\-ASCII 텍스트가 대부분이기에, 다음과 같은 경우는 \fBxz \-6e\fP
명령을 실행했을 때부다는 아주 약간(거의 0.1% 수준) 작은 파일을 얻어낼 수도 있습니다(\fBlc=4\fP를 빼고도 시도해보십시오):
.RS
.PP
.nf
\f(CWxz \-\-lzma2=preset=6e,pb=0,lc=4 source_code.tar\fP
.fi
.RE
.PP
LZMA2와 다른 필터를 함께 사용하면 일부 파일 형식에 대해 압축율을 개선할 수 있습니다.  예를 들면 x86\-32 또는 x86\-64
공유 라이브러리를 x86 BCJ 필터를 활용하여 압축할 경우:
.RS
.PP
.nf
\f(CWxz \-\-x86 \-\-lzma2 libfoo.so\fP
.fi
.RE
.PP
참고로 필터 옵션의 순서는 상당히 중요합니다.  \fB\-\-x86\fP을 \fB\-\-lzma\fP 이전에 지정하면 \fBxz\fP에서 오류가 나는데,
LZMA2 다음에는 어떤 필터든 설정할 수 없고, 옵션 체인상 마지막 필터로 x86 BCJ 필터를 사용할 수 없기 때문입니다.
.PP
LZMA2와 델타 필터는 비트맵 그림에 최적의 결과를 가져다줄 수 있습니다.  PNG에 보통 안성맞춥인데, PNG에는 단순 델타 필터보단
약간 더 고급진 필터를 사용하지만, 실제 압축을 진행할 때는 Deflate를 사용하기 때문입니다.
.PP
예를 들어 이미지를 압축하지 않은 비압축 TIFF로 저장해야 하는 경우가 있습니다.  델타 필터의 거리 매개변수는 그림에서 픽셀당 바이트
수에 일치하도록 설정합니다.  예를 들면, 24비트 RGB 비트맵의 경우 \fBdist=3\fP 거리 매개변수 값을 설정해야 하며, LZMA2
압축시 3바이트 정렬을 따르도록 \fBpb=0\fP 값을 전달하는 방법도 바람직합니다.
.RS
.PP
.nf
\f(CWxz \-\-delta=dist=3 \-\-lzma2=pb=0 foo.tiff\fP
.fi
.RE
.PP
여러 이미지를 단일 아카이브로 넣고 싶다면(예: \fB.tar\fP), 모든 이미지에 대해 동일한 픽셀당 바이트 수가 들어가는 경우에도 델타
필터가 동작합니다.
.
.SH "추가 참조"
\fBxzdec\fP(1), \fBxzdiff\fP(1), \fBxzgrep\fP(1), \fBxzless\fP(1), \fBxzmore\fP(1),
\fBgzip\fP(1), \fBbzip2\fP(1), \fB7z\fP(1)
.PP
XZ 유틸리티: <https://tukaani.org/xz/>
.br
XZ 임베디드: <https://tukaani.org/xz/embedded.html>
.br
LZMA SDK: <https://7\-zip.org/sdk.html>
