.\"
.\" Original zdiff.1 for gzip: <PERSON><PERSON><PERSON><PERSON>
.\" Modifications for XZ Utils: <PERSON><PERSON>
.\"
.\" License: GNU GPLv2+
.\"
.\"*******************************************************************
.\"
.\" This file was generated with po4a. Translate the source file.
.\"
.\"*******************************************************************
.TH XZMORE 1 2013\-06\-30 Tukaani "XZ 유틸리티"
.SH 이름
xzmore, lzmore \- xz 압축 (텍스트) 파일 또는 lzma 압축 (텍스트) 파일을 봅니다
.SH 요약
\fBxzmore\fP [\fI<파일>...\fP]
.br
\fBlzmore\fP [\fI<파일>...\fP]
.SH 설명
\fBxzmore\fP 는 \fBxz\fP(1)  또는 \fBlzma\fP(1)  형식으로 압축한 텍스트 파일을 한 번에 한 화면만큼 소프트\-복제
터미널에 표시하는 필터입니다.
.PP
기본 \fBmore\fP 대신 다른 페이저 프로그램을 사용하려면,  \fBPAGER\fP 환경 변수에 원하는 프로그램 이름을 넣으십시오.
\fBlzmore\fP의 이름은 LZMA 유틸리티의 하위 호환성을 목적으로 제공합니다.
.TP 
\fBe\fP 또는 \fBq\fP
\-\-More\-\-(다음 파일: \fI<파일>\fP)  프롬프트가 뜨면, 이 명령은 \fBxzmore\fP를 빠져나가게 합니다.
.TP 
\fBs\fP
\-\-More\-\-(다음 파일: \fI<파일>\fP)  프롬프트가 뜨면, 이 명령은 \fBxzmore\fP에서 다음 파일로 건너뛰어 계속
실행하게 합니다.
.PP
파일 내용을 실제로 보는 동안 지원하는 키보드 명령 목록을 보려면, \fBmore\fP(1)  와 같은 사용하는 페이저의 설명서를
참고하십시오.
.SH "추가 참조"
\fBmore\fP(1), \fBxz\fP(1), \fBxzless\fP(1), \fBzmore\fP(1)
