.\"
.\" Original zdiff.1 for gzip: <PERSON><PERSON><PERSON><PERSON>
.\"
.\" Modifications for XZ Utils: <PERSON><PERSON>
.\"                             <PERSON>
.\"
.\" License: GNU GPLv2+
.\"
.\"*******************************************************************
.\"
.\" This file was generated with po4a. Translate the source file.
.\"
.\"*******************************************************************
.TH XZDIFF 1 2021\-06\-04 Tukaani "Utilitaires XZ"
.SH NOM
xzcmp, xzdiff, lzcmp, lzdiff \- Comparer des fichiers compressés.
.SH SYNOPSIS
\fBxzcmp\fP [\fIcmp_options\fP] \fIfichier1\fP [\fIfichier2\fP]
.br
\fBxzdiff\fP [\fIdiff_options\fP] \fIfichier1\fP [\fIfichier2\fP]
.br
\fBlzcmp\fP [\fIcmp_options\fP] \fIfichier1\fP [\fIfichier2\fP]
.br
\fBlzdiff\fP [\fIdiff_options\fP] \fIfichier1\fP [\fIfichier2\fP]
.SH DESCRIPTION
\fBxzcmp\fP and \fBxzdiff\fP invoke \fBcmp\fP(1)  or \fBdiff\fP(1)  on files compressed
with \fBxz\fP(1), \fBlzma\fP(1), \fBgzip\fP(1), \fBbzip2\fP(1), \fBlzop\fP(1), or
\fBzstd\fP(1).  All options specified are passed directly to \fBcmp\fP(1)  or
\fBdiff\fP(1).  If only one file is specified, then the files compared are
\fIfile1\fP (which must have a suffix of a supported compression format) and
\fIfile1\fP from which the compression format suffix has been stripped.  If two
files are specified, then they are uncompressed if necessary and fed to
\fBcmp\fP(1)  or \fBdiff\fP(1).  The exit status from \fBcmp\fP(1)  or \fBdiff\fP(1)  is
preserved unless a decompression error occurs; then exit status is 2.
.PP
Les noms \fBlzcmp\fP et \fBlzdiff\fP sont fournis pour des besoins de
rétrocompatibilité avec les Utilitaires LZMA.
.SH "VOIR AUSSI"
\fBcmp\fP(1), \fBdiff\fP(1), \fBxz\fP(1), \fBgzip\fP(1), \fBbzip2\fP(1), \fBlzop\fP(1),
\fBzstd\fP(1), \fBzdiff\fP(1)
.SH BOGUES
Les messages des programmes \fBcmp\fP(1) ou \fBdiff\fP(1) se réfèrent à des noms
de fichiers temporaires et non à ceux spécifiés.
