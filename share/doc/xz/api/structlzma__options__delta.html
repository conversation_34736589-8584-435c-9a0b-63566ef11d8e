<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.7"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma_options_delta Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>


<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.4.5</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.7 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">lzma_options_delta Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Options for the Delta filter.  
 <a href="structlzma__options__delta.html#details">More...</a></p>

<p><code>#include &lt;delta.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:af3f1ece7f8c472f4a794953b414c7cd7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="delta_8h.html#a04d84d7fa6cefdc219b6e2e96ff36fe1">lzma_delta_type</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__options__delta.html#af3f1ece7f8c472f4a794953b414c7cd7">type</a></td></tr>
<tr class="separator:af3f1ece7f8c472f4a794953b414c7cd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31b4b0b5a2462cb9433c2663b8a62790"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__options__delta.html#a31b4b0b5a2462cb9433c2663b8a62790">dist</a></td></tr>
<tr class="memdesc:a31b4b0b5a2462cb9433c2663b8a62790"><td class="mdescLeft">&#160;</td><td class="mdescRight">Delta distance.  <br /></td></tr>
<tr class="separator:a31b4b0b5a2462cb9433c2663b8a62790"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Options for the Delta filter. </p>
<p>These options are needed by both encoder and decoder. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="af3f1ece7f8c472f4a794953b414c7cd7" name="af3f1ece7f8c472f4a794953b414c7cd7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af3f1ece7f8c472f4a794953b414c7cd7">&#9670;&#160;</a></span>type</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="delta_8h.html#a04d84d7fa6cefdc219b6e2e96ff36fe1">lzma_delta_type</a> lzma_options_delta::type</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>For now, this must always be LZMA_DELTA_TYPE_BYTE. </p>

</div>
</div>
<a id="a31b4b0b5a2462cb9433c2663b8a62790" name="a31b4b0b5a2462cb9433c2663b8a62790"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a31b4b0b5a2462cb9433c2663b8a62790">&#9670;&#160;</a></span>dist</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_options_delta::dist</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Delta distance. </p>
<p>With the only currently supported type, LZMA_DELTA_TYPE_BYTE, the distance is as bytes.</p>
<p>Examples:</p><ul>
<li>16-bit stereo audio: distance = 4 bytes</li>
<li>24-bit RGB image data: distance = 3 bytes </li>
</ul>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>lzma/<a class="el" href="delta_8h.html">delta.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.7
</small></address>
</body>
</html>
