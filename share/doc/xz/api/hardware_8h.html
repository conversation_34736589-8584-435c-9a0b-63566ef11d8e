<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.7"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/hardware.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>


<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.4.5</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.7 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">hardware.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Hardware information.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a85363e453b34272a9f26c9fdffb041ee"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="hardware_8h.html#a85363e453b34272a9f26c9fdffb041ee">lzma_physmem</a> (void) lzma_nothrow</td></tr>
<tr class="memdesc:a85363e453b34272a9f26c9fdffb041ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the total amount of physical memory (RAM) in bytes.  <br /></td></tr>
<tr class="separator:a85363e453b34272a9f26c9fdffb041ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a22f7a882b7a4b741a226abf62bdf46ca"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="hardware_8h.html#a22f7a882b7a4b741a226abf62bdf46ca">lzma_cputhreads</a> (void) lzma_nothrow</td></tr>
<tr class="memdesc:a22f7a882b7a4b741a226abf62bdf46ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the number of processor cores or threads.  <br /></td></tr>
<tr class="separator:a22f7a882b7a4b741a226abf62bdf46ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Hardware information. </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead.</dd></dl>
<p>Since liblzma can consume a lot of system resources, it also provides ways to limit the resource usage. Applications linking against liblzma need to do the actual decisions how much resources to let liblzma to use. To ease making these decisions, liblzma provides functions to find out the relevant capabilities of the underlying hardware. Currently there is only a function to find out the amount of RAM, but in the future there will be also a function to detect how many concurrent threads the system can run.</p>
<dl class="section note"><dt>Note</dt><dd>On some operating systems, these function may temporarily load a shared library or open file descriptor(s) to find out the requested hardware information. Unless the application assumes that specific file descriptors are not touched by other threads, this should have no effect on thread safety. Possible operations involving file descriptors will restart the syscalls if they return EINTR. </dd></dl>
</div><h2 class="groupheader">Function Documentation</h2>
<a id="a85363e453b34272a9f26c9fdffb041ee" name="a85363e453b34272a9f26c9fdffb041ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a85363e453b34272a9f26c9fdffb041ee">&#9670;&#160;</a></span>lzma_physmem()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_physmem </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the total amount of physical memory (RAM) in bytes. </p>
<p>This function may be useful when determining a reasonable memory usage limit for decompressing or how much memory it is OK to use for compressing.</p>
<dl class="section return"><dt>Returns</dt><dd>On success, the total amount of physical memory in bytes is returned. If the amount of RAM cannot be determined, zero is returned. This can happen if an error occurs or if there is no code in liblzma to detect the amount of RAM on the specific operating system. </dd></dl>

</div>
</div>
<a id="a22f7a882b7a4b741a226abf62bdf46ca" name="a22f7a882b7a4b741a226abf62bdf46ca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a22f7a882b7a4b741a226abf62bdf46ca">&#9670;&#160;</a></span>lzma_cputhreads()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_cputhreads </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the number of processor cores or threads. </p>
<p>This function may be useful when determining how many threads to use. If the hardware supports more than one thread per CPU core, the number of hardware threads is returned if that information is available.</p>
<dl class="section return"><dt>Returns</dt><dd>On success, the number of available CPU threads or cores is returned. If this information isn't available or an error occurs, zero is returned. </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.7
</small></address>
</body>
</html>
