<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.7"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma_index_iter Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>


<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.4.5</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.7 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">lzma_index_iter Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Iterator to get information about Blocks and Streams.  
 <a href="structlzma__index__iter.html#details">More...</a></p>

<p><code>#include &lt;index.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:ad92e1ccf25428b1a207dc1bdb4a45b2c"><td class="memItemLeft" ><a id="ad92e1ccf25428b1a207dc1bdb4a45b2c" name="ad92e1ccf25428b1a207dc1bdb4a45b2c"></a>
struct {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49a96c22ae9b653df6263a7fe271807a"><td class="memItemLeft" >&#160;&#160;&#160;const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#a223a046bcf09077a6e720967682deeae">flags</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a49a96c22ae9b653df6263a7fe271807a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to Stream Flags.  <a href="structlzma__index__iter.html#a223a046bcf09077a6e720967682deeae">More...</a><br /></td></tr>
<tr class="separator:a49a96c22ae9b653df6263a7fe271807a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8d181cfac5188dd4a678d4115fbbcb2"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#a115a307dbc778a9de296376dc39c7b23">number</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:ae8d181cfac5188dd4a678d4115fbbcb2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Stream number in the lzma_index.  <a href="structlzma__index__iter.html#a115a307dbc778a9de296376dc39c7b23">More...</a><br /></td></tr>
<tr class="separator:ae8d181cfac5188dd4a678d4115fbbcb2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeff742c77bbdb23c7f31f6d179b47f31"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#abc6ee9be23e54f31aed07382c8caaf7c">block_count</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:aeff742c77bbdb23c7f31f6d179b47f31"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of Blocks in the Stream.  <a href="structlzma__index__iter.html#abc6ee9be23e54f31aed07382c8caaf7c">More...</a><br /></td></tr>
<tr class="separator:aeff742c77bbdb23c7f31f6d179b47f31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a286009ecb802eb150adb6c6ad1a50918"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#a426705df8dde4b094a42f91ea20a46ac">compressed_offset</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a286009ecb802eb150adb6c6ad1a50918"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compressed start offset of this Stream.  <a href="structlzma__index__iter.html#a426705df8dde4b094a42f91ea20a46ac">More...</a><br /></td></tr>
<tr class="separator:a286009ecb802eb150adb6c6ad1a50918"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1203268640946888544f0af52c19a66"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#abd374b748b4a42e122b90841709609bc">uncompressed_offset</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:af1203268640946888544f0af52c19a66"><td class="mdescLeft">&#160;</td><td class="mdescRight">Uncompressed start offset of this Stream.  <a href="structlzma__index__iter.html#abd374b748b4a42e122b90841709609bc">More...</a><br /></td></tr>
<tr class="separator:af1203268640946888544f0af52c19a66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e8737468f3bc6b779c78f45fe6c561c"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#a6e73b1f37e3fcf1e9491e4a53b2c52c7">compressed_size</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a6e8737468f3bc6b779c78f45fe6c561c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compressed size of this Stream.  <a href="structlzma__index__iter.html#a6e73b1f37e3fcf1e9491e4a53b2c52c7">More...</a><br /></td></tr>
<tr class="separator:a6e8737468f3bc6b779c78f45fe6c561c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a591f6115029d2655276d7709a604cddc"><td class="memItemLeft" >
&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<b>uncompressed_size</b>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a591f6115029d2655276d7709a604cddc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Uncompressed size of this Stream. <br /></td></tr>
<tr class="separator:a591f6115029d2655276d7709a604cddc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a081a8169e2014da2f8c8539120809cb5"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#a48cfc856f283fe00b0df37402e012818">padding</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a081a8169e2014da2f8c8539120809cb5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Size of Stream Padding after this Stream.  <a href="structlzma__index__iter.html#a48cfc856f283fe00b0df37402e012818">More...</a><br /></td></tr>
<tr class="separator:a081a8169e2014da2f8c8539120809cb5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad92e1ccf25428b1a207dc1bdb4a45b2c"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><b>stream</b>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:ad92e1ccf25428b1a207dc1bdb4a45b2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5092ff3be1ff1066585a9066dc5fd2b4"><td class="memItemLeft" ><a id="a5092ff3be1ff1066585a9066dc5fd2b4" name="a5092ff3be1ff1066585a9066dc5fd2b4"></a>
struct {&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f332b95b77bd86ea2b3f86fb30375bd"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#abe5333de53562189012d5ed084c0ef98">number_in_file</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a7f332b95b77bd86ea2b3f86fb30375bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Block number in the file.  <a href="structlzma__index__iter.html#abe5333de53562189012d5ed084c0ef98">More...</a><br /></td></tr>
<tr class="separator:a7f332b95b77bd86ea2b3f86fb30375bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc9701ec21240e8d2701afe55d742167"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#a26436e75d4c2b5dd8d1de24140d8003e">compressed_file_offset</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:abc9701ec21240e8d2701afe55d742167"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compressed start offset of this Block.  <a href="structlzma__index__iter.html#a26436e75d4c2b5dd8d1de24140d8003e">More...</a><br /></td></tr>
<tr class="separator:abc9701ec21240e8d2701afe55d742167"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae5593002adfeef61b9325b33a4eb687d"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#a2f3ecf341b5dc043e9673759b8ff47b9">uncompressed_file_offset</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:ae5593002adfeef61b9325b33a4eb687d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Uncompressed start offset of this Block.  <a href="structlzma__index__iter.html#a2f3ecf341b5dc043e9673759b8ff47b9">More...</a><br /></td></tr>
<tr class="separator:ae5593002adfeef61b9325b33a4eb687d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19a8e58670a1b22612428d5da300f2d2"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#a35a752d344ff5d35d2a858a20bd6e5e8">number_in_stream</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a19a8e58670a1b22612428d5da300f2d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Block number in this Stream.  <a href="structlzma__index__iter.html#a35a752d344ff5d35d2a858a20bd6e5e8">More...</a><br /></td></tr>
<tr class="separator:a19a8e58670a1b22612428d5da300f2d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4111d3bc81eb08bf42421814cd76ef33"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#a578bba553c43dc59a5e4032d4f6c89a3">compressed_stream_offset</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a4111d3bc81eb08bf42421814cd76ef33"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compressed start offset of this Block.  <a href="structlzma__index__iter.html#a578bba553c43dc59a5e4032d4f6c89a3">More...</a><br /></td></tr>
<tr class="separator:a4111d3bc81eb08bf42421814cd76ef33"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba1991fc46f4ebd0a3ac0bec43d36d56"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#a0fc4959fab08e1a6a4902c728c735a99">uncompressed_stream_offset</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:aba1991fc46f4ebd0a3ac0bec43d36d56"><td class="mdescLeft">&#160;</td><td class="mdescRight">Uncompressed start offset of this Block.  <a href="structlzma__index__iter.html#a0fc4959fab08e1a6a4902c728c735a99">More...</a><br /></td></tr>
<tr class="separator:aba1991fc46f4ebd0a3ac0bec43d36d56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8de4ea7cdf905303a31d851e222595db"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#aafc48408ed40060a84ecd66bae5e1b23">uncompressed_size</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a8de4ea7cdf905303a31d851e222595db"><td class="mdescLeft">&#160;</td><td class="mdescRight">Uncompressed size of this Block.  <a href="structlzma__index__iter.html#aafc48408ed40060a84ecd66bae5e1b23">More...</a><br /></td></tr>
<tr class="separator:a8de4ea7cdf905303a31d851e222595db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1eeb164c66c723607b3ee7ed68b4c22a"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#a9f4e405b9884be08e3a35bc06e3e15df">unpadded_size</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a1eeb164c66c723607b3ee7ed68b4c22a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unpadded size of this Block.  <a href="structlzma__index__iter.html#a9f4e405b9884be08e3a35bc06e3e15df">More...</a><br /></td></tr>
<tr class="separator:a1eeb164c66c723607b3ee7ed68b4c22a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a340f9c08cd05d0931468ab976050ffa2"><td class="memItemLeft" >&#160;&#160;&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;&#160;&#160;<a class="el" href="structlzma__index__iter.html#ae164ca3d7492dcf5883769c38baac30e">total_size</a>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="memdesc:a340f9c08cd05d0931468ab976050ffa2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Total compressed size.  <a href="structlzma__index__iter.html#ae164ca3d7492dcf5883769c38baac30e">More...</a><br /></td></tr>
<tr class="separator:a340f9c08cd05d0931468ab976050ffa2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5092ff3be1ff1066585a9066dc5fd2b4"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><b>block</b>&#160;</td><td class="memItemRight" valign="bottom"></td></tr>
<tr class="separator:a5092ff3be1ff1066585a9066dc5fd2b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Iterator to get information about Blocks and Streams. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="a223a046bcf09077a6e720967682deeae" name="a223a046bcf09077a6e720967682deeae"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a223a046bcf09077a6e720967682deeae">&#9670;&#160;</a></span>flags</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a>* lzma_index_iter::flags</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to Stream Flags. </p>
<p>This is NULL if Stream Flags have not been set for this Stream with <a class="el" href="index_8h.html#a79a19669237f19f0b11c9f3be80a62b4" title="Set the Stream Flags.">lzma_index_stream_flags()</a>. </p>

</div>
</div>
<a id="a115a307dbc778a9de296376dc39c7b23" name="a115a307dbc778a9de296376dc39c7b23"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a115a307dbc778a9de296376dc39c7b23">&#9670;&#160;</a></span>number</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::number</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Stream number in the lzma_index. </p>
<p>The first Stream is 1. </p>

</div>
</div>
<a id="abc6ee9be23e54f31aed07382c8caaf7c" name="abc6ee9be23e54f31aed07382c8caaf7c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc6ee9be23e54f31aed07382c8caaf7c">&#9670;&#160;</a></span>block_count</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::block_count</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Number of Blocks in the Stream. </p>
<p>If this is zero, the block structure below has undefined values. </p>

</div>
</div>
<a id="a426705df8dde4b094a42f91ea20a46ac" name="a426705df8dde4b094a42f91ea20a46ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a426705df8dde4b094a42f91ea20a46ac">&#9670;&#160;</a></span>compressed_offset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::compressed_offset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compressed start offset of this Stream. </p>
<p>The offset is relative to the beginning of the lzma_index (i.e. usually the beginning of the .xz file). </p>

</div>
</div>
<a id="abd374b748b4a42e122b90841709609bc" name="abd374b748b4a42e122b90841709609bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abd374b748b4a42e122b90841709609bc">&#9670;&#160;</a></span>uncompressed_offset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::uncompressed_offset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Uncompressed start offset of this Stream. </p>
<p>The offset is relative to the beginning of the lzma_index (i.e. usually the beginning of the .xz file). </p>

</div>
</div>
<a id="a6e73b1f37e3fcf1e9491e4a53b2c52c7" name="a6e73b1f37e3fcf1e9491e4a53b2c52c7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6e73b1f37e3fcf1e9491e4a53b2c52c7">&#9670;&#160;</a></span>compressed_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::compressed_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compressed size of this Stream. </p>
<p>This includes all headers except the possible Stream Padding after this Stream. </p>

</div>
</div>
<a id="aafc48408ed40060a84ecd66bae5e1b23" name="aafc48408ed40060a84ecd66bae5e1b23"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aafc48408ed40060a84ecd66bae5e1b23">&#9670;&#160;</a></span>uncompressed_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::uncompressed_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Uncompressed size of this Stream. </p>
<p>Uncompressed size of this Block.</p>
<p>You should pass this to the Block decoder if you will decode this Block. It will allow the Block decoder to validate the uncompressed size. </p>

</div>
</div>
<a id="a48cfc856f283fe00b0df37402e012818" name="a48cfc856f283fe00b0df37402e012818"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a48cfc856f283fe00b0df37402e012818">&#9670;&#160;</a></span>padding</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::padding</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Size of Stream Padding after this Stream. </p>
<p>If it hasn't been set with <a class="el" href="index_8h.html#a3ed82f96c688f3c953f6509b6f4e2ef3" title="Set the amount of Stream Padding.">lzma_index_stream_padding()</a>, this defaults to zero. Stream Padding is always a multiple of four bytes. </p>

</div>
</div>
<a id="abe5333de53562189012d5ed084c0ef98" name="abe5333de53562189012d5ed084c0ef98"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe5333de53562189012d5ed084c0ef98">&#9670;&#160;</a></span>number_in_file</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::number_in_file</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Block number in the file. </p>
<p>The first Block is 1. </p>

</div>
</div>
<a id="a26436e75d4c2b5dd8d1de24140d8003e" name="a26436e75d4c2b5dd8d1de24140d8003e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a26436e75d4c2b5dd8d1de24140d8003e">&#9670;&#160;</a></span>compressed_file_offset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::compressed_file_offset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compressed start offset of this Block. </p>
<p>This offset is relative to the beginning of the lzma_index (i.e. usually the beginning of the .xz file). Normally this is where you should seek in the .xz file to start decompressing this Block. </p>

</div>
</div>
<a id="a2f3ecf341b5dc043e9673759b8ff47b9" name="a2f3ecf341b5dc043e9673759b8ff47b9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2f3ecf341b5dc043e9673759b8ff47b9">&#9670;&#160;</a></span>uncompressed_file_offset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::uncompressed_file_offset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Uncompressed start offset of this Block. </p>
<p>This offset is relative to the beginning of the lzma_index (i.e. usually the beginning of the .xz file).</p>
<p>When doing random-access reading, it is possible that the target offset is not exactly at Block boundary. One will need to compare the target offset against uncompressed_file_offset or uncompressed_stream_offset, and possibly decode and throw away some amount of data before reaching the target offset. </p>

</div>
</div>
<a id="a35a752d344ff5d35d2a858a20bd6e5e8" name="a35a752d344ff5d35d2a858a20bd6e5e8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a35a752d344ff5d35d2a858a20bd6e5e8">&#9670;&#160;</a></span>number_in_stream</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::number_in_stream</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Block number in this Stream. </p>
<p>The first Block is 1. </p>

</div>
</div>
<a id="a578bba553c43dc59a5e4032d4f6c89a3" name="a578bba553c43dc59a5e4032d4f6c89a3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a578bba553c43dc59a5e4032d4f6c89a3">&#9670;&#160;</a></span>compressed_stream_offset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::compressed_stream_offset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compressed start offset of this Block. </p>
<p>This offset is relative to the beginning of the Stream containing this Block. </p>

</div>
</div>
<a id="a0fc4959fab08e1a6a4902c728c735a99" name="a0fc4959fab08e1a6a4902c728c735a99"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0fc4959fab08e1a6a4902c728c735a99">&#9670;&#160;</a></span>uncompressed_stream_offset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::uncompressed_stream_offset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Uncompressed start offset of this Block. </p>
<p>This offset is relative to the beginning of the Stream containing this Block. </p>

</div>
</div>
<a id="a9f4e405b9884be08e3a35bc06e3e15df" name="a9f4e405b9884be08e3a35bc06e3e15df"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f4e405b9884be08e3a35bc06e3e15df">&#9670;&#160;</a></span>unpadded_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::unpadded_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unpadded size of this Block. </p>
<p>You should pass this to the Block decoder if you will decode this Block. It will allow the Block decoder to validate the unpadded size. </p>

</div>
</div>
<a id="ae164ca3d7492dcf5883769c38baac30e" name="ae164ca3d7492dcf5883769c38baac30e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae164ca3d7492dcf5883769c38baac30e">&#9670;&#160;</a></span>total_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_iter::total_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Total compressed size. </p>
<p>This includes all headers and padding in this Block. This is useful if you need to know how many bytes the Block decoder will actually read. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>lzma/<a class="el" href="index_8h.html">index.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.7
</small></address>
</body>
</html>
