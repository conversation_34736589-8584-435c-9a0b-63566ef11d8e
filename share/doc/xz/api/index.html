<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.7"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): liblzma (XZ Utils)</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>


<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.4.5</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.7 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li class="current"><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div><div class="header">
  <div class="headertitle"><div class="title">liblzma (XZ Utils) </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>liblzma is a public domain general-purpose data compression library with a zlib-like API. The native file format is .xz, but also the old .lzma format and raw (no headers) streams are supported. Multiple compression algorithms (filters) are supported. Currently LZMA2 is the primary filter.</p>
<p>liblzma is part of XZ Utils <a href="https://tukaani.org/xz/">https://tukaani.org/xz/</a>. XZ Utils includes a gzip-like command line tool named xz and some other tools. XZ Utils is developed and maintained by Lasse Collin and Jia Tan.</p>
<p>Major parts of liblzma are based on Igor Pavlov's public domain LZMA SDK <a href="https://7-zip.org/sdk.html">https://7-zip.org/sdk.html</a>.</p>
<p>The SHA-256 implementation is based on the public domain code found from 7-Zip <a href="https://7-zip.org/">https://7-zip.org/</a>, which has a modified version of the public domain SHA-256 code found from Crypto++ <a href="https://www.cryptopp.com/">https://www.cryptopp.com/</a>. The SHA-256 code in Crypto++ was written by Kevin Springle and Wei Dai. </p>
</div></div><!-- PageDoc -->
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.7
</small></address>
</body>
</html>
