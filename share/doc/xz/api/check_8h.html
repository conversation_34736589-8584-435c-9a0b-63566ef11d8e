<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.7"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/check.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>


<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.4.5</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.7 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#enum-members">Enumerations</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">check.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Integrity checks.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:acd221ababe30230d9647aab469ad80cb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="check_8h.html#acd221ababe30230d9647aab469ad80cb">LZMA_CHECK_ID_MAX</a>&#160;&#160;&#160;15</td></tr>
<tr class="memdesc:acd221ababe30230d9647aab469ad80cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum valid Check ID.  <br /></td></tr>
<tr class="separator:acd221ababe30230d9647aab469ad80cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a379e931cf86351ab1d97896cda9abbe0"><td class="memItemLeft" align="right" valign="top"><a id="a379e931cf86351ab1d97896cda9abbe0" name="a379e931cf86351ab1d97896cda9abbe0"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_CHECK_SIZE_MAX</b>&#160;&#160;&#160;64</td></tr>
<tr class="memdesc:a379e931cf86351ab1d97896cda9abbe0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum size of a Check field. <br /></td></tr>
<tr class="separator:a379e931cf86351ab1d97896cda9abbe0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="enum-members" name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:a0a6100c719ac9aa49be3fdf7519e8c3f"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a> { <a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3fa7b9851d75abfabc08d7fc5b4aaeb6f20">LZMA_CHECK_NONE</a> = 0
, <a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3fa0be65014a40b5cb4ab32252b3709bef7">LZMA_CHECK_CRC32</a> = 1
, <a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3fa87b4b0697a1e1ccb6766dd5c2fa24afc">LZMA_CHECK_CRC64</a> = 4
, <a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3faf26a55ddd204a50ae87ec3432e7bc309">LZMA_CHECK_SHA256</a> = 10
 }</td></tr>
<tr class="memdesc:a0a6100c719ac9aa49be3fdf7519e8c3f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type of the integrity check (Check ID)  <a href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">More...</a><br /></td></tr>
<tr class="separator:a0a6100c719ac9aa49be3fdf7519e8c3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ae9391ed2acfad0ce9357b68c608f07d8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="check_8h.html#ae9391ed2acfad0ce9357b68c608f07d8">lzma_check_is_supported</a> (<a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a> check) lzma_nothrow lzma_attr_const</td></tr>
<tr class="memdesc:ae9391ed2acfad0ce9357b68c608f07d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Test if the given Check ID is supported.  <br /></td></tr>
<tr class="separator:ae9391ed2acfad0ce9357b68c608f07d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd3fda19575d9d4f864c626c02b7cb48"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="check_8h.html#afd3fda19575d9d4f864c626c02b7cb48">lzma_check_size</a> (<a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a> check) lzma_nothrow lzma_attr_const</td></tr>
<tr class="memdesc:afd3fda19575d9d4f864c626c02b7cb48"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the size of the Check field with the given Check ID.  <br /></td></tr>
<tr class="separator:afd3fda19575d9d4f864c626c02b7cb48"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a760b569cce91bdd01e4ce9d78823c96d"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="check_8h.html#a760b569cce91bdd01e4ce9d78823c96d">lzma_crc32</a> (const uint8_t *buf, size_t size, uint32_t crc) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a760b569cce91bdd01e4ce9d78823c96d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate CRC32.  <br /></td></tr>
<tr class="separator:a760b569cce91bdd01e4ce9d78823c96d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff2e74ce671b9f82a96adb549c68cea2"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="check_8h.html#aff2e74ce671b9f82a96adb549c68cea2">lzma_crc64</a> (const uint8_t *buf, size_t size, uint64_t crc) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:aff2e74ce671b9f82a96adb549c68cea2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate CRC64.  <br /></td></tr>
<tr class="separator:aff2e74ce671b9f82a96adb549c68cea2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d7c3ffabfd024485f03fa209536c746"><td class="memItemLeft" align="right" valign="top"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="check_8h.html#a8d7c3ffabfd024485f03fa209536c746">lzma_get_check</a> (const <a class="el" href="structlzma__stream.html">lzma_stream</a> *strm) lzma_nothrow</td></tr>
<tr class="memdesc:a8d7c3ffabfd024485f03fa209536c746"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the type of the integrity check.  <br /></td></tr>
<tr class="separator:a8d7c3ffabfd024485f03fa209536c746"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Integrity checks. </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead. </dd></dl>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="acd221ababe30230d9647aab469ad80cb" name="acd221ababe30230d9647aab469ad80cb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acd221ababe30230d9647aab469ad80cb">&#9670;&#160;</a></span>LZMA_CHECK_ID_MAX</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_CHECK_ID_MAX&#160;&#160;&#160;15</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum valid Check ID. </p>
<p>The .xz file format specification specifies 16 Check IDs (0-15). Some of them are only reserved, that is, no actual Check algorithm has been assigned. When decoding, liblzma still accepts unknown Check IDs for future compatibility. If a valid but unsupported Check ID is detected, liblzma can indicate a warning; see the flags LZMA_TELL_NO_CHECK, LZMA_TELL_UNSUPPORTED_CHECK, and LZMA_TELL_ANY_CHECK in <a class="el" href="container_8h.html" title="File formats.">container.h</a>. </p>

</div>
</div>
<h2 class="groupheader">Enumeration Type Documentation</h2>
<a id="a0a6100c719ac9aa49be3fdf7519e8c3f" name="a0a6100c719ac9aa49be3fdf7519e8c3f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0a6100c719ac9aa49be3fdf7519e8c3f">&#9670;&#160;</a></span>lzma_check</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Type of the integrity check (Check ID) </p>
<p>The .xz format supports multiple types of checks that are calculated from the uncompressed data. They vary in both speed and ability to detect errors. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a0a6100c719ac9aa49be3fdf7519e8c3fa7b9851d75abfabc08d7fc5b4aaeb6f20" name="a0a6100c719ac9aa49be3fdf7519e8c3fa7b9851d75abfabc08d7fc5b4aaeb6f20"></a>LZMA_CHECK_NONE&#160;</td><td class="fielddoc"><p>No Check is calculated.</p>
<p>Size of the Check field: 0 bytes </p>
</td></tr>
<tr><td class="fieldname"><a id="a0a6100c719ac9aa49be3fdf7519e8c3fa0be65014a40b5cb4ab32252b3709bef7" name="a0a6100c719ac9aa49be3fdf7519e8c3fa0be65014a40b5cb4ab32252b3709bef7"></a>LZMA_CHECK_CRC32&#160;</td><td class="fielddoc"><p>CRC32 using the polynomial from the IEEE 802.3 standard</p>
<p>Size of the Check field: 4 bytes </p>
</td></tr>
<tr><td class="fieldname"><a id="a0a6100c719ac9aa49be3fdf7519e8c3fa87b4b0697a1e1ccb6766dd5c2fa24afc" name="a0a6100c719ac9aa49be3fdf7519e8c3fa87b4b0697a1e1ccb6766dd5c2fa24afc"></a>LZMA_CHECK_CRC64&#160;</td><td class="fielddoc"><p>CRC64 using the polynomial from the ECMA-182 standard</p>
<p>Size of the Check field: 8 bytes </p>
</td></tr>
<tr><td class="fieldname"><a id="a0a6100c719ac9aa49be3fdf7519e8c3faf26a55ddd204a50ae87ec3432e7bc309" name="a0a6100c719ac9aa49be3fdf7519e8c3faf26a55ddd204a50ae87ec3432e7bc309"></a>LZMA_CHECK_SHA256&#160;</td><td class="fielddoc"><p>SHA-256</p>
<p>Size of the Check field: 32 bytes </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="ae9391ed2acfad0ce9357b68c608f07d8" name="ae9391ed2acfad0ce9357b68c608f07d8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae9391ed2acfad0ce9357b68c608f07d8">&#9670;&#160;</a></span>lzma_check_is_supported()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a> lzma_check_is_supported </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a>&#160;</td>
          <td class="paramname"><em>check</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Test if the given Check ID is supported. </p>
<p>LZMA_CHECK_NONE and LZMA_CHECK_CRC32 are always supported (even if liblzma is built with limited features).</p>
<dl class="section note"><dt>Note</dt><dd>It is safe to call this with a value that is not in the range [0, 15]; in that case the return value is always false.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">check</td><td>Check ID</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>lzma_bool:<ul>
<li>true if Check ID is supported by this liblzma build.</li>
<li>false otherwise. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="afd3fda19575d9d4f864c626c02b7cb48" name="afd3fda19575d9d4f864c626c02b7cb48"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afd3fda19575d9d4f864c626c02b7cb48">&#9670;&#160;</a></span>lzma_check_size()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_check_size </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a>&#160;</td>
          <td class="paramname"><em>check</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the size of the Check field with the given Check ID. </p>
<p>Although not all Check IDs have a check algorithm associated, the size of every Check is already frozen. This function returns the size (in bytes) of the Check field with the specified Check ID. The values are: { 0, 4, 4, 4, 8, 8, 8, 16, 16, 16, 32, 32, 32, 64, 64, 64 }</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">check</td><td>Check ID</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Size of the Check field in bytes. If the argument is not in the range [0, 15], UINT32_MAX is returned. </dd></dl>

</div>
</div>
<a id="a760b569cce91bdd01e4ce9d78823c96d" name="a760b569cce91bdd01e4ce9d78823c96d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a760b569cce91bdd01e4ce9d78823c96d">&#9670;&#160;</a></span>lzma_crc32()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_crc32 </td>
          <td>(</td>
          <td class="paramtype">const uint8_t *&#160;</td>
          <td class="paramname"><em>buf</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>size</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>crc</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Calculate CRC32. </p>
<p>Calculate CRC32 using the polynomial from the IEEE 802.3 standard.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">buf</td><td>Pointer to the input buffer </td></tr>
    <tr><td class="paramname">size</td><td>Size of the input buffer </td></tr>
    <tr><td class="paramname">crc</td><td>Previously returned CRC value. This is used to calculate the CRC of a big buffer in smaller chunks. Set to zero when starting a new calculation.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Updated CRC value, which can be passed to this function again to continue CRC calculation. </dd></dl>

</div>
</div>
<a id="aff2e74ce671b9f82a96adb549c68cea2" name="aff2e74ce671b9f82a96adb549c68cea2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aff2e74ce671b9f82a96adb549c68cea2">&#9670;&#160;</a></span>lzma_crc64()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_crc64 </td>
          <td>(</td>
          <td class="paramtype">const uint8_t *&#160;</td>
          <td class="paramname"><em>buf</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>size</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&#160;</td>
          <td class="paramname"><em>crc</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Calculate CRC64. </p>
<p>Calculate CRC64 using the polynomial from the ECMA-182 standard.</p>
<p>This function is used similarly to <a class="el" href="check_8h.html#a760b569cce91bdd01e4ce9d78823c96d" title="Calculate CRC32.">lzma_crc32()</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">buf</td><td>Pointer to the input buffer </td></tr>
    <tr><td class="paramname">size</td><td>Size of the input buffer </td></tr>
    <tr><td class="paramname">crc</td><td>Previously returned CRC value. This is used to calculate the CRC of a big buffer in smaller chunks. Set to zero when starting a new calculation.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Updated CRC value, which can be passed to this function again to continue CRC calculation. </dd></dl>

</div>
</div>
<a id="a8d7c3ffabfd024485f03fa209536c746" name="a8d7c3ffabfd024485f03fa209536c746"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8d7c3ffabfd024485f03fa209536c746">&#9670;&#160;</a></span>lzma_get_check()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a> lzma_get_check </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__stream.html">lzma_stream</a> *&#160;</td>
          <td class="paramname"><em>strm</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the type of the integrity check. </p>
<p>This function can be called only immediately after <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> has returned LZMA_NO_CHECK, LZMA_UNSUPPORTED_CHECK, or LZMA_GET_CHECK. Calling this function in any other situation has undefined behavior.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> meeting the above conditions.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Check ID in the <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a>, or undefined if called improperly. </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.7
</small></address>
</body>
</html>
