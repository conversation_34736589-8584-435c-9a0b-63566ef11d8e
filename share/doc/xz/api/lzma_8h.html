<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.7"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>


<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.4.5</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.7 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">lzma.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>The public API of liblzma data compression library.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;stddef.h&gt;</code><br />
<code>#include &lt;inttypes.h&gt;</code><br />
<code>#include &lt;limits.h&gt;</code><br />
<code>#include &quot;<a class="el" href="version_8h.html">lzma/version.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="base_8h.html">lzma/base.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="vli_8h.html">lzma/vli.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="check_8h.html">lzma/check.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="filter_8h.html">lzma/filter.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="bcj_8h.html">lzma/bcj.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="delta_8h.html">lzma/delta.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="lzma12_8h.html">lzma/lzma12.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="container_8h.html">lzma/container.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="stream__flags_8h.html">lzma/stream_flags.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="block_8h.html">lzma/block.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="index_8h.html">lzma/index.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="index__hash_8h.html">lzma/index_hash.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="hardware_8h.html">lzma/hardware.h</a>&quot;</code><br />
</div><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:acecee981dc3f28418f54035e20d7e578"><td class="memItemLeft" align="right" valign="top"><a id="acecee981dc3f28418f54035e20d7e578" name="acecee981dc3f28418f54035e20d7e578"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>UINT32_C</b>(n)&#160;&#160;&#160;n ## U</td></tr>
<tr class="separator:acecee981dc3f28418f54035e20d7e578"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6548785b283106d4d7bfc7a4ef87fc4"><td class="memItemLeft" align="right" valign="top"><a id="ac6548785b283106d4d7bfc7a4ef87fc4" name="ac6548785b283106d4d7bfc7a4ef87fc4"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>UINT64_C</b>(n)&#160;&#160;&#160;n ## UL</td></tr>
<tr class="separator:ac6548785b283106d4d7bfc7a4ef87fc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5eb23180f7cc12b7d6c04a8ec067fdd"><td class="memItemLeft" align="right" valign="top"><a id="ab5eb23180f7cc12b7d6c04a8ec067fdd" name="ab5eb23180f7cc12b7d6c04a8ec067fdd"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>UINT32_MAX</b>&#160;&#160;&#160;(UINT32_C(4294967295))</td></tr>
<tr class="separator:ab5eb23180f7cc12b7d6c04a8ec067fdd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a30654b4b67d97c42ca3f9b6052dda916"><td class="memItemLeft" align="right" valign="top"><a id="a30654b4b67d97c42ca3f9b6052dda916" name="a30654b4b67d97c42ca3f9b6052dda916"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>UINT64_MAX</b>&#160;&#160;&#160;(UINT64_C(18446744073709551615))</td></tr>
<tr class="separator:a30654b4b67d97c42ca3f9b6052dda916"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a55854429aba444dbb0bd8325fed4eece"><td class="memItemLeft" align="right" valign="top"><a id="a55854429aba444dbb0bd8325fed4eece" name="a55854429aba444dbb0bd8325fed4eece"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>lzma_nothrow</b></td></tr>
<tr class="separator:a55854429aba444dbb0bd8325fed4eece"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adbc570ea8654f0c96699e38249ac8032"><td class="memItemLeft" align="right" valign="top"><a id="adbc570ea8654f0c96699e38249ac8032" name="adbc570ea8654f0c96699e38249ac8032"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>lzma_attr_pure</b>&#160;&#160;&#160;lzma_attribute((__pure__))</td></tr>
<tr class="separator:adbc570ea8654f0c96699e38249ac8032"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aaafb664f89a525ff22530d61704556"><td class="memItemLeft" align="right" valign="top"><a id="a0aaafb664f89a525ff22530d61704556" name="a0aaafb664f89a525ff22530d61704556"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>lzma_attr_const</b>&#160;&#160;&#160;lzma_attribute((__const__))</td></tr>
<tr class="separator:a0aaafb664f89a525ff22530d61704556"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af15e147fcd7986ec4dd82660aec2b695"><td class="memItemLeft" align="right" valign="top"><a id="af15e147fcd7986ec4dd82660aec2b695" name="af15e147fcd7986ec4dd82660aec2b695"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>lzma_attr_warn_unused_result</b>&#160;&#160;&#160;		lzma_attribute((__warn_unused_result__))</td></tr>
<tr class="separator:af15e147fcd7986ec4dd82660aec2b695"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af828f75941ade54f8379bb847da37349"><td class="memItemLeft" align="right" valign="top"><a id="af828f75941ade54f8379bb847da37349" name="af828f75941ade54f8379bb847da37349"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_H_INTERNAL</b>&#160;&#160;&#160;1</td></tr>
<tr class="separator:af828f75941ade54f8379bb847da37349"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The public API of liblzma data compression library. </p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.7
</small></address>
</body>
</html>
