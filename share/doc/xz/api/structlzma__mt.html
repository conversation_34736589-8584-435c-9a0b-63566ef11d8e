<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.7"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma_mt Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>


<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.4.5</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.7 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li><a href="functions.html"><span>Data&#160;Fields</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">lzma_mt Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Multithreading options.  
 <a href="structlzma__mt.html#details">More...</a></p>

<p><code>#include &lt;container.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a1c2fe028f547bf58b48b5199557d9a9f"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__mt.html#a1c2fe028f547bf58b48b5199557d9a9f">flags</a></td></tr>
<tr class="memdesc:a1c2fe028f547bf58b48b5199557d9a9f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Flags.  <br /></td></tr>
<tr class="separator:a1c2fe028f547bf58b48b5199557d9a9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a881761f858dbda33c697e74acde0be70"><td class="memItemLeft" align="right" valign="top"><a id="a881761f858dbda33c697e74acde0be70" name="a881761f858dbda33c697e74acde0be70"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>threads</b></td></tr>
<tr class="memdesc:a881761f858dbda33c697e74acde0be70"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of worker threads to use. <br /></td></tr>
<tr class="separator:a881761f858dbda33c697e74acde0be70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a20cdc7865266ccb88da36a6e68f84d15"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__mt.html#a20cdc7865266ccb88da36a6e68f84d15">block_size</a></td></tr>
<tr class="memdesc:a20cdc7865266ccb88da36a6e68f84d15"><td class="mdescLeft">&#160;</td><td class="mdescRight">Encoder only: Maximum uncompressed size of a Block.  <br /></td></tr>
<tr class="separator:a20cdc7865266ccb88da36a6e68f84d15"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a298992bf7d2154d8dd814560219d10c2"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__mt.html#a298992bf7d2154d8dd814560219d10c2">timeout</a></td></tr>
<tr class="memdesc:a298992bf7d2154d8dd814560219d10c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Timeout to allow <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> to return early.  <br /></td></tr>
<tr class="separator:a298992bf7d2154d8dd814560219d10c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3883b5644752cdd15f01387d58dd050"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__mt.html#ab3883b5644752cdd15f01387d58dd050">preset</a></td></tr>
<tr class="memdesc:ab3883b5644752cdd15f01387d58dd050"><td class="mdescLeft">&#160;</td><td class="mdescRight">Encoder only: Compression preset.  <br /></td></tr>
<tr class="separator:ab3883b5644752cdd15f01387d58dd050"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad43a62ef2178c76405e5be0ece7a98b4"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__mt.html#ad43a62ef2178c76405e5be0ece7a98b4">filters</a></td></tr>
<tr class="memdesc:ad43a62ef2178c76405e5be0ece7a98b4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Encoder only: Filter chain (alternative to a preset)  <br /></td></tr>
<tr class="separator:ad43a62ef2178c76405e5be0ece7a98b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae38846e8aca5b20d2a86a2364283b730"><td class="memItemLeft" align="right" valign="top"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__mt.html#ae38846e8aca5b20d2a86a2364283b730">check</a></td></tr>
<tr class="memdesc:ae38846e8aca5b20d2a86a2364283b730"><td class="mdescLeft">&#160;</td><td class="mdescRight">Encoder only: Integrity check type.  <br /></td></tr>
<tr class="separator:ae38846e8aca5b20d2a86a2364283b730"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a7fb0c7c2db350e09e77477bc3c9509"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__mt.html#a5a7fb0c7c2db350e09e77477bc3c9509">memlimit_threading</a></td></tr>
<tr class="memdesc:a5a7fb0c7c2db350e09e77477bc3c9509"><td class="mdescLeft">&#160;</td><td class="mdescRight">Memory usage limit to reduce the number of threads.  <br /></td></tr>
<tr class="separator:a5a7fb0c7c2db350e09e77477bc3c9509"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5e0b530d4c572c7a2361aabbad656aa"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__mt.html#ab5e0b530d4c572c7a2361aabbad656aa">memlimit_stop</a></td></tr>
<tr class="memdesc:ab5e0b530d4c572c7a2361aabbad656aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Memory usage limit that should never be exceeded.  <br /></td></tr>
<tr class="separator:ab5e0b530d4c572c7a2361aabbad656aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Multithreading options. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="a1c2fe028f547bf58b48b5199557d9a9f" name="a1c2fe028f547bf58b48b5199557d9a9f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1c2fe028f547bf58b48b5199557d9a9f">&#9670;&#160;</a></span>flags</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_mt::flags</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Flags. </p>
<p>Set this to zero if no flags are wanted.</p>
<p>Encoder: No flags are currently supported.</p>
<p>Decoder: Bitwise-or of zero or more of the decoder flags:</p><ul>
<li>LZMA_TELL_NO_CHECK</li>
<li>LZMA_TELL_UNSUPPORTED_CHECK</li>
<li>LZMA_TELL_ANY_CHECK</li>
<li>LZMA_IGNORE_CHECK</li>
<li>LZMA_CONCATENATED</li>
<li>LZMA_FAIL_FAST </li>
</ul>

</div>
</div>
<a id="a20cdc7865266ccb88da36a6e68f84d15" name="a20cdc7865266ccb88da36a6e68f84d15"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a20cdc7865266ccb88da36a6e68f84d15">&#9670;&#160;</a></span>block_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_mt::block_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Encoder only: Maximum uncompressed size of a Block. </p>
<p>The encoder will start a new .xz Block every block_size bytes. Using LZMA_FULL_FLUSH or LZMA_FULL_BARRIER with <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> the caller may tell liblzma to start a new Block earlier.</p>
<p>With LZMA2, a recommended block size is 2-4 times the LZMA2 dictionary size. With very small dictionaries, it is recommended to use at least 1 MiB block size for good compression ratio, even if this is more than four times the dictionary size. Note that these are only recommendations for typical use cases; feel free to use other values. Just keep in mind that using a block size less than the LZMA2 dictionary size is waste of RAM.</p>
<p>Set this to 0 to let liblzma choose the block size depending on the compression options. For LZMA2 it will be 3*dict_size or 1 MiB, whichever is more.</p>
<p>For each thread, about 3 * block_size bytes of memory will be allocated. This may change in later liblzma versions. If so, the memory usage will probably be reduced, not increased. </p>

</div>
</div>
<a id="a298992bf7d2154d8dd814560219d10c2" name="a298992bf7d2154d8dd814560219d10c2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a298992bf7d2154d8dd814560219d10c2">&#9670;&#160;</a></span>timeout</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_mt::timeout</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Timeout to allow <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> to return early. </p>
<p>Multithreading can make liblzma consume input and produce output in a very bursty way: it may first read a lot of input to fill internal buffers, then no input or output occurs for a while.</p>
<p>In single-threaded mode, <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> won't return until it has either consumed all the input or filled the output buffer. If this is done in multithreaded mode, it may cause a call <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> to take even tens of seconds, which isn't acceptable in all applications.</p>
<p>To avoid very long blocking times in <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>, a timeout (in milliseconds) may be set here. If <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> would block longer than this number of milliseconds, it will return with LZMA_OK. Reasonable values are 100 ms or more. The xz command line tool uses 300 ms.</p>
<p>If long blocking times are acceptable, set timeout to a special value of 0. This will disable the timeout mechanism and will make <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> block until all the input is consumed or the output buffer has been filled.</p>
<dl class="section note"><dt>Note</dt><dd>Even with a timeout, <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> might sometimes take a long time to return. No timing guarantees are made. </dd></dl>

</div>
</div>
<a id="ab3883b5644752cdd15f01387d58dd050" name="ab3883b5644752cdd15f01387d58dd050"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab3883b5644752cdd15f01387d58dd050">&#9670;&#160;</a></span>preset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_mt::preset</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Encoder only: Compression preset. </p>
<p>The preset is set just like with <a class="el" href="container_8h.html#acbdad999c544872f0f5d242f0d1a4ed4" title="Initialize .xz Stream encoder using a preset number.">lzma_easy_encoder()</a>. The preset is ignored if filters below is non-NULL. </p>

</div>
</div>
<a id="ad43a62ef2178c76405e5be0ece7a98b4" name="ad43a62ef2178c76405e5be0ece7a98b4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad43a62ef2178c76405e5be0ece7a98b4">&#9670;&#160;</a></span>filters</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structlzma__filter.html">lzma_filter</a>* lzma_mt::filters</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Encoder only: Filter chain (alternative to a preset) </p>
<p>If this is NULL, the preset above is used. Otherwise the preset is ignored and the filter chain specified here is used. </p>

</div>
</div>
<a id="ae38846e8aca5b20d2a86a2364283b730" name="ae38846e8aca5b20d2a86a2364283b730"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae38846e8aca5b20d2a86a2364283b730">&#9670;&#160;</a></span>check</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a> lzma_mt::check</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Encoder only: Integrity check type. </p>
<p>See <a class="el" href="check_8h.html" title="Integrity checks.">check.h</a> for available checks. The xz command line tool defaults to LZMA_CHECK_CRC64, which is a good choice if you are unsure. </p>

</div>
</div>
<a id="a5a7fb0c7c2db350e09e77477bc3c9509" name="a5a7fb0c7c2db350e09e77477bc3c9509"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5a7fb0c7c2db350e09e77477bc3c9509">&#9670;&#160;</a></span>memlimit_threading</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_mt::memlimit_threading</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Memory usage limit to reduce the number of threads. </p>
<p>Encoder: Ignored.</p>
<p>Decoder:</p>
<p>If the number of threads has been set so high that more than memlimit_threading bytes of memory would be needed, the number of threads will be reduced so that the memory usage will not exceed memlimit_threading bytes. However, if memlimit_threading cannot be met even in single-threaded mode, then decoding will continue in single-threaded mode and memlimit_threading may be exceeded even by a large amount. That is, memlimit_threading will never make <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> return LZMA_MEMLIMIT_ERROR. To truly cap the memory usage, see memlimit_stop below.</p>
<p>Setting memlimit_threading to UINT64_MAX or a similar huge value means that liblzma is allowed to keep the whole compressed file and the whole uncompressed file in memory in addition to the memory needed by the decompressor data structures used by each thread! In other words, a reasonable value limit must be set here or it will cause problems sooner or later. If you have no idea what a reasonable value could be, try <a class="el" href="hardware_8h.html#a85363e453b34272a9f26c9fdffb041ee" title="Get the total amount of physical memory (RAM) in bytes.">lzma_physmem()</a> / 4 as a starting point. Setting this limit will never prevent decompression of a file; this will only reduce the number of threads.</p>
<p>If memlimit_threading is greater than memlimit_stop, then the value of memlimit_stop will be used for both. </p>

</div>
</div>
<a id="ab5e0b530d4c572c7a2361aabbad656aa" name="ab5e0b530d4c572c7a2361aabbad656aa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab5e0b530d4c572c7a2361aabbad656aa">&#9670;&#160;</a></span>memlimit_stop</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_mt::memlimit_stop</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Memory usage limit that should never be exceeded. </p>
<p>Encoder: Ignored.</p>
<p>Decoder: If decompressing will need more than this amount of memory even in the single-threaded mode, then <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> will return LZMA_MEMLIMIT_ERROR. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>lzma/<a class="el" href="container_8h.html">container.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.7
</small></address>
</body>
</html>
