<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.7"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>


<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.4.5</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.7 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li class="current"><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li class="current"><a href="#index_l"><span>l</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented functions with links to the documentation:</div>

<h3><a id="index_l" name="index_l"></a>- l -</h3><ul>
<li>lzma_alone_decoder()&#160;:&#160;<a class="el" href="container_8h.html#a5f43c3a1035e5a226dcd298f4162b861">container.h</a></li>
<li>lzma_alone_encoder()&#160;:&#160;<a class="el" href="container_8h.html#a26fcc5bccdf3f862caa4c992d01e1a72">container.h</a></li>
<li>lzma_auto_decoder()&#160;:&#160;<a class="el" href="container_8h.html#a21cbebf2771617bb1e956385cfb353e3">container.h</a></li>
<li>lzma_block_buffer_bound()&#160;:&#160;<a class="el" href="block_8h.html#a58ff73e2572b529f48cc590bfffe5b4f">block.h</a></li>
<li>lzma_block_buffer_decode()&#160;:&#160;<a class="el" href="block_8h.html#a0c6eb869d91b08f68648b1aa7a32ee9f">block.h</a></li>
<li>lzma_block_buffer_encode()&#160;:&#160;<a class="el" href="block_8h.html#af415fa5130ab64e8760e9c39e856fa54">block.h</a></li>
<li>lzma_block_compressed_size()&#160;:&#160;<a class="el" href="block_8h.html#a6c3e102d76db06a07126a569abc6e2bc">block.h</a></li>
<li>lzma_block_decoder()&#160;:&#160;<a class="el" href="block_8h.html#aa92c73b2a228efe921fa2376aa7adc92">block.h</a></li>
<li>lzma_block_encoder()&#160;:&#160;<a class="el" href="block_8h.html#a2218a49025a0b44f9a6f9d6d24359359">block.h</a></li>
<li>lzma_block_header_decode()&#160;:&#160;<a class="el" href="block_8h.html#a7f5487c21a7b36a8bd17be36074d43c9">block.h</a></li>
<li>lzma_block_header_encode()&#160;:&#160;<a class="el" href="block_8h.html#a0eedbd6331d5708ea963260e6f2a92d0">block.h</a></li>
<li>lzma_block_header_size()&#160;:&#160;<a class="el" href="block_8h.html#ae9b47abc872d0b02c2da9d3fa5a7dacd">block.h</a></li>
<li>lzma_block_total_size()&#160;:&#160;<a class="el" href="block_8h.html#a694424f9dfdd5151e01debac1c501fa9">block.h</a></li>
<li>lzma_block_uncomp_encode()&#160;:&#160;<a class="el" href="block_8h.html#a5a260f634ccd5f54fb98f570d8d92d8c">block.h</a></li>
<li>lzma_block_unpadded_size()&#160;:&#160;<a class="el" href="block_8h.html#a412d5605280fa29befae1b89e344bf30">block.h</a></li>
<li>lzma_check_is_supported()&#160;:&#160;<a class="el" href="check_8h.html#ae9391ed2acfad0ce9357b68c608f07d8">check.h</a></li>
<li>lzma_check_size()&#160;:&#160;<a class="el" href="check_8h.html#afd3fda19575d9d4f864c626c02b7cb48">check.h</a></li>
<li>lzma_code()&#160;:&#160;<a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957">base.h</a></li>
<li>lzma_cputhreads()&#160;:&#160;<a class="el" href="hardware_8h.html#a22f7a882b7a4b741a226abf62bdf46ca">hardware.h</a></li>
<li>lzma_crc32()&#160;:&#160;<a class="el" href="check_8h.html#a760b569cce91bdd01e4ce9d78823c96d">check.h</a></li>
<li>lzma_crc64()&#160;:&#160;<a class="el" href="check_8h.html#aff2e74ce671b9f82a96adb549c68cea2">check.h</a></li>
<li>lzma_easy_buffer_encode()&#160;:&#160;<a class="el" href="container_8h.html#ac5e71f2711b57391186671967435faf2">container.h</a></li>
<li>lzma_easy_decoder_memusage()&#160;:&#160;<a class="el" href="container_8h.html#a3562055d26c18fad067a7c7516eaddf5">container.h</a></li>
<li>lzma_easy_encoder()&#160;:&#160;<a class="el" href="container_8h.html#acbdad999c544872f0f5d242f0d1a4ed4">container.h</a></li>
<li>lzma_easy_encoder_memusage()&#160;:&#160;<a class="el" href="container_8h.html#a62c853cf7dbf008bdbd97b2685c3eabf">container.h</a></li>
<li>lzma_end()&#160;:&#160;<a class="el" href="base_8h.html#a854ff37464ae1225febf14db1af43308">base.h</a></li>
<li>lzma_file_info_decoder()&#160;:&#160;<a class="el" href="index_8h.html#a7c5d77cf8532d95977d4571a1eb0a222">index.h</a></li>
<li>lzma_filter_decoder_is_supported()&#160;:&#160;<a class="el" href="filter_8h.html#acab0c67bf5b3a76f2b474c8e1da98938">filter.h</a></li>
<li>lzma_filter_encoder_is_supported()&#160;:&#160;<a class="el" href="filter_8h.html#a3db3c36cd6e57658a74c53e4daa2bef6">filter.h</a></li>
<li>lzma_filter_flags_decode()&#160;:&#160;<a class="el" href="filter_8h.html#a4cba9a4c658cce0ff01fd102b31ea1a7">filter.h</a></li>
<li>lzma_filter_flags_encode()&#160;:&#160;<a class="el" href="filter_8h.html#a96f23309bc21398fece18c00ebe7db98">filter.h</a></li>
<li>lzma_filter_flags_size()&#160;:&#160;<a class="el" href="filter_8h.html#a996c9c21840ed54e37bd1f664a79d940">filter.h</a></li>
<li>lzma_filters_copy()&#160;:&#160;<a class="el" href="filter_8h.html#a611fe1176eeeda187b1bd8aef45040aa">filter.h</a></li>
<li>lzma_filters_free()&#160;:&#160;<a class="el" href="filter_8h.html#ae06979d219897f5f4c29cbc7a96a8892">filter.h</a></li>
<li>lzma_filters_update()&#160;:&#160;<a class="el" href="filter_8h.html#a4a8fd969df001e449ebe4421ab33bba5">filter.h</a></li>
<li>lzma_get_check()&#160;:&#160;<a class="el" href="check_8h.html#a8d7c3ffabfd024485f03fa209536c746">check.h</a></li>
<li>lzma_get_progress()&#160;:&#160;<a class="el" href="base_8h.html#ab6447cd68eeecbd6b88f21daeb8ce751">base.h</a></li>
<li>lzma_index_append()&#160;:&#160;<a class="el" href="index_8h.html#ac347747eb933c7c408e6c801b33becc3">index.h</a></li>
<li>lzma_index_block_count()&#160;:&#160;<a class="el" href="index_8h.html#add1a8c506f67dbc19cae6747107e3bec">index.h</a></li>
<li>lzma_index_buffer_decode()&#160;:&#160;<a class="el" href="index_8h.html#a028b8b8d59a413f9682eea1269a6ae8b">index.h</a></li>
<li>lzma_index_buffer_encode()&#160;:&#160;<a class="el" href="index_8h.html#add1ef06dec8a26d08ae8651cff0fd8d6">index.h</a></li>
<li>lzma_index_cat()&#160;:&#160;<a class="el" href="index_8h.html#abc4db36b4bd67af01819be9dd045c34a">index.h</a></li>
<li>lzma_index_checks()&#160;:&#160;<a class="el" href="index_8h.html#af8d6528a04241841bd0a4322b0c57eaa">index.h</a></li>
<li>lzma_index_decoder()&#160;:&#160;<a class="el" href="index_8h.html#abb56fd1d5914f8900ece7b88b78e5e23">index.h</a></li>
<li>lzma_index_dup()&#160;:&#160;<a class="el" href="index_8h.html#a5161e3f67156577882e1d95dcb57e33e">index.h</a></li>
<li>lzma_index_encoder()&#160;:&#160;<a class="el" href="index_8h.html#a6800d70f3b2afca085496460cd03211d">index.h</a></li>
<li>lzma_index_end()&#160;:&#160;<a class="el" href="index_8h.html#a0c2d0009f07fc315d5ac89e4bcd25abd">index.h</a></li>
<li>lzma_index_file_size()&#160;:&#160;<a class="el" href="index_8h.html#ac875ed47d35385e5dac461b25c5ea1c9">index.h</a></li>
<li>lzma_index_hash_append()&#160;:&#160;<a class="el" href="index__hash_8h.html#a2bdbe4f0b5fa2fadb7528447feaaa97f">index_hash.h</a></li>
<li>lzma_index_hash_decode()&#160;:&#160;<a class="el" href="index__hash_8h.html#a891eb955284c9117155f92eb0ddba44c">index_hash.h</a></li>
<li>lzma_index_hash_end()&#160;:&#160;<a class="el" href="index__hash_8h.html#a7dacb41b9ec1c8df5d33dfdae97743b3">index_hash.h</a></li>
<li>lzma_index_hash_init()&#160;:&#160;<a class="el" href="index__hash_8h.html#aaafae4967a4a266d97dc34a98bfcabfb">index_hash.h</a></li>
<li>lzma_index_hash_size()&#160;:&#160;<a class="el" href="index__hash_8h.html#a0f8ab3b57b117f9547866156755c917f">index_hash.h</a></li>
<li>lzma_index_init()&#160;:&#160;<a class="el" href="index_8h.html#a0850627d011111326d4278a3e2edec25">index.h</a></li>
<li>lzma_index_iter_init()&#160;:&#160;<a class="el" href="index_8h.html#aa78f02f18ed29d289a6ef37b8ea98a21">index.h</a></li>
<li>lzma_index_iter_locate()&#160;:&#160;<a class="el" href="index_8h.html#ac4f56df9d210712e5d7add5502c9eb93">index.h</a></li>
<li>lzma_index_iter_next()&#160;:&#160;<a class="el" href="index_8h.html#af428522e1b3eef137c65c5a01f766e0e">index.h</a></li>
<li>lzma_index_iter_rewind()&#160;:&#160;<a class="el" href="index_8h.html#ae81438be8deff4894b104e65d8acdd24">index.h</a></li>
<li>lzma_index_memusage()&#160;:&#160;<a class="el" href="index_8h.html#a880def3727ecdd7f242807083d228fc5">index.h</a></li>
<li>lzma_index_memused()&#160;:&#160;<a class="el" href="index_8h.html#a4118805ac7be6618aca6d6d7e5e4dde7">index.h</a></li>
<li>lzma_index_size()&#160;:&#160;<a class="el" href="index_8h.html#a669ed1a82b1941217cfbb07e7826afc2">index.h</a></li>
<li>lzma_index_stream_count()&#160;:&#160;<a class="el" href="index_8h.html#afd159a765b09b0cf79186069a848d07e">index.h</a></li>
<li>lzma_index_stream_flags()&#160;:&#160;<a class="el" href="index_8h.html#a79a19669237f19f0b11c9f3be80a62b4">index.h</a></li>
<li>lzma_index_stream_padding()&#160;:&#160;<a class="el" href="index_8h.html#a3ed82f96c688f3c953f6509b6f4e2ef3">index.h</a></li>
<li>lzma_index_stream_size()&#160;:&#160;<a class="el" href="index_8h.html#af3630369b43c9ccc906065d759b49663">index.h</a></li>
<li>lzma_index_total_size()&#160;:&#160;<a class="el" href="index_8h.html#a7829942b83ee1fa5b6443cc248b81041">index.h</a></li>
<li>lzma_index_uncompressed_size()&#160;:&#160;<a class="el" href="index_8h.html#a620fe6317f1f9d7af9cc27c748bf07d6">index.h</a></li>
<li>lzma_lzip_decoder()&#160;:&#160;<a class="el" href="container_8h.html#a97689f5709e0db1e2dac450f5ce4e5eb">container.h</a></li>
<li>lzma_lzma_preset()&#160;:&#160;<a class="el" href="lzma12_8h.html#aa62c28944fe3575653a4c25780400d77">lzma12.h</a></li>
<li>lzma_memlimit_get()&#160;:&#160;<a class="el" href="base_8h.html#ac871bc2ead5d482c6d6b3d51bfec365c">base.h</a></li>
<li>lzma_memlimit_set()&#160;:&#160;<a class="el" href="base_8h.html#afc49d4cf75b73128a167df3407505f7b">base.h</a></li>
<li>lzma_memusage()&#160;:&#160;<a class="el" href="base_8h.html#a418b210cf206782a73cd9de7dc27f670">base.h</a></li>
<li>lzma_mf_is_supported()&#160;:&#160;<a class="el" href="lzma12_8h.html#aefba1f7214ddcf8cd408a0702e8642b5">lzma12.h</a></li>
<li>lzma_microlzma_decoder()&#160;:&#160;<a class="el" href="container_8h.html#aa8372dae3e7c907c36f7bb5426aeacdf">container.h</a></li>
<li>lzma_microlzma_encoder()&#160;:&#160;<a class="el" href="container_8h.html#abfc8f11acf837b167aa94b7071b54c30">container.h</a></li>
<li>lzma_mode_is_supported()&#160;:&#160;<a class="el" href="lzma12_8h.html#ad1add1c2600fdbb3d737e4fb3465dfcb">lzma12.h</a></li>
<li>lzma_physmem()&#160;:&#160;<a class="el" href="hardware_8h.html#a85363e453b34272a9f26c9fdffb041ee">hardware.h</a></li>
<li>lzma_properties_decode()&#160;:&#160;<a class="el" href="filter_8h.html#a88d2e864b2039ac82802cc202278d478">filter.h</a></li>
<li>lzma_properties_encode()&#160;:&#160;<a class="el" href="filter_8h.html#a8e00887086df5a44084ac22e48415de3">filter.h</a></li>
<li>lzma_properties_size()&#160;:&#160;<a class="el" href="filter_8h.html#aee038818cf7bbe044c3f7a7c86998c1b">filter.h</a></li>
<li>lzma_raw_buffer_decode()&#160;:&#160;<a class="el" href="filter_8h.html#a3b942df507e4f9a6d7525e5a4c6864e5">filter.h</a></li>
<li>lzma_raw_buffer_encode()&#160;:&#160;<a class="el" href="filter_8h.html#a226724ab3391b410281fdf656cc7c432">filter.h</a></li>
<li>lzma_raw_decoder()&#160;:&#160;<a class="el" href="filter_8h.html#ae77b3b6c5eccd9d77bbafef0a8a203c1">filter.h</a></li>
<li>lzma_raw_decoder_memusage()&#160;:&#160;<a class="el" href="filter_8h.html#a58511249ae9206d7de7c5d1f05842297">filter.h</a></li>
<li>lzma_raw_encoder()&#160;:&#160;<a class="el" href="filter_8h.html#a2368e4129032345eb0738b0c6e085703">filter.h</a></li>
<li>lzma_raw_encoder_memusage()&#160;:&#160;<a class="el" href="filter_8h.html#a730f9391e85a5979bcd1b32643ae7176">filter.h</a></li>
<li>lzma_str_from_filters()&#160;:&#160;<a class="el" href="filter_8h.html#a7deeb86ef59a9111b8033681290e0fb0">filter.h</a></li>
<li>lzma_str_list_filters()&#160;:&#160;<a class="el" href="filter_8h.html#ab51585b68796ce0270f87e615b923809">filter.h</a></li>
<li>lzma_str_to_filters()&#160;:&#160;<a class="el" href="filter_8h.html#aa042cf11749bc2183b27de1c3142da30">filter.h</a></li>
<li>lzma_stream_buffer_bound()&#160;:&#160;<a class="el" href="container_8h.html#a66d4366a47b8332bff2a512f44f5c45e">container.h</a></li>
<li>lzma_stream_buffer_decode()&#160;:&#160;<a class="el" href="container_8h.html#aa58f237f6cea97ef0eb9bf5c37a3008d">container.h</a></li>
<li>lzma_stream_buffer_encode()&#160;:&#160;<a class="el" href="container_8h.html#a6e645ccaeace3b13a6981e03c6e190ad">container.h</a></li>
<li>lzma_stream_decoder()&#160;:&#160;<a class="el" href="container_8h.html#a02b7683ef98d8049788961370a8b28c0">container.h</a></li>
<li>lzma_stream_decoder_mt()&#160;:&#160;<a class="el" href="container_8h.html#a7179d178e6430c10e2006a467921e98e">container.h</a></li>
<li>lzma_stream_encoder()&#160;:&#160;<a class="el" href="container_8h.html#a1a97aec94c9fedd7646cfa51c4f4cd52">container.h</a></li>
<li>lzma_stream_encoder_mt()&#160;:&#160;<a class="el" href="container_8h.html#a3f8793518711ee84d1abf12ea3aaba42">container.h</a></li>
<li>lzma_stream_encoder_mt_memusage()&#160;:&#160;<a class="el" href="container_8h.html#ad7cf41496d77f4d346e006b26ed8e101">container.h</a></li>
<li>lzma_stream_flags_compare()&#160;:&#160;<a class="el" href="stream__flags_8h.html#a3e25ca4205021302882a696283d45263">stream_flags.h</a></li>
<li>lzma_stream_footer_decode()&#160;:&#160;<a class="el" href="stream__flags_8h.html#aa92a383f85753bb79ee23227fa68186c">stream_flags.h</a></li>
<li>lzma_stream_footer_encode()&#160;:&#160;<a class="el" href="stream__flags_8h.html#a438249a75ea8da952a7474b92bfe7b7a">stream_flags.h</a></li>
<li>lzma_stream_header_decode()&#160;:&#160;<a class="el" href="stream__flags_8h.html#ae03198e464f0d296e601ff841e100805">stream_flags.h</a></li>
<li>lzma_stream_header_encode()&#160;:&#160;<a class="el" href="stream__flags_8h.html#a2ebb8d6dff23daeb3de398913b845eff">stream_flags.h</a></li>
<li>lzma_version_number()&#160;:&#160;<a class="el" href="version_8h.html#a72f929c9b9e8e730b790b3f8c80c3c80">version.h</a></li>
<li>lzma_version_string()&#160;:&#160;<a class="el" href="version_8h.html#a8998c1d8b4b5c2c1218bdfd58fdb1baa">version.h</a></li>
<li>lzma_vli_decode()&#160;:&#160;<a class="el" href="vli_8h.html#a7b7d50e1074e0e2bcd81c29a5f7461c7">vli.h</a></li>
<li>lzma_vli_encode()&#160;:&#160;<a class="el" href="vli_8h.html#a50bbb77e9ec3b72c25586aa700c20970">vli.h</a></li>
<li>lzma_vli_size()&#160;:&#160;<a class="el" href="vli_8h.html#a8d53e0b69934b43da8721fa6f1e8cc4f">vli.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.7
</small></address>
</body>
</html>
