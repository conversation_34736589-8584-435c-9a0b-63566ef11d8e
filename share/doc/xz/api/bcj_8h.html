<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.7"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/bcj.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>


<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.4.5</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.7 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#define-members">Macros</a>  </div>
  <div class="headertitle"><div class="title">bcj.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Branch/Call/Jump conversion filters.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__options__bcj.html">lzma_options_bcj</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Options for BCJ filters.  <a href="structlzma__options__bcj.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:aa9eac1f580ddde3309518cd153d596b1"><td class="memItemLeft" align="right" valign="top"><a id="aa9eac1f580ddde3309518cd153d596b1" name="aa9eac1f580ddde3309518cd153d596b1"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_X86</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x04)</td></tr>
<tr class="memdesc:aa9eac1f580ddde3309518cd153d596b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for x86 binaries. <br /></td></tr>
<tr class="separator:aa9eac1f580ddde3309518cd153d596b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7f667d4a5d319f227f23163cbea086f"><td class="memItemLeft" align="right" valign="top"><a id="ab7f667d4a5d319f227f23163cbea086f" name="ab7f667d4a5d319f227f23163cbea086f"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_POWERPC</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x05)</td></tr>
<tr class="memdesc:ab7f667d4a5d319f227f23163cbea086f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for Big endian PowerPC binaries. <br /></td></tr>
<tr class="separator:ab7f667d4a5d319f227f23163cbea086f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2fe36218a38f400e1ce40820758f7427"><td class="memItemLeft" align="right" valign="top"><a id="a2fe36218a38f400e1ce40820758f7427" name="a2fe36218a38f400e1ce40820758f7427"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_IA64</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x06)</td></tr>
<tr class="memdesc:a2fe36218a38f400e1ce40820758f7427"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for IA-64 (Itanium) binaries. <br /></td></tr>
<tr class="separator:a2fe36218a38f400e1ce40820758f7427"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a495a58f63ebc7a8b756099efba492f8b"><td class="memItemLeft" align="right" valign="top"><a id="a495a58f63ebc7a8b756099efba492f8b" name="a495a58f63ebc7a8b756099efba492f8b"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_ARM</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x07)</td></tr>
<tr class="memdesc:a495a58f63ebc7a8b756099efba492f8b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for ARM binaries. <br /></td></tr>
<tr class="separator:a495a58f63ebc7a8b756099efba492f8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ec62e7e5e7df3d9af5b2ea3f857689a"><td class="memItemLeft" align="right" valign="top"><a id="a5ec62e7e5e7df3d9af5b2ea3f857689a" name="a5ec62e7e5e7df3d9af5b2ea3f857689a"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_ARMTHUMB</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x08)</td></tr>
<tr class="memdesc:a5ec62e7e5e7df3d9af5b2ea3f857689a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for ARM-Thumb binaries. <br /></td></tr>
<tr class="separator:a5ec62e7e5e7df3d9af5b2ea3f857689a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a50941088e93ef659c6b000bbcaf58143"><td class="memItemLeft" align="right" valign="top"><a id="a50941088e93ef659c6b000bbcaf58143" name="a50941088e93ef659c6b000bbcaf58143"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_SPARC</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x09)</td></tr>
<tr class="memdesc:a50941088e93ef659c6b000bbcaf58143"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for SPARC binaries. <br /></td></tr>
<tr class="separator:a50941088e93ef659c6b000bbcaf58143"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a01765158cd31cac21b272b180628fc4b"><td class="memItemLeft" align="right" valign="top"><a id="a01765158cd31cac21b272b180628fc4b" name="a01765158cd31cac21b272b180628fc4b"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_ARM64</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x0A)</td></tr>
<tr class="memdesc:a01765158cd31cac21b272b180628fc4b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for ARM64 binaries. <br /></td></tr>
<tr class="separator:a01765158cd31cac21b272b180628fc4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Branch/Call/Jump conversion filters. </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead. </dd></dl>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.7
</small></address>
</body>
</html>
