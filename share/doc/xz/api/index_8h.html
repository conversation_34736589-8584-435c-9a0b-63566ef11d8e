<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.7"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/index.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>


<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.4.5</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.7 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#enum-members">Enumerations</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">index.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Handling of .xz Index and related information.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__index__iter.html">lzma_index_iter</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Iterator to get information about Blocks and Streams.  <a href="structlzma__index__iter.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="typedef-members" name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:afc18c1443b3b9aa0d146b44e8755b62e"><td class="memItemLeft" align="right" valign="top">typedef struct lzma_index_s&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a></td></tr>
<tr class="memdesc:afc18c1443b3b9aa0d146b44e8755b62e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Opaque data type to hold the Index(es) and other information.  <br /></td></tr>
<tr class="separator:afc18c1443b3b9aa0d146b44e8755b62e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="enum-members" name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:a712b43192d944bf2f767711343cd9ca8"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8">lzma_index_iter_mode</a> { <a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8af46f6e5c414471c7c96586f380e48315">LZMA_INDEX_ITER_ANY</a> = 0
, <a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8a5b31d985de1c823151acdd7e4a966fc9">LZMA_INDEX_ITER_STREAM</a> = 1
, <a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8a2702617d60d6fc15138a749e06ef3414">LZMA_INDEX_ITER_BLOCK</a> = 2
, <a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8aa49bf4d561d8f2c61d300edbb6c282c7">LZMA_INDEX_ITER_NONEMPTY_BLOCK</a> = 3
 }</td></tr>
<tr class="memdesc:a712b43192d944bf2f767711343cd9ca8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Operation mode for <a class="el" href="index_8h.html#af428522e1b3eef137c65c5a01f766e0e" title="Get the next Block or Stream.">lzma_index_iter_next()</a>  <a href="index_8h.html#a712b43192d944bf2f767711343cd9ca8">More...</a><br /></td></tr>
<tr class="separator:a712b43192d944bf2f767711343cd9ca8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a880def3727ecdd7f242807083d228fc5"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a880def3727ecdd7f242807083d228fc5">lzma_index_memusage</a> (<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> streams, <a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> blocks) lzma_nothrow</td></tr>
<tr class="memdesc:a880def3727ecdd7f242807083d228fc5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate memory usage of lzma_index.  <br /></td></tr>
<tr class="separator:a880def3727ecdd7f242807083d228fc5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4118805ac7be6618aca6d6d7e5e4dde7"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a4118805ac7be6618aca6d6d7e5e4dde7">lzma_index_memused</a> (const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i) lzma_nothrow</td></tr>
<tr class="memdesc:a4118805ac7be6618aca6d6d7e5e4dde7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate the memory usage of an existing lzma_index.  <br /></td></tr>
<tr class="separator:a4118805ac7be6618aca6d6d7e5e4dde7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0850627d011111326d4278a3e2edec25"><td class="memItemLeft" align="right" valign="top"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a0850627d011111326d4278a3e2edec25">lzma_index_init</a> (const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator) lzma_nothrow</td></tr>
<tr class="memdesc:a0850627d011111326d4278a3e2edec25"><td class="mdescLeft">&#160;</td><td class="mdescRight">Allocate and initialize a new lzma_index structure.  <br /></td></tr>
<tr class="separator:a0850627d011111326d4278a3e2edec25"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0c2d0009f07fc315d5ac89e4bcd25abd"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a0c2d0009f07fc315d5ac89e4bcd25abd">lzma_index_end</a> (<a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator) lzma_nothrow</td></tr>
<tr class="memdesc:a0c2d0009f07fc315d5ac89e4bcd25abd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Deallocate lzma_index.  <br /></td></tr>
<tr class="separator:a0c2d0009f07fc315d5ac89e4bcd25abd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac347747eb933c7c408e6c801b33becc3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#ac347747eb933c7c408e6c801b33becc3">lzma_index_append</a> (<a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator, <a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> unpadded_size, <a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> uncompressed_size) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:ac347747eb933c7c408e6c801b33becc3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a new Block to lzma_index.  <br /></td></tr>
<tr class="separator:ac347747eb933c7c408e6c801b33becc3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79a19669237f19f0b11c9f3be80a62b4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a79a19669237f19f0b11c9f3be80a62b4">lzma_index_stream_flags</a> (<a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i, const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *stream_flags) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a79a19669237f19f0b11c9f3be80a62b4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the Stream Flags.  <br /></td></tr>
<tr class="separator:a79a19669237f19f0b11c9f3be80a62b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af8d6528a04241841bd0a4322b0c57eaa"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#af8d6528a04241841bd0a4322b0c57eaa">lzma_index_checks</a> (const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:af8d6528a04241841bd0a4322b0c57eaa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the types of integrity Checks.  <br /></td></tr>
<tr class="separator:af8d6528a04241841bd0a4322b0c57eaa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ed82f96c688f3c953f6509b6f4e2ef3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a3ed82f96c688f3c953f6509b6f4e2ef3">lzma_index_stream_padding</a> (<a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i, <a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> stream_padding) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a3ed82f96c688f3c953f6509b6f4e2ef3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the amount of Stream Padding.  <br /></td></tr>
<tr class="separator:a3ed82f96c688f3c953f6509b6f4e2ef3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd159a765b09b0cf79186069a848d07e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#afd159a765b09b0cf79186069a848d07e">lzma_index_stream_count</a> (const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:afd159a765b09b0cf79186069a848d07e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the number of Streams.  <br /></td></tr>
<tr class="separator:afd159a765b09b0cf79186069a848d07e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add1a8c506f67dbc19cae6747107e3bec"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#add1a8c506f67dbc19cae6747107e3bec">lzma_index_block_count</a> (const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:add1a8c506f67dbc19cae6747107e3bec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the number of Blocks.  <br /></td></tr>
<tr class="separator:add1a8c506f67dbc19cae6747107e3bec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a669ed1a82b1941217cfbb07e7826afc2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a669ed1a82b1941217cfbb07e7826afc2">lzma_index_size</a> (const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a669ed1a82b1941217cfbb07e7826afc2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the size of the Index field as bytes.  <br /></td></tr>
<tr class="separator:a669ed1a82b1941217cfbb07e7826afc2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af3630369b43c9ccc906065d759b49663"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#af3630369b43c9ccc906065d759b49663">lzma_index_stream_size</a> (const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:af3630369b43c9ccc906065d759b49663"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the total size of the Stream.  <br /></td></tr>
<tr class="separator:af3630369b43c9ccc906065d759b49663"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7829942b83ee1fa5b6443cc248b81041"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a7829942b83ee1fa5b6443cc248b81041">lzma_index_total_size</a> (const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a7829942b83ee1fa5b6443cc248b81041"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the total size of the Blocks.  <br /></td></tr>
<tr class="separator:a7829942b83ee1fa5b6443cc248b81041"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac875ed47d35385e5dac461b25c5ea1c9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#ac875ed47d35385e5dac461b25c5ea1c9">lzma_index_file_size</a> (const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:ac875ed47d35385e5dac461b25c5ea1c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the total size of the file.  <br /></td></tr>
<tr class="separator:ac875ed47d35385e5dac461b25c5ea1c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a620fe6317f1f9d7af9cc27c748bf07d6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a620fe6317f1f9d7af9cc27c748bf07d6">lzma_index_uncompressed_size</a> (const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a620fe6317f1f9d7af9cc27c748bf07d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the uncompressed size of the file.  <br /></td></tr>
<tr class="separator:a620fe6317f1f9d7af9cc27c748bf07d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa78f02f18ed29d289a6ef37b8ea98a21"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#aa78f02f18ed29d289a6ef37b8ea98a21">lzma_index_iter_init</a> (<a class="el" href="structlzma__index__iter.html">lzma_index_iter</a> *iter, const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i) lzma_nothrow</td></tr>
<tr class="memdesc:aa78f02f18ed29d289a6ef37b8ea98a21"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize an iterator.  <br /></td></tr>
<tr class="separator:aa78f02f18ed29d289a6ef37b8ea98a21"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae81438be8deff4894b104e65d8acdd24"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#ae81438be8deff4894b104e65d8acdd24">lzma_index_iter_rewind</a> (<a class="el" href="structlzma__index__iter.html">lzma_index_iter</a> *iter) lzma_nothrow</td></tr>
<tr class="memdesc:ae81438be8deff4894b104e65d8acdd24"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rewind the iterator.  <br /></td></tr>
<tr class="separator:ae81438be8deff4894b104e65d8acdd24"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af428522e1b3eef137c65c5a01f766e0e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#af428522e1b3eef137c65c5a01f766e0e">lzma_index_iter_next</a> (<a class="el" href="structlzma__index__iter.html">lzma_index_iter</a> *iter, <a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8">lzma_index_iter_mode</a> mode) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:af428522e1b3eef137c65c5a01f766e0e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the next Block or Stream.  <br /></td></tr>
<tr class="separator:af428522e1b3eef137c65c5a01f766e0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4f56df9d210712e5d7add5502c9eb93"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#ac4f56df9d210712e5d7add5502c9eb93">lzma_index_iter_locate</a> (<a class="el" href="structlzma__index__iter.html">lzma_index_iter</a> *iter, <a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> target) lzma_nothrow</td></tr>
<tr class="memdesc:ac4f56df9d210712e5d7add5502c9eb93"><td class="mdescLeft">&#160;</td><td class="mdescRight">Locate a Block.  <br /></td></tr>
<tr class="separator:ac4f56df9d210712e5d7add5502c9eb93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc4db36b4bd67af01819be9dd045c34a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#abc4db36b4bd67af01819be9dd045c34a">lzma_index_cat</a> (<a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *dest, <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *src, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:abc4db36b4bd67af01819be9dd045c34a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Concatenate lzma_indexes.  <br /></td></tr>
<tr class="separator:abc4db36b4bd67af01819be9dd045c34a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5161e3f67156577882e1d95dcb57e33e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a5161e3f67156577882e1d95dcb57e33e">lzma_index_dup</a> (const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a5161e3f67156577882e1d95dcb57e33e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Duplicate lzma_index.  <br /></td></tr>
<tr class="separator:a5161e3f67156577882e1d95dcb57e33e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6800d70f3b2afca085496460cd03211d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a6800d70f3b2afca085496460cd03211d">lzma_index_encoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a6800d70f3b2afca085496460cd03211d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize .xz Index encoder.  <br /></td></tr>
<tr class="separator:a6800d70f3b2afca085496460cd03211d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb56fd1d5914f8900ece7b88b78e5e23"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#abb56fd1d5914f8900ece7b88b78e5e23">lzma_index_decoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> **i, uint64_t memlimit) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:abb56fd1d5914f8900ece7b88b78e5e23"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize .xz Index decoder.  <br /></td></tr>
<tr class="separator:abb56fd1d5914f8900ece7b88b78e5e23"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add1ef06dec8a26d08ae8651cff0fd8d6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#add1ef06dec8a26d08ae8651cff0fd8d6">lzma_index_buffer_encode</a> (const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *i, uint8_t *out, size_t *out_pos, size_t out_size) lzma_nothrow</td></tr>
<tr class="memdesc:add1ef06dec8a26d08ae8651cff0fd8d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-call .xz Index encoder.  <br /></td></tr>
<tr class="separator:add1ef06dec8a26d08ae8651cff0fd8d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a028b8b8d59a413f9682eea1269a6ae8b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a028b8b8d59a413f9682eea1269a6ae8b">lzma_index_buffer_decode</a> (<a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> **i, uint64_t *memlimit, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator, const uint8_t *in, size_t *in_pos, size_t in_size) lzma_nothrow</td></tr>
<tr class="memdesc:a028b8b8d59a413f9682eea1269a6ae8b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-call .xz Index decoder.  <br /></td></tr>
<tr class="separator:a028b8b8d59a413f9682eea1269a6ae8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c5d77cf8532d95977d4571a1eb0a222"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="index_8h.html#a7c5d77cf8532d95977d4571a1eb0a222">lzma_file_info_decoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> **dest_index, uint64_t memlimit, uint64_t file_size) lzma_nothrow</td></tr>
<tr class="memdesc:a7c5d77cf8532d95977d4571a1eb0a222"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize a .xz file information decoder.  <br /></td></tr>
<tr class="separator:a7c5d77cf8532d95977d4571a1eb0a222"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Handling of .xz Index and related information. </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead. </dd></dl>
</div><h2 class="groupheader">Typedef Documentation</h2>
<a id="afc18c1443b3b9aa0d146b44e8755b62e" name="afc18c1443b3b9aa0d146b44e8755b62e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afc18c1443b3b9aa0d146b44e8755b62e">&#9670;&#160;</a></span>lzma_index</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct lzma_index_s <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Opaque data type to hold the Index(es) and other information. </p>
<p>lzma_index often holds just one .xz Index and possibly the Stream Flags of the same Stream and size of the Stream Padding field. However, multiple lzma_indexes can be concatenated with <a class="el" href="index_8h.html#abc4db36b4bd67af01819be9dd045c34a" title="Concatenate lzma_indexes.">lzma_index_cat()</a> and then there may be information about multiple Streams in the same lzma_index.</p>
<p>Notes about thread safety: Only one thread may modify lzma_index at a time. All functions that take non-const pointer to lzma_index modify it. As long as no thread is modifying the lzma_index, getting information from the same lzma_index can be done from multiple threads at the same time with functions that take a const pointer to lzma_index or use <a class="el" href="structlzma__index__iter.html" title="Iterator to get information about Blocks and Streams.">lzma_index_iter</a>. The same iterator must be used only by one thread at a time, of course, but there can be as many iterators for the same lzma_index as needed. </p>

</div>
</div>
<h2 class="groupheader">Enumeration Type Documentation</h2>
<a id="a712b43192d944bf2f767711343cd9ca8" name="a712b43192d944bf2f767711343cd9ca8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a712b43192d944bf2f767711343cd9ca8">&#9670;&#160;</a></span>lzma_index_iter_mode</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8">lzma_index_iter_mode</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Operation mode for <a class="el" href="index_8h.html#af428522e1b3eef137c65c5a01f766e0e" title="Get the next Block or Stream.">lzma_index_iter_next()</a> </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a712b43192d944bf2f767711343cd9ca8af46f6e5c414471c7c96586f380e48315" name="a712b43192d944bf2f767711343cd9ca8af46f6e5c414471c7c96586f380e48315"></a>LZMA_INDEX_ITER_ANY&#160;</td><td class="fielddoc"><p>Get the next Block or Stream. </p>
<p>Go to the next Block if the current Stream has at least one Block left. Otherwise go to the next Stream even if it has no Blocks. If the Stream has no Blocks (lzma_index_iter.stream.block_count == 0), lzma_index_iter.block will have undefined values. </p>
</td></tr>
<tr><td class="fieldname"><a id="a712b43192d944bf2f767711343cd9ca8a5b31d985de1c823151acdd7e4a966fc9" name="a712b43192d944bf2f767711343cd9ca8a5b31d985de1c823151acdd7e4a966fc9"></a>LZMA_INDEX_ITER_STREAM&#160;</td><td class="fielddoc"><p>Get the next Stream. </p>
<p>Go to the next Stream even if the current Stream has unread Blocks left. If the next Stream has at least one Block, the iterator will point to the first Block. If there are no Blocks, lzma_index_iter.block will have undefined values. </p>
</td></tr>
<tr><td class="fieldname"><a id="a712b43192d944bf2f767711343cd9ca8a2702617d60d6fc15138a749e06ef3414" name="a712b43192d944bf2f767711343cd9ca8a2702617d60d6fc15138a749e06ef3414"></a>LZMA_INDEX_ITER_BLOCK&#160;</td><td class="fielddoc"><p>Get the next Block. </p>
<p>Go to the next Block if the current Stream has at least one Block left. If the current Stream has no Blocks left, the next Stream with at least one Block is located and the iterator will be made to point to the first Block of that Stream. </p>
</td></tr>
<tr><td class="fieldname"><a id="a712b43192d944bf2f767711343cd9ca8aa49bf4d561d8f2c61d300edbb6c282c7" name="a712b43192d944bf2f767711343cd9ca8aa49bf4d561d8f2c61d300edbb6c282c7"></a>LZMA_INDEX_ITER_NONEMPTY_BLOCK&#160;</td><td class="fielddoc"><p>Get the next non-empty Block. </p>
<p>This is like LZMA_INDEX_ITER_BLOCK except that it will skip Blocks whose Uncompressed Size is zero. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="a880def3727ecdd7f242807083d228fc5" name="a880def3727ecdd7f242807083d228fc5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a880def3727ecdd7f242807083d228fc5">&#9670;&#160;</a></span>lzma_index_memusage()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_index_memusage </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td>
          <td class="paramname"><em>streams</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td>
          <td class="paramname"><em>blocks</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Calculate memory usage of lzma_index. </p>
<p>On disk, the size of the Index field depends on both the number of Records stored and the size of the Records (due to variable-length integer encoding). When the Index is kept in lzma_index structure, the memory usage depends only on the number of Records/Blocks stored in the Index(es), and in case of concatenated lzma_indexes, the number of Streams. The size in RAM is almost always significantly bigger than in the encoded form on disk.</p>
<p>This function calculates an approximate amount of memory needed to hold the given number of Streams and Blocks in lzma_index structure. This value may vary between CPU architectures and also between liblzma versions if the internal implementation is modified.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">streams</td><td>Number of Streams </td></tr>
    <tr><td class="paramname">blocks</td><td>Number of Blocks</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Approximate memory in bytes needed in a lzma_index structure. </dd></dl>

</div>
</div>
<a id="a4118805ac7be6618aca6d6d7e5e4dde7" name="a4118805ac7be6618aca6d6d7e5e4dde7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4118805ac7be6618aca6d6d7e5e4dde7">&#9670;&#160;</a></span>lzma_index_memused()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_index_memused </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Calculate the memory usage of an existing lzma_index. </p>
<p>This is a shorthand for lzma_index_memusage(lzma_index_stream_count(i), lzma_index_block_count(i)).</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i</td><td>Pointer to lzma_index structure</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Approximate memory in bytes used by the lzma_index structure. </dd></dl>

</div>
</div>
<a id="a0850627d011111326d4278a3e2edec25" name="a0850627d011111326d4278a3e2edec25"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0850627d011111326d4278a3e2edec25">&#9670;&#160;</a></span>lzma_index_init()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> * lzma_index_init </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *&#160;</td>
          <td class="paramname"><em>allocator</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Allocate and initialize a new lzma_index structure. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free().</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>On success, a pointer to an empty initialized lzma_index is returned. If allocation fails, NULL is returned. </dd></dl>

</div>
</div>
<a id="a0c2d0009f07fc315d5ac89e4bcd25abd" name="a0c2d0009f07fc315d5ac89e4bcd25abd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0c2d0009f07fc315d5ac89e4bcd25abd">&#9670;&#160;</a></span>lzma_index_end()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void lzma_index_end </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *&#160;</td>
          <td class="paramname"><em>allocator</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Deallocate lzma_index. </p>
<p>If i is NULL, this does nothing.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i</td><td>Pointer to lzma_index structure to deallocate </td></tr>
    <tr><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ac347747eb933c7c408e6c801b33becc3" name="ac347747eb933c7c408e6c801b33becc3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac347747eb933c7c408e6c801b33becc3">&#9670;&#160;</a></span>lzma_index_append()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_index_append </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *&#160;</td>
          <td class="paramname"><em>allocator</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td>
          <td class="paramname"><em>unpadded_size</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td>
          <td class="paramname"><em>uncompressed_size</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Add a new Block to lzma_index. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i</td><td>Pointer to a lzma_index structure </td></tr>
    <tr><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). </td></tr>
    <tr><td class="paramname">unpadded_size</td><td>Unpadded Size of a Block. This can be calculated with <a class="el" href="block_8h.html#a412d5605280fa29befae1b89e344bf30" title="Calculate Unpadded Size.">lzma_block_unpadded_size()</a> after encoding or decoding the Block. </td></tr>
    <tr><td class="paramname">uncompressed_size</td><td>Uncompressed Size of a Block. This can be taken directly from <a class="el" href="structlzma__block.html" title="Options for the Block and Block Header encoders and decoders.">lzma_block</a> structure after encoding or decoding the Block.</td></tr>
  </table>
  </dd>
</dl>
<p>Appending a new Block does not invalidate iterators. For example, if an iterator was pointing to the end of the lzma_index, after <a class="el" href="index_8h.html#ac347747eb933c7c408e6c801b33becc3" title="Add a new Block to lzma_index.">lzma_index_append()</a> it is possible to read the next Block with an existing iterator.</p>
<dl class="section return"><dt>Returns</dt><dd>Possible lzma_ret values:<ul>
<li>LZMA_OK</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_DATA_ERROR: Compressed or uncompressed size of the Stream or size of the Index field would grow too big.</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a79a19669237f19f0b11c9f3be80a62b4" name="a79a19669237f19f0b11c9f3be80a62b4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a79a19669237f19f0b11c9f3be80a62b4">&#9670;&#160;</a></span>lzma_index_stream_flags()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_index_stream_flags </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__stream__flags.html">lzma_stream_flags</a> *&#160;</td>
          <td class="paramname"><em>stream_flags</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the Stream Flags. </p>
<p>Set the Stream Flags of the last (and typically the only) Stream in lzma_index. This can be useful when reading information from the lzma_index, because to decode Blocks, knowing the integrity check type is needed.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i</td><td>Pointer to lzma_index structure </td></tr>
    <tr><td class="paramname">stream_flags</td><td>Pointer to <a class="el" href="structlzma__stream__flags.html" title="Options for encoding/decoding Stream Header and Stream Footer.">lzma_stream_flags</a> structure. This is copied into the internal preallocated structure, so the caller doesn't need to keep the flags' data available after calling this function.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible lzma_ret values:<ul>
<li>LZMA_OK</li>
<li>LZMA_OPTIONS_ERROR: Unsupported stream_flags-&gt;version.</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="af8d6528a04241841bd0a4322b0c57eaa" name="af8d6528a04241841bd0a4322b0c57eaa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af8d6528a04241841bd0a4322b0c57eaa">&#9670;&#160;</a></span>lzma_index_checks()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_index_checks </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the types of integrity Checks. </p>
<p>If <a class="el" href="index_8h.html#a79a19669237f19f0b11c9f3be80a62b4" title="Set the Stream Flags.">lzma_index_stream_flags()</a> is used to set the Stream Flags for every Stream, <a class="el" href="index_8h.html#af8d6528a04241841bd0a4322b0c57eaa" title="Get the types of integrity Checks.">lzma_index_checks()</a> can be used to get a bitmask to indicate which Check types have been used. It can be useful e.g. if showing the Check types to the user.</p>
<p>The bitmask is 1 &lt;&lt; check_id, e.g. CRC32 is 1 &lt;&lt; 1 and SHA-256 is 1 &lt;&lt; 10.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i</td><td>Pointer to lzma_index structure</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Bitmask indicating which Check types are used in the lzma_index </dd></dl>

</div>
</div>
<a id="a3ed82f96c688f3c953f6509b6f4e2ef3" name="a3ed82f96c688f3c953f6509b6f4e2ef3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3ed82f96c688f3c953f6509b6f4e2ef3">&#9670;&#160;</a></span>lzma_index_stream_padding()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_index_stream_padding </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td>
          <td class="paramname"><em>stream_padding</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the amount of Stream Padding. </p>
<p>Set the amount of Stream Padding of the last (and typically the only) Stream in the lzma_index. This is needed when planning to do random-access reading within multiple concatenated Streams.</p>
<p>By default, the amount of Stream Padding is assumed to be zero bytes.</p>
<dl class="section return"><dt>Returns</dt><dd>Possible lzma_ret values:<ul>
<li>LZMA_OK</li>
<li>LZMA_DATA_ERROR: The file size would grow too big.</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="afd159a765b09b0cf79186069a848d07e" name="afd159a765b09b0cf79186069a848d07e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afd159a765b09b0cf79186069a848d07e">&#9670;&#160;</a></span>lzma_index_stream_count()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_stream_count </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the number of Streams. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i</td><td>Pointer to lzma_index structure</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Number of Streams in the lzma_index </dd></dl>

</div>
</div>
<a id="add1a8c506f67dbc19cae6747107e3bec" name="add1a8c506f67dbc19cae6747107e3bec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#add1a8c506f67dbc19cae6747107e3bec">&#9670;&#160;</a></span>lzma_index_block_count()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_block_count </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the number of Blocks. </p>
<p>This returns the total number of Blocks in lzma_index. To get number of Blocks in individual Streams, use <a class="el" href="structlzma__index__iter.html" title="Iterator to get information about Blocks and Streams.">lzma_index_iter</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i</td><td>Pointer to lzma_index structure</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Number of blocks in the lzma_index </dd></dl>

</div>
</div>
<a id="a669ed1a82b1941217cfbb07e7826afc2" name="a669ed1a82b1941217cfbb07e7826afc2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a669ed1a82b1941217cfbb07e7826afc2">&#9670;&#160;</a></span>lzma_index_size()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_size </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the size of the Index field as bytes. </p>
<p>This is needed to verify the Backward Size field in the Stream Footer.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i</td><td>Pointer to lzma_index structure</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Size in bytes of the Index </dd></dl>

</div>
</div>
<a id="af3630369b43c9ccc906065d759b49663" name="af3630369b43c9ccc906065d759b49663"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af3630369b43c9ccc906065d759b49663">&#9670;&#160;</a></span>lzma_index_stream_size()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_stream_size </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the total size of the Stream. </p>
<p>If multiple lzma_indexes have been combined, this works as if the Blocks were in a single Stream. This is useful if you are going to combine Blocks from multiple Streams into a single new Stream.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i</td><td>Pointer to lzma_index structure</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Size in bytes of the Stream (if all Blocks are combined into one Stream). </dd></dl>

</div>
</div>
<a id="a7829942b83ee1fa5b6443cc248b81041" name="a7829942b83ee1fa5b6443cc248b81041"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7829942b83ee1fa5b6443cc248b81041">&#9670;&#160;</a></span>lzma_index_total_size()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_total_size </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the total size of the Blocks. </p>
<p>This doesn't include the Stream Header, Stream Footer, Stream Padding, or Index fields.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i</td><td>Pointer to lzma_index structure</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Size in bytes of all Blocks in the Stream(s) </dd></dl>

</div>
</div>
<a id="ac875ed47d35385e5dac461b25c5ea1c9" name="ac875ed47d35385e5dac461b25c5ea1c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac875ed47d35385e5dac461b25c5ea1c9">&#9670;&#160;</a></span>lzma_index_file_size()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_file_size </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the total size of the file. </p>
<p>When no lzma_indexes have been combined with <a class="el" href="index_8h.html#abc4db36b4bd67af01819be9dd045c34a" title="Concatenate lzma_indexes.">lzma_index_cat()</a> and there is no Stream Padding, this function is identical to <a class="el" href="index_8h.html#af3630369b43c9ccc906065d759b49663" title="Get the total size of the Stream.">lzma_index_stream_size()</a>. If multiple lzma_indexes have been combined, this includes also the headers of each separate Stream and the possible Stream Padding fields.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i</td><td>Pointer to lzma_index structure</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Total size of the .xz file in bytes </dd></dl>

</div>
</div>
<a id="a620fe6317f1f9d7af9cc27c748bf07d6" name="a620fe6317f1f9d7af9cc27c748bf07d6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a620fe6317f1f9d7af9cc27c748bf07d6">&#9670;&#160;</a></span>lzma_index_uncompressed_size()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_index_uncompressed_size </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the uncompressed size of the file. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i</td><td>Pointer to lzma_index structure</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Size in bytes of the uncompressed data in the file </dd></dl>

</div>
</div>
<a id="aa78f02f18ed29d289a6ef37b8ea98a21" name="aa78f02f18ed29d289a6ef37b8ea98a21"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa78f02f18ed29d289a6ef37b8ea98a21">&#9670;&#160;</a></span>lzma_index_iter_init()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void lzma_index_iter_init </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__index__iter.html">lzma_index_iter</a> *&#160;</td>
          <td class="paramname"><em>iter</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Initialize an iterator. </p>
<p>This function associates the iterator with the given lzma_index, and calls <a class="el" href="index_8h.html#ae81438be8deff4894b104e65d8acdd24" title="Rewind the iterator.">lzma_index_iter_rewind()</a> on the iterator.</p>
<p>This function doesn't allocate any memory, thus there is no lzma_index_iter_end(). The iterator is valid as long as the associated lzma_index is valid, that is, until <a class="el" href="index_8h.html#a0c2d0009f07fc315d5ac89e4bcd25abd" title="Deallocate lzma_index.">lzma_index_end()</a> or using it as source in <a class="el" href="index_8h.html#abc4db36b4bd67af01819be9dd045c34a" title="Concatenate lzma_indexes.">lzma_index_cat()</a>. Specifically, lzma_index doesn't become invalid if new Blocks are added to it with <a class="el" href="index_8h.html#ac347747eb933c7c408e6c801b33becc3" title="Add a new Block to lzma_index.">lzma_index_append()</a> or if it is used as the destination in <a class="el" href="index_8h.html#abc4db36b4bd67af01819be9dd045c34a" title="Concatenate lzma_indexes.">lzma_index_cat()</a>.</p>
<p>It is safe to make copies of an initialized <a class="el" href="structlzma__index__iter.html" title="Iterator to get information about Blocks and Streams.">lzma_index_iter</a>, for example, to easily restart reading at some particular position.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">iter</td><td>Pointer to a <a class="el" href="structlzma__index__iter.html" title="Iterator to get information about Blocks and Streams.">lzma_index_iter</a> structure </td></tr>
    <tr><td class="paramname">i</td><td>lzma_index to which the iterator will be associated </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ae81438be8deff4894b104e65d8acdd24" name="ae81438be8deff4894b104e65d8acdd24"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae81438be8deff4894b104e65d8acdd24">&#9670;&#160;</a></span>lzma_index_iter_rewind()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void lzma_index_iter_rewind </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__index__iter.html">lzma_index_iter</a> *&#160;</td>
          <td class="paramname"><em>iter</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rewind the iterator. </p>
<p>Rewind the iterator so that next call to <a class="el" href="index_8h.html#af428522e1b3eef137c65c5a01f766e0e" title="Get the next Block or Stream.">lzma_index_iter_next()</a> will return the first Block or Stream.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">iter</td><td>Pointer to a <a class="el" href="structlzma__index__iter.html" title="Iterator to get information about Blocks and Streams.">lzma_index_iter</a> structure </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="af428522e1b3eef137c65c5a01f766e0e" name="af428522e1b3eef137c65c5a01f766e0e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af428522e1b3eef137c65c5a01f766e0e">&#9670;&#160;</a></span>lzma_index_iter_next()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a> lzma_index_iter_next </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__index__iter.html">lzma_index_iter</a> *&#160;</td>
          <td class="paramname"><em>iter</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8">lzma_index_iter_mode</a>&#160;</td>
          <td class="paramname"><em>mode</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the next Block or Stream. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">iter</td><td>Iterator initialized with <a class="el" href="index_8h.html#aa78f02f18ed29d289a6ef37b8ea98a21" title="Initialize an iterator.">lzma_index_iter_init()</a> </td></tr>
    <tr><td class="paramname">mode</td><td>Specify what kind of information the caller wants to get. See lzma_index_iter_mode for details.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>lzma_bool:<ul>
<li>true if no Block or Stream matching the mode is found. *iter is not updated (failure).</li>
<li>false if the next Block or Stream matching the mode was found. *iter is updated (success). </li>
</ul>
</dd></dl>

</div>
</div>
<a id="ac4f56df9d210712e5d7add5502c9eb93" name="ac4f56df9d210712e5d7add5502c9eb93"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac4f56df9d210712e5d7add5502c9eb93">&#9670;&#160;</a></span>lzma_index_iter_locate()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a> lzma_index_iter_locate </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__index__iter.html">lzma_index_iter</a> *&#160;</td>
          <td class="paramname"><em>iter</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td>
          <td class="paramname"><em>target</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Locate a Block. </p>
<p>If it is possible to seek in the .xz file, it is possible to parse the Index field(s) and use <a class="el" href="index_8h.html#ac4f56df9d210712e5d7add5502c9eb93" title="Locate a Block.">lzma_index_iter_locate()</a> to do random-access reading with granularity of Block size.</p>
<p>If the target is smaller than the uncompressed size of the Stream (can be checked with <a class="el" href="index_8h.html#a620fe6317f1f9d7af9cc27c748bf07d6" title="Get the uncompressed size of the file.">lzma_index_uncompressed_size()</a>):</p><ul>
<li>Information about the Stream and Block containing the requested uncompressed offset is stored into *iter.</li>
<li>Internal state of the iterator is adjusted so that <a class="el" href="index_8h.html#af428522e1b3eef137c65c5a01f766e0e" title="Get the next Block or Stream.">lzma_index_iter_next()</a> can be used to read subsequent Blocks or Streams.</li>
</ul>
<p>If the target is greater than the uncompressed size of the Stream, *iter is not modified.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">iter</td><td>Iterator that was earlier initialized with <a class="el" href="index_8h.html#aa78f02f18ed29d289a6ef37b8ea98a21" title="Initialize an iterator.">lzma_index_iter_init()</a>. </td></tr>
    <tr><td class="paramname">target</td><td>Uncompressed target offset which the caller would like to locate from the Stream</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>lzma_bool:<ul>
<li>true if the target is greater than or equal to the uncompressed size of the Stream (failure)</li>
<li>false if the target is smaller than the uncompressed size of the Stream (success) </li>
</ul>
</dd></dl>

</div>
</div>
<a id="abc4db36b4bd67af01819be9dd045c34a" name="abc4db36b4bd67af01819be9dd045c34a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc4db36b4bd67af01819be9dd045c34a">&#9670;&#160;</a></span>lzma_index_cat()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_index_cat </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>dest</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>src</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *&#160;</td>
          <td class="paramname"><em>allocator</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Concatenate lzma_indexes. </p>
<p>Concatenating lzma_indexes is useful when doing random-access reading in multi-Stream .xz file, or when combining multiple Streams into single Stream.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">dest</td><td>lzma_index after which src is appended </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">src</td><td>lzma_index to be appended after dest. If this function succeeds, the memory allocated for src is freed or moved to be part of dest, and all iterators pointing to src will become invalid. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free().</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible lzma_ret values:<ul>
<li>LZMA_OK: lzma_indexes were concatenated successfully. src is now a dangling pointer.</li>
<li>LZMA_DATA_ERROR: *dest would grow too big.</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a5161e3f67156577882e1d95dcb57e33e" name="a5161e3f67156577882e1d95dcb57e33e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5161e3f67156577882e1d95dcb57e33e">&#9670;&#160;</a></span>lzma_index_dup()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> * lzma_index_dup </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *&#160;</td>
          <td class="paramname"><em>allocator</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Duplicate lzma_index. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i</td><td>Pointer to lzma_index structure to be duplicated </td></tr>
    <tr><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free().</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A copy of the lzma_index, or NULL if memory allocation failed. </dd></dl>

</div>
</div>
<a id="a6800d70f3b2afca085496460cd03211d" name="a6800d70f3b2afca085496460cd03211d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6800d70f3b2afca085496460cd03211d">&#9670;&#160;</a></span>lzma_index_encoder()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_index_encoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *&#160;</td>
          <td class="paramname"><em>strm</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Initialize .xz Index encoder. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to properly prepared <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> </td></tr>
    <tr><td class="paramname">i</td><td>Pointer to lzma_index which should be encoded.</td></tr>
  </table>
  </dd>
</dl>
<p>The valid `action' values for <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> are LZMA_RUN and LZMA_FINISH. It is enough to use only one of them (you can choose freely).</p>
<dl class="section return"><dt>Returns</dt><dd>Possible lzma_ret values:<ul>
<li>LZMA_OK: Initialization succeeded, continue with <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>.</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="abb56fd1d5914f8900ece7b88b78e5e23" name="abb56fd1d5914f8900ece7b88b78e5e23"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abb56fd1d5914f8900ece7b88b78e5e23">&#9670;&#160;</a></span>lzma_index_decoder()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_index_decoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *&#160;</td>
          <td class="paramname"><em>strm</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> **&#160;</td>
          <td class="paramname"><em>i</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&#160;</td>
          <td class="paramname"><em>memlimit</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Initialize .xz Index decoder. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">strm</td><td>Pointer to properly prepared <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">i</td><td>The decoded Index will be made available via this pointer. Initially this function will set *i to NULL (the old value is ignored). If decoding succeeds (<a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> returns LZMA_STREAM_END), *i will be set to point to a new lzma_index, which the application has to later free with <a class="el" href="index_8h.html#a0c2d0009f07fc315d5ac89e4bcd25abd" title="Deallocate lzma_index.">lzma_index_end()</a>. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">memlimit</td><td>How much memory the resulting lzma_index is allowed to require. liblzma 5.2.3 and earlier don't allow 0 here and return LZMA_PROG_ERROR; later versions treat 0 as if 1 had been specified.</td></tr>
  </table>
  </dd>
</dl>
<p>Valid `action' arguments to <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> are LZMA_RUN and LZMA_FINISH. There is no need to use LZMA_FINISH, but it's allowed because it may simplify certain types of applications.</p>
<dl class="section return"><dt>Returns</dt><dd>Possible lzma_ret values:<ul>
<li>LZMA_OK: Initialization succeeded, continue with <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>.</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_PROG_ERROR</li>
</ul>
</dd></dl>
<dl class="section note"><dt>Note</dt><dd>liblzma 5.2.3 and older list also LZMA_MEMLIMIT_ERROR here but that error code has never been possible from this initialization function. </dd></dl>

</div>
</div>
<a id="add1ef06dec8a26d08ae8651cff0fd8d6" name="add1ef06dec8a26d08ae8651cff0fd8d6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#add1ef06dec8a26d08ae8651cff0fd8d6">&#9670;&#160;</a></span>lzma_index_buffer_encode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_index_buffer_encode </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> *&#160;</td>
          <td class="paramname"><em>i</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *&#160;</td>
          <td class="paramname"><em>out</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&#160;</td>
          <td class="paramname"><em>out_pos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>out_size</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-call .xz Index encoder. </p>
<dl class="section note"><dt>Note</dt><dd>This function doesn't take allocator argument since all the internal data is allocated on stack.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">i</td><td>lzma_index to be encoded </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out_pos</td><td>The next byte will be written to out[*out_pos]. *out_pos is updated only if encoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">out_size</td><td>Size of the out buffer; the first byte into which no data is written to is out[out_size].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible lzma_ret values:<ul>
<li>LZMA_OK: Encoding was successful.</li>
<li>LZMA_BUF_ERROR: Output buffer is too small. Use <a class="el" href="index_8h.html#a669ed1a82b1941217cfbb07e7826afc2" title="Get the size of the Index field as bytes.">lzma_index_size()</a> to find out how much output space is needed.</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a028b8b8d59a413f9682eea1269a6ae8b" name="a028b8b8d59a413f9682eea1269a6ae8b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a028b8b8d59a413f9682eea1269a6ae8b">&#9670;&#160;</a></span>lzma_index_buffer_decode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_index_buffer_decode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> **&#160;</td>
          <td class="paramname"><em>i</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t *&#160;</td>
          <td class="paramname"><em>memlimit</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *&#160;</td>
          <td class="paramname"><em>allocator</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *&#160;</td>
          <td class="paramname"><em>in</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&#160;</td>
          <td class="paramname"><em>in_pos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>in_size</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Single-call .xz Index decoder. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">i</td><td>If decoding succeeds, *i will point to a new lzma_index, which the application has to later free with <a class="el" href="index_8h.html#a0c2d0009f07fc315d5ac89e4bcd25abd" title="Deallocate lzma_index.">lzma_index_end()</a>. If an error occurs, *i will be NULL. The old value of *i is always ignored and thus doesn't need to be initialized by the caller. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">memlimit</td><td>Pointer to how much memory the resulting lzma_index is allowed to require. The value pointed by this pointer is modified if and only if LZMA_MEMLIMIT_ERROR is returned. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_pos</td><td>The next byte will be read from in[*in_pos]. *in_pos is updated only if decoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_size</td><td>Size of the input buffer; the first byte that won't be read is in[in_size].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible lzma_ret values:<ul>
<li>LZMA_OK: Decoding was successful.</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_MEMLIMIT_ERROR: Memory usage limit was reached. The minimum required memlimit value was stored to *memlimit.</li>
<li>LZMA_DATA_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a7c5d77cf8532d95977d4571a1eb0a222" name="a7c5d77cf8532d95977d4571a1eb0a222"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7c5d77cf8532d95977d4571a1eb0a222">&#9670;&#160;</a></span>lzma_file_info_decoder()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_file_info_decoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *&#160;</td>
          <td class="paramname"><em>strm</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">lzma_index</a> **&#160;</td>
          <td class="paramname"><em>dest_index</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&#160;</td>
          <td class="paramname"><em>memlimit</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&#160;</td>
          <td class="paramname"><em>file_size</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Initialize a .xz file information decoder. </p>
<p>This decoder decodes the Stream Header, Stream Footer, Index, and Stream Padding field(s) from the input .xz file and stores the resulting combined index in *dest_index. This information can be used to get the uncompressed file size with lzma_index_uncompressed_size(*dest_index) or, for example, to implement random access reading by locating the Blocks in the Streams.</p>
<p>To get the required information from the .xz file, <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> may ask the application to seek in the input file by returning LZMA_SEEK_NEEDED and having the target file position specified in <a class="el" href="structlzma__stream.html#af7c43a61f3dfeb0b9c8487b7f275054e" title="New seek input position for LZMA_SEEK_NEEDED.">lzma_stream.seek_pos</a>. The number of seeks required depends on the input file and how big buffers the application provides. When possible, the decoder will seek backward and forward in the given buffer to avoid useless seek requests. Thus, if the application provides the whole file at once, no external seeking will be required (that is, <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> won't return LZMA_SEEK_NEEDED).</p>
<p>The value in <a class="el" href="structlzma__stream.html#a1a411e1755d6185756caefabc3932c7b">lzma_stream.total_in</a> can be used to estimate how much data liblzma had to read to get the file information. However, due to seeking and the way total_in is updated, the value of total_in will be somewhat inaccurate (a little too big). Thus, total_in is a good estimate but don't expect to see the same exact value for the same file if you change the input buffer size or switch to a different liblzma version.</p>
<p>Valid `action' arguments to <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> are LZMA_RUN and LZMA_FINISH. You only need to use LZMA_RUN; LZMA_FINISH is only supported because it might be convenient for some applications. If you use LZMA_FINISH and if <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> asks the application to seek, remember to reset `action' back to LZMA_RUN unless you hit the end of the file again.</p>
<p>Possible return values from <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>:</p><ul>
<li>LZMA_OK: All OK so far, more input needed</li>
<li>LZMA_SEEK_NEEDED: Provide more input starting from the absolute file position strm-&gt;seek_pos</li>
<li>LZMA_STREAM_END: Decoding was successful, *dest_index has been set</li>
<li>LZMA_FORMAT_ERROR: The input file is not in the .xz format (the expected magic bytes were not found from the beginning of the file)</li>
<li>LZMA_OPTIONS_ERROR: File looks valid but contains headers that aren't supported by this version of liblzma</li>
<li>LZMA_DATA_ERROR: File is corrupt</li>
<li>LZMA_BUF_ERROR</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_MEMLIMIT_ERROR</li>
<li>LZMA_PROG_ERROR</li>
</ul>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">strm</td><td>Pointer to a properly prepared <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">dest_index</td><td>Pointer to a pointer where the decoder will put the decoded lzma_index. The old value of *dest_index is ignored (not freed). </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">memlimit</td><td>How much memory the resulting lzma_index is allowed to require. Use UINT64_MAX to effectively disable the limiter. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">file_size</td><td>Size of the input .xz file</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible lzma_ret values:<ul>
<li>LZMA_OK</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.7
</small></address>
</body>
</html>
