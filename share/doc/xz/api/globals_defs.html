<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.7"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>


<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.4.5</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.7 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_enum.html"><span>Enumerations</span></a></li>
      <li><a href="globals_eval.html"><span>Enumerator</span></a></li>
      <li class="current"><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li class="current"><a href="#index_l"><span>l</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented macros with links to the documentation:</div>

<h3><a id="index_l" name="index_l"></a>- l -</h3><ul>
<li>LZMA_BACKWARD_SIZE_MAX&#160;:&#160;<a class="el" href="stream__flags_8h.html#a2e5e09010880f8caa6cd6539c7341239">stream_flags.h</a></li>
<li>LZMA_BACKWARD_SIZE_MIN&#160;:&#160;<a class="el" href="stream__flags_8h.html#ae8da8190f1396f66332073946bc45634">stream_flags.h</a></li>
<li>lzma_block_header_size_decode&#160;:&#160;<a class="el" href="block_8h.html#ac025c940683a70f4c7f956bad814fd5f">block.h</a></li>
<li>LZMA_CHECK_ID_MAX&#160;:&#160;<a class="el" href="check_8h.html#acd221ababe30230d9647aab469ad80cb">check.h</a></li>
<li>LZMA_CHECK_SIZE_MAX&#160;:&#160;<a class="el" href="check_8h.html#a379e931cf86351ab1d97896cda9abbe0">check.h</a></li>
<li>LZMA_CONCATENATED&#160;:&#160;<a class="el" href="container_8h.html#a563c84b5f368b3dd00d92ea903c5c33d">container.h</a></li>
<li>LZMA_DELTA_DIST_MAX&#160;:&#160;<a class="el" href="delta_8h.html#afdf8a5ce184ddf9f8070de637775da94">delta.h</a></li>
<li>LZMA_DELTA_DIST_MIN&#160;:&#160;<a class="el" href="delta_8h.html#a466886d9d01392f61bdf267687a4f96e">delta.h</a></li>
<li>LZMA_FAIL_FAST&#160;:&#160;<a class="el" href="container_8h.html#aa1f469ed3d4b2eaf12f8081657efc9d5">container.h</a></li>
<li>LZMA_FILTER_ARM&#160;:&#160;<a class="el" href="bcj_8h.html#a495a58f63ebc7a8b756099efba492f8b">bcj.h</a></li>
<li>LZMA_FILTER_ARM64&#160;:&#160;<a class="el" href="bcj_8h.html#a01765158cd31cac21b272b180628fc4b">bcj.h</a></li>
<li>LZMA_FILTER_ARMTHUMB&#160;:&#160;<a class="el" href="bcj_8h.html#a5ec62e7e5e7df3d9af5b2ea3f857689a">bcj.h</a></li>
<li>LZMA_FILTER_DELTA&#160;:&#160;<a class="el" href="delta_8h.html#a7ced67235ad7a01ae31d32ecf1e634cb">delta.h</a></li>
<li>LZMA_FILTER_IA64&#160;:&#160;<a class="el" href="bcj_8h.html#a2fe36218a38f400e1ce40820758f7427">bcj.h</a></li>
<li>LZMA_FILTER_LZMA1&#160;:&#160;<a class="el" href="lzma12_8h.html#accedd16abcb758e7f748bac1102abda9">lzma12.h</a></li>
<li>LZMA_FILTER_LZMA1EXT&#160;:&#160;<a class="el" href="lzma12_8h.html#a98a7fd42aa78a273a6b138629e46772d">lzma12.h</a></li>
<li>LZMA_FILTER_LZMA2&#160;:&#160;<a class="el" href="lzma12_8h.html#a04f9d9a018a47cc99491e6e94e92f96b">lzma12.h</a></li>
<li>LZMA_FILTER_POWERPC&#160;:&#160;<a class="el" href="bcj_8h.html#ab7f667d4a5d319f227f23163cbea086f">bcj.h</a></li>
<li>LZMA_FILTER_SPARC&#160;:&#160;<a class="el" href="bcj_8h.html#a50941088e93ef659c6b000bbcaf58143">bcj.h</a></li>
<li>LZMA_FILTER_X86&#160;:&#160;<a class="el" href="bcj_8h.html#aa9eac1f580ddde3309518cd153d596b1">bcj.h</a></li>
<li>LZMA_FILTERS_MAX&#160;:&#160;<a class="el" href="filter_8h.html#ab33c0cc1728bf390e5b84f8bce1928ba">filter.h</a></li>
<li>LZMA_IGNORE_CHECK&#160;:&#160;<a class="el" href="container_8h.html#a1289925ae1c63a8e86f69f3657118a4d">container.h</a></li>
<li>LZMA_PRESET_DEFAULT&#160;:&#160;<a class="el" href="container_8h.html#af3ca20ff228b363a82515c1aee9e27bc">container.h</a></li>
<li>LZMA_PRESET_EXTREME&#160;:&#160;<a class="el" href="container_8h.html#af524fe9af5737820fdadcd40a2c26deb">container.h</a></li>
<li>LZMA_PRESET_LEVEL_MASK&#160;:&#160;<a class="el" href="container_8h.html#a97e40265e355a21bd2465aaa5b85f03d">container.h</a></li>
<li>lzma_set_ext_size&#160;:&#160;<a class="el" href="lzma12_8h.html#a73ed0293db4e59d73a702d66fef537c3">lzma12.h</a></li>
<li>LZMA_STR_ALL_FILTERS&#160;:&#160;<a class="el" href="filter_8h.html#a41aa51eeb53190404439c31d8e9c97cd">filter.h</a></li>
<li>LZMA_STR_DECODER&#160;:&#160;<a class="el" href="filter_8h.html#a8a0f3fc03bdb84a294cdd53a98783104">filter.h</a></li>
<li>LZMA_STR_ENCODER&#160;:&#160;<a class="el" href="filter_8h.html#a09a775f6a78d28ca136acfb51ad5fa02">filter.h</a></li>
<li>LZMA_STR_GETOPT_LONG&#160;:&#160;<a class="el" href="filter_8h.html#a87e9ac4ae5829b092262223256141a29">filter.h</a></li>
<li>LZMA_STR_NO_SPACES&#160;:&#160;<a class="el" href="filter_8h.html#ac0113c47caf98a735db2297936c5e857">filter.h</a></li>
<li>LZMA_STR_NO_VALIDATION&#160;:&#160;<a class="el" href="filter_8h.html#adc33f4c0c7b5d3ae36acc0437a904339">filter.h</a></li>
<li>LZMA_STREAM_HEADER_SIZE&#160;:&#160;<a class="el" href="stream__flags_8h.html#ada7e0a4f5e7146f547962cb9e9ef08ee">stream_flags.h</a></li>
<li>LZMA_STREAM_INIT&#160;:&#160;<a class="el" href="base_8h.html#af31f0c8b6f14359cd082b9559f7f3e01">base.h</a></li>
<li>LZMA_TELL_ANY_CHECK&#160;:&#160;<a class="el" href="container_8h.html#a0bdde702a77ff42b90a99c0bf4147b6b">container.h</a></li>
<li>LZMA_TELL_NO_CHECK&#160;:&#160;<a class="el" href="container_8h.html#ada9cd20febb28b5ed6656de9184a86e9">container.h</a></li>
<li>LZMA_TELL_UNSUPPORTED_CHECK&#160;:&#160;<a class="el" href="container_8h.html#ae21fb746037c82735d40d428c462e078">container.h</a></li>
<li>LZMA_VERSION&#160;:&#160;<a class="el" href="version_8h.html#a156c47ff34aa0c2b726d0daf799f10a0">version.h</a></li>
<li>LZMA_VERSION_COMMIT&#160;:&#160;<a class="el" href="version_8h.html#a7fd6169ff15ac7f01f94970359a331ea">version.h</a></li>
<li>LZMA_VERSION_MAJOR&#160;:&#160;<a class="el" href="version_8h.html#aa0f450c9d3b0ff5f88b55888ed55701f">version.h</a></li>
<li>LZMA_VERSION_MINOR&#160;:&#160;<a class="el" href="version_8h.html#af8fd295cf8aa349b0731423ad7a56134">version.h</a></li>
<li>LZMA_VERSION_PATCH&#160;:&#160;<a class="el" href="version_8h.html#a8b550373cbff381f15d4308b852a3c2a">version.h</a></li>
<li>LZMA_VERSION_STABILITY&#160;:&#160;<a class="el" href="version_8h.html#ae289abe5dcc203c7cda9f6a9a2f36b3a">version.h</a></li>
<li>LZMA_VERSION_STRING&#160;:&#160;<a class="el" href="version_8h.html#a57bb143c993c305a53e9aade831a546c">version.h</a></li>
<li>LZMA_VLI_BYTES_MAX&#160;:&#160;<a class="el" href="vli_8h.html#a063ecff4133aa2f8899b9fa3fdefd310">vli.h</a></li>
<li>LZMA_VLI_C&#160;:&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">vli.h</a></li>
<li>lzma_vli_is_valid&#160;:&#160;<a class="el" href="vli_8h.html#a4f67ed698215d865a2b87a95ab1320dd">vli.h</a></li>
<li>LZMA_VLI_MAX&#160;:&#160;<a class="el" href="vli_8h.html#a7b782528bd1934db7c020adbedb20ec9">vli.h</a></li>
<li>LZMA_VLI_UNKNOWN&#160;:&#160;<a class="el" href="vli_8h.html#a5a4b28254a30c859018b896ed371d69a">vli.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.7
</small></address>
</body>
</html>
