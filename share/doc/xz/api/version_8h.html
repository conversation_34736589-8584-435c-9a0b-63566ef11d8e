<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.7"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/version.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>


<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.4.5</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.7 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">version.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Version number.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:aa0f450c9d3b0ff5f88b55888ed55701f"><td class="memItemLeft" align="right" valign="top"><a id="aa0f450c9d3b0ff5f88b55888ed55701f" name="aa0f450c9d3b0ff5f88b55888ed55701f"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_VERSION_MAJOR</b>&#160;&#160;&#160;5</td></tr>
<tr class="memdesc:aa0f450c9d3b0ff5f88b55888ed55701f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Major version number of the liblzma release. <br /></td></tr>
<tr class="separator:aa0f450c9d3b0ff5f88b55888ed55701f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af8fd295cf8aa349b0731423ad7a56134"><td class="memItemLeft" align="right" valign="top"><a id="af8fd295cf8aa349b0731423ad7a56134" name="af8fd295cf8aa349b0731423ad7a56134"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_VERSION_MINOR</b>&#160;&#160;&#160;4</td></tr>
<tr class="memdesc:af8fd295cf8aa349b0731423ad7a56134"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minor version number of the liblzma release. <br /></td></tr>
<tr class="separator:af8fd295cf8aa349b0731423ad7a56134"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b550373cbff381f15d4308b852a3c2a"><td class="memItemLeft" align="right" valign="top"><a id="a8b550373cbff381f15d4308b852a3c2a" name="a8b550373cbff381f15d4308b852a3c2a"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_VERSION_PATCH</b>&#160;&#160;&#160;5</td></tr>
<tr class="memdesc:a8b550373cbff381f15d4308b852a3c2a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Patch version number of the liblzma release. <br /></td></tr>
<tr class="separator:a8b550373cbff381f15d4308b852a3c2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae289abe5dcc203c7cda9f6a9a2f36b3a"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="version_8h.html#ae289abe5dcc203c7cda9f6a9a2f36b3a">LZMA_VERSION_STABILITY</a>&#160;&#160;&#160;LZMA_VERSION_STABILITY_STABLE</td></tr>
<tr class="memdesc:ae289abe5dcc203c7cda9f6a9a2f36b3a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Version stability marker.  <br /></td></tr>
<tr class="separator:ae289abe5dcc203c7cda9f6a9a2f36b3a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7fd6169ff15ac7f01f94970359a331ea"><td class="memItemLeft" align="right" valign="top"><a id="a7fd6169ff15ac7f01f94970359a331ea" name="a7fd6169ff15ac7f01f94970359a331ea"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_VERSION_COMMIT</b>&#160;&#160;&#160;&quot;&quot;</td></tr>
<tr class="memdesc:a7fd6169ff15ac7f01f94970359a331ea"><td class="mdescLeft">&#160;</td><td class="mdescRight">Commit version number of the liblzma release. <br /></td></tr>
<tr class="separator:a7fd6169ff15ac7f01f94970359a331ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5bc145ed7d9149eadb77e547ae8f1c5f"><td class="memItemLeft" align="right" valign="top"><a id="a5bc145ed7d9149eadb77e547ae8f1c5f" name="a5bc145ed7d9149eadb77e547ae8f1c5f"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_VERSION_STABILITY_ALPHA</b>&#160;&#160;&#160;0</td></tr>
<tr class="separator:a5bc145ed7d9149eadb77e547ae8f1c5f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad53a96c53713062b4380f01fb115cd48"><td class="memItemLeft" align="right" valign="top"><a id="ad53a96c53713062b4380f01fb115cd48" name="ad53a96c53713062b4380f01fb115cd48"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_VERSION_STABILITY_BETA</b>&#160;&#160;&#160;1</td></tr>
<tr class="separator:ad53a96c53713062b4380f01fb115cd48"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a97f7ed9e90264388614837baf97a4d3b"><td class="memItemLeft" align="right" valign="top"><a id="a97f7ed9e90264388614837baf97a4d3b" name="a97f7ed9e90264388614837baf97a4d3b"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_VERSION_STABILITY_STABLE</b>&#160;&#160;&#160;2</td></tr>
<tr class="separator:a97f7ed9e90264388614837baf97a4d3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a156c47ff34aa0c2b726d0daf799f10a0"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="version_8h.html#a156c47ff34aa0c2b726d0daf799f10a0">LZMA_VERSION</a></td></tr>
<tr class="memdesc:a156c47ff34aa0c2b726d0daf799f10a0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compile-time version number.  <br /></td></tr>
<tr class="separator:a156c47ff34aa0c2b726d0daf799f10a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a55a97e55fedce2c148796047ddc88c96"><td class="memItemLeft" align="right" valign="top"><a id="a55a97e55fedce2c148796047ddc88c96" name="a55a97e55fedce2c148796047ddc88c96"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_VERSION_STABILITY_STRING</b>&#160;&#160;&#160;&quot;alpha&quot;</td></tr>
<tr class="separator:a55a97e55fedce2c148796047ddc88c96"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b89024f7a04da9b754abee2afe6df23"><td class="memItemLeft" align="right" valign="top"><a id="a0b89024f7a04da9b754abee2afe6df23" name="a0b89024f7a04da9b754abee2afe6df23"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_VERSION_STRING_C_</b>(major,  minor,  patch,  stability,  commit)&#160;&#160;&#160;		#major &quot;.&quot; #minor &quot;.&quot; #patch stability commit</td></tr>
<tr class="separator:a0b89024f7a04da9b754abee2afe6df23"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad5614eaf4c2e9408a99bc2137c65ed17"><td class="memItemLeft" align="right" valign="top"><a id="ad5614eaf4c2e9408a99bc2137c65ed17" name="ad5614eaf4c2e9408a99bc2137c65ed17"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_VERSION_STRING_C</b>(major,  minor,  patch,  stability,  commit)&#160;&#160;&#160;		LZMA_VERSION_STRING_C_(major, minor, patch, stability, commit)</td></tr>
<tr class="separator:ad5614eaf4c2e9408a99bc2137c65ed17"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a57bb143c993c305a53e9aade831a546c"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="version_8h.html#a57bb143c993c305a53e9aade831a546c">LZMA_VERSION_STRING</a></td></tr>
<tr class="memdesc:a57bb143c993c305a53e9aade831a546c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compile-time version as a string.  <br /></td></tr>
<tr class="separator:a57bb143c993c305a53e9aade831a546c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a72f929c9b9e8e730b790b3f8c80c3c80"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="version_8h.html#a72f929c9b9e8e730b790b3f8c80c3c80">lzma_version_number</a> (void) lzma_nothrow lzma_attr_const</td></tr>
<tr class="memdesc:a72f929c9b9e8e730b790b3f8c80c3c80"><td class="mdescLeft">&#160;</td><td class="mdescRight">Run-time version number as an integer.  <br /></td></tr>
<tr class="separator:a72f929c9b9e8e730b790b3f8c80c3c80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8998c1d8b4b5c2c1218bdfd58fdb1baa"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="version_8h.html#a8998c1d8b4b5c2c1218bdfd58fdb1baa">lzma_version_string</a> (void) lzma_nothrow lzma_attr_const</td></tr>
<tr class="memdesc:a8998c1d8b4b5c2c1218bdfd58fdb1baa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Run-time version as a string.  <br /></td></tr>
<tr class="separator:a8998c1d8b4b5c2c1218bdfd58fdb1baa"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Version number. </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead. </dd></dl>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="ae289abe5dcc203c7cda9f6a9a2f36b3a" name="ae289abe5dcc203c7cda9f6a9a2f36b3a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae289abe5dcc203c7cda9f6a9a2f36b3a">&#9670;&#160;</a></span>LZMA_VERSION_STABILITY</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_VERSION_STABILITY&#160;&#160;&#160;LZMA_VERSION_STABILITY_STABLE</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Version stability marker. </p>
<p>This will always be one of three values:</p><ul>
<li>LZMA_VERSION_STABILITY_ALPHA</li>
<li>LZMA_VERSION_STABILITY_BETA</li>
<li>LZMA_VERSION_STABILITY_STABLE </li>
</ul>

</div>
</div>
<a id="a156c47ff34aa0c2b726d0daf799f10a0" name="a156c47ff34aa0c2b726d0daf799f10a0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a156c47ff34aa0c2b726d0daf799f10a0">&#9670;&#160;</a></span>LZMA_VERSION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_VERSION</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">                (<a class="code hl_define" href="version_8h.html#aa0f450c9d3b0ff5f88b55888ed55701f" title="Major version number of the liblzma release.">LZMA_VERSION_MAJOR</a> * UINT32_C(10000000) \</div>
<div class="line">                + <a class="code hl_define" href="version_8h.html#af8fd295cf8aa349b0731423ad7a56134" title="Minor version number of the liblzma release.">LZMA_VERSION_MINOR</a> * UINT32_C(10000) \</div>
<div class="line">                + <a class="code hl_define" href="version_8h.html#a8b550373cbff381f15d4308b852a3c2a" title="Patch version number of the liblzma release.">LZMA_VERSION_PATCH</a> * UINT32_C(10) \</div>
<div class="line">                + <a class="code hl_define" href="version_8h.html#ae289abe5dcc203c7cda9f6a9a2f36b3a" title="Version stability marker.">LZMA_VERSION_STABILITY</a>)</div>
</div><!-- fragment -->
<p>Compile-time version number. </p>
<p>The version number is of format xyyyzzzs where</p><ul>
<li>x = major</li>
<li>yyy = minor</li>
<li>zzz = revision</li>
<li>s indicates stability: 0 = alpha, 1 = beta, 2 = stable</li>
</ul>
<p>The same xyyyzzz triplet is never reused with different stability levels. For example, if 5.1.0alpha has been released, there will never be 5.1.0beta or 5.1.0 stable.</p>
<dl class="section note"><dt>Note</dt><dd>The version number of liblzma has nothing to with the version number of Igor Pavlov's LZMA SDK. </dd></dl>

</div>
</div>
<a id="a57bb143c993c305a53e9aade831a546c" name="a57bb143c993c305a53e9aade831a546c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a57bb143c993c305a53e9aade831a546c">&#9670;&#160;</a></span>LZMA_VERSION_STRING</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_VERSION_STRING</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">                LZMA_VERSION_STRING_C( \</div>
<div class="line">                <a class="code hl_define" href="version_8h.html#aa0f450c9d3b0ff5f88b55888ed55701f" title="Major version number of the liblzma release.">LZMA_VERSION_MAJOR</a>, <a class="code hl_define" href="version_8h.html#af8fd295cf8aa349b0731423ad7a56134" title="Minor version number of the liblzma release.">LZMA_VERSION_MINOR</a>, \</div>
<div class="line">                <a class="code hl_define" href="version_8h.html#a8b550373cbff381f15d4308b852a3c2a" title="Patch version number of the liblzma release.">LZMA_VERSION_PATCH</a>, LZMA_VERSION_STABILITY_STRING, \</div>
<div class="line">                <a class="code hl_define" href="version_8h.html#a7fd6169ff15ac7f01f94970359a331ea" title="Commit version number of the liblzma release.">LZMA_VERSION_COMMIT</a>)</div>
</div><!-- fragment -->
<p>Compile-time version as a string. </p>
<p>This can be for example "4.999.5alpha", "4.999.8beta", or "5.0.0" (stable versions don't have any "stable" suffix). In future, a snapshot built from source code repository may include an additional suffix, for example "4.999.8beta-21-g1d92". The commit ID won't be available in numeric form in LZMA_VERSION macro. </p>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="a72f929c9b9e8e730b790b3f8c80c3c80" name="a72f929c9b9e8e730b790b3f8c80c3c80"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a72f929c9b9e8e730b790b3f8c80c3c80">&#9670;&#160;</a></span>lzma_version_number()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_version_number </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Run-time version number as an integer. </p>
<p>This allows an application to compare if it was built against the same, older, or newer version of liblzma that is currently running.</p>
<dl class="section return"><dt>Returns</dt><dd>The value of LZMA_VERSION macro at the compile time of liblzma </dd></dl>

</div>
</div>
<a id="a8998c1d8b4b5c2c1218bdfd58fdb1baa" name="a8998c1d8b4b5c2c1218bdfd58fdb1baa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8998c1d8b4b5c2c1218bdfd58fdb1baa">&#9670;&#160;</a></span>lzma_version_string()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char * lzma_version_string </td>
          <td>(</td>
          <td class="paramtype">void&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Run-time version as a string. </p>
<p>This function may be useful to display which version of liblzma an application is currently using.</p>
<dl class="section return"><dt>Returns</dt><dd>Run-time version of liblzma </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.7
</small></address>
</body>
</html>
