<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.7"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/lzma12.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>


<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.4.5</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.7 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Data&#160;Structures</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>Globals</span></a></li>
    </ul>
  </div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#enum-members">Enumerations</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">lzma12.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>LZMA1 and LZMA2 filters.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__options__lzma.html">lzma_options_lzma</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Options specific to the LZMA1 and LZMA2 filters.  <a href="structlzma__options__lzma.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:accedd16abcb758e7f748bac1102abda9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="lzma12_8h.html#accedd16abcb758e7f748bac1102abda9">LZMA_FILTER_LZMA1</a>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x4000000000000001)</td></tr>
<tr class="memdesc:accedd16abcb758e7f748bac1102abda9"><td class="mdescLeft">&#160;</td><td class="mdescRight">LZMA1 Filter ID (for raw encoder/decoder only, not in .xz)  <br /></td></tr>
<tr class="separator:accedd16abcb758e7f748bac1102abda9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a98a7fd42aa78a273a6b138629e46772d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="lzma12_8h.html#a98a7fd42aa78a273a6b138629e46772d">LZMA_FILTER_LZMA1EXT</a>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x4000000000000002)</td></tr>
<tr class="memdesc:a98a7fd42aa78a273a6b138629e46772d"><td class="mdescLeft">&#160;</td><td class="mdescRight">LZMA1 Filter ID with extended options (for raw encoder/decoder)  <br /></td></tr>
<tr class="separator:a98a7fd42aa78a273a6b138629e46772d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a04f9d9a018a47cc99491e6e94e92f96b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="lzma12_8h.html#a04f9d9a018a47cc99491e6e94e92f96b">LZMA_FILTER_LZMA2</a>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x21)</td></tr>
<tr class="memdesc:a04f9d9a018a47cc99491e6e94e92f96b"><td class="mdescLeft">&#160;</td><td class="mdescRight">LZMA2 Filter ID.  <br /></td></tr>
<tr class="separator:a04f9d9a018a47cc99491e6e94e92f96b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a221f69f8a1f604c1aa5d79ae8afec1e0"><td class="memItemLeft" align="right" valign="top"><a id="a221f69f8a1f604c1aa5d79ae8afec1e0" name="a221f69f8a1f604c1aa5d79ae8afec1e0"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_DICT_SIZE_MIN</b>&#160;&#160;&#160;UINT32_C(4096)</td></tr>
<tr class="separator:a221f69f8a1f604c1aa5d79ae8afec1e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abadf9a9599c106a1381756219c16f8ee"><td class="memItemLeft" align="right" valign="top"><a id="abadf9a9599c106a1381756219c16f8ee" name="abadf9a9599c106a1381756219c16f8ee"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_DICT_SIZE_DEFAULT</b>&#160;&#160;&#160;(UINT32_C(1) &lt;&lt; 23)</td></tr>
<tr class="separator:abadf9a9599c106a1381756219c16f8ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36552f4d49390d060692424817222d9c"><td class="memItemLeft" align="right" valign="top"><a id="a36552f4d49390d060692424817222d9c" name="a36552f4d49390d060692424817222d9c"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_LCLP_MIN</b>&#160;&#160;&#160;0</td></tr>
<tr class="separator:a36552f4d49390d060692424817222d9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6890226055b5069e83835af1fb804654"><td class="memItemLeft" align="right" valign="top"><a id="a6890226055b5069e83835af1fb804654" name="a6890226055b5069e83835af1fb804654"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_LCLP_MAX</b>&#160;&#160;&#160;4</td></tr>
<tr class="separator:a6890226055b5069e83835af1fb804654"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af3769f40103ec40516e1235c4b912d05"><td class="memItemLeft" align="right" valign="top"><a id="af3769f40103ec40516e1235c4b912d05" name="af3769f40103ec40516e1235c4b912d05"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_LC_DEFAULT</b>&#160;&#160;&#160;3</td></tr>
<tr class="separator:af3769f40103ec40516e1235c4b912d05"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa8ed908b2c1a88f3a76c47610036e68"><td class="memItemLeft" align="right" valign="top"><a id="afa8ed908b2c1a88f3a76c47610036e68" name="afa8ed908b2c1a88f3a76c47610036e68"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_LP_DEFAULT</b>&#160;&#160;&#160;0</td></tr>
<tr class="separator:afa8ed908b2c1a88f3a76c47610036e68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11ed595416a499021cd571f08bbd38bc"><td class="memItemLeft" align="right" valign="top"><a id="a11ed595416a499021cd571f08bbd38bc" name="a11ed595416a499021cd571f08bbd38bc"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_PB_MIN</b>&#160;&#160;&#160;0</td></tr>
<tr class="separator:a11ed595416a499021cd571f08bbd38bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e2661cef9dbcbb309239ec243a7c034"><td class="memItemLeft" align="right" valign="top"><a id="a5e2661cef9dbcbb309239ec243a7c034" name="a5e2661cef9dbcbb309239ec243a7c034"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_PB_MAX</b>&#160;&#160;&#160;4</td></tr>
<tr class="separator:a5e2661cef9dbcbb309239ec243a7c034"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5072e13ce7b6abcdffdf6456da5959a1"><td class="memItemLeft" align="right" valign="top"><a id="a5072e13ce7b6abcdffdf6456da5959a1" name="a5072e13ce7b6abcdffdf6456da5959a1"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_PB_DEFAULT</b>&#160;&#160;&#160;2</td></tr>
<tr class="separator:a5072e13ce7b6abcdffdf6456da5959a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad088307349f27b38fda0d1f21588adc9"><td class="memItemLeft" align="right" valign="top"><a id="ad088307349f27b38fda0d1f21588adc9" name="ad088307349f27b38fda0d1f21588adc9"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_LZMA1EXT_ALLOW_EOPM</b>&#160;&#160;&#160;UINT32_C(0x01)</td></tr>
<tr class="separator:ad088307349f27b38fda0d1f21588adc9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73ed0293db4e59d73a702d66fef537c3"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="lzma12_8h.html#a73ed0293db4e59d73a702d66fef537c3">lzma_set_ext_size</a>(opt_lzma2,  u64size)</td></tr>
<tr class="memdesc:a73ed0293db4e59d73a702d66fef537c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Macro to set the 64-bit uncompressed size in ext_size_*.  <br /></td></tr>
<tr class="separator:a73ed0293db4e59d73a702d66fef537c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="enum-members" name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:acf740075f86fa61dc408d6d0dbf8fa80"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80">lzma_match_finder</a> { <br />
&#160;&#160;<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a6eb38f634021a192cada8a978b5de93b">LZMA_MF_HC3</a> = 0x03
, <a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a0944620f4949289c2ebde613cae12b04">LZMA_MF_HC4</a> = 0x04
, <a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a7ab212446c3f6520f5c33ccfa4b3386a">LZMA_MF_BT2</a> = 0x12
, <a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a983ecc59bf3e07a7c43fea551ea11865">LZMA_MF_BT3</a> = 0x13
, <br />
&#160;&#160;<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a468c32cdea9861d1ff98478364e6c547">LZMA_MF_BT4</a> = 0x14
<br />
 }</td></tr>
<tr class="memdesc:acf740075f86fa61dc408d6d0dbf8fa80"><td class="mdescLeft">&#160;</td><td class="mdescRight">Match finders.  <a href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80">More...</a><br /></td></tr>
<tr class="separator:acf740075f86fa61dc408d6d0dbf8fa80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1032316e3075c2c8086fb17104b91866"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866">lzma_mode</a> { <a class="el" href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866ac8c0926a91b4f756e11121efd30648cc">LZMA_MODE_FAST</a> = 1
, <a class="el" href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866ad37225f30d5cd21fc8bb3eaba283bbf9">LZMA_MODE_NORMAL</a> = 2
 }</td></tr>
<tr class="memdesc:a1032316e3075c2c8086fb17104b91866"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compression modes.  <a href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866">More...</a><br /></td></tr>
<tr class="separator:a1032316e3075c2c8086fb17104b91866"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:aefba1f7214ddcf8cd408a0702e8642b5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="lzma12_8h.html#aefba1f7214ddcf8cd408a0702e8642b5">lzma_mf_is_supported</a> (<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80">lzma_match_finder</a> match_finder) lzma_nothrow lzma_attr_const</td></tr>
<tr class="memdesc:aefba1f7214ddcf8cd408a0702e8642b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Test if given match finder is supported.  <br /></td></tr>
<tr class="separator:aefba1f7214ddcf8cd408a0702e8642b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad1add1c2600fdbb3d737e4fb3465dfcb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="lzma12_8h.html#ad1add1c2600fdbb3d737e4fb3465dfcb">lzma_mode_is_supported</a> (<a class="el" href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866">lzma_mode</a> mode) lzma_nothrow lzma_attr_const</td></tr>
<tr class="memdesc:ad1add1c2600fdbb3d737e4fb3465dfcb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Test if given compression mode is supported.  <br /></td></tr>
<tr class="separator:ad1add1c2600fdbb3d737e4fb3465dfcb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa62c28944fe3575653a4c25780400d77"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="lzma12_8h.html#aa62c28944fe3575653a4c25780400d77">lzma_lzma_preset</a> (<a class="el" href="structlzma__options__lzma.html">lzma_options_lzma</a> *options, uint32_t preset) lzma_nothrow</td></tr>
<tr class="memdesc:aa62c28944fe3575653a4c25780400d77"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a compression preset to <a class="el" href="structlzma__options__lzma.html" title="Options specific to the LZMA1 and LZMA2 filters.">lzma_options_lzma</a> structure.  <br /></td></tr>
<tr class="separator:aa62c28944fe3575653a4c25780400d77"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>LZMA1 and LZMA2 filters. </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead. </dd></dl>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="accedd16abcb758e7f748bac1102abda9" name="accedd16abcb758e7f748bac1102abda9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#accedd16abcb758e7f748bac1102abda9">&#9670;&#160;</a></span>LZMA_FILTER_LZMA1</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_FILTER_LZMA1&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x4000000000000001)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>LZMA1 Filter ID (for raw encoder/decoder only, not in .xz) </p>
<p>LZMA1 is the very same thing as what was called just LZMA in LZMA Utils, 7-Zip, and LZMA SDK. It's called LZMA1 here to prevent developers from accidentally using LZMA when they actually want LZMA2. </p>

</div>
</div>
<a id="a98a7fd42aa78a273a6b138629e46772d" name="a98a7fd42aa78a273a6b138629e46772d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a98a7fd42aa78a273a6b138629e46772d">&#9670;&#160;</a></span>LZMA_FILTER_LZMA1EXT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_FILTER_LZMA1EXT&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x4000000000000002)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>LZMA1 Filter ID with extended options (for raw encoder/decoder) </p>
<p>This is like LZMA_FILTER_LZMA1 but with this ID a few extra options are supported in the <a class="el" href="structlzma__options__lzma.html" title="Options specific to the LZMA1 and LZMA2 filters.">lzma_options_lzma</a> structure:</p>
<ul>
<li>A flag to tell the encoder if the end of payload marker (EOPM) alias end of stream (EOS) marker must be written at the end of the stream. In contrast, LZMA_FILTER_LZMA1 always writes the end marker.</li>
<li>Decoder needs to be told the uncompressed size of the stream or that it is unknown (using the special value UINT64_MAX). If the size is known, a flag can be set to allow the presence of the end marker anyway. In contrast, LZMA_FILTER_LZMA1 always behaves as if the uncompressed size was unknown.</li>
</ul>
<p>This allows handling file formats where LZMA1 streams are used but where the end marker isn't allowed or where it might not (always) be present. This extended LZMA1 functionality is provided as a Filter ID for raw encoder and decoder instead of adding new encoder and decoder initialization functions because this way it is possible to also use extra filters, for example, LZMA_FILTER_X86 in a filter chain with LZMA_FILTER_LZMA1EXT, which might be needed to handle some file formats. </p>

</div>
</div>
<a id="a04f9d9a018a47cc99491e6e94e92f96b" name="a04f9d9a018a47cc99491e6e94e92f96b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a04f9d9a018a47cc99491e6e94e92f96b">&#9670;&#160;</a></span>LZMA_FILTER_LZMA2</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_FILTER_LZMA2&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x21)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>LZMA2 Filter ID. </p>
<p>Usually you want this instead of LZMA1. Compared to LZMA1, LZMA2 adds support for LZMA_SYNC_FLUSH, uncompressed chunks (smaller expansion when trying to compress incompressible data), possibility to change lc/lp/pb in the middle of encoding, and some other internal improvements. </p>

</div>
</div>
<a id="a73ed0293db4e59d73a702d66fef537c3" name="a73ed0293db4e59d73a702d66fef537c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a73ed0293db4e59d73a702d66fef537c3">&#9670;&#160;</a></span>lzma_set_ext_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define lzma_set_ext_size</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">opt_lzma2, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">u64size&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><span class="keywordflow">do</span> { \</div>
<div class="line">        (opt_lzma2).ext_size_low = (uint32_t)(u64size); \</div>
<div class="line">        (opt_lzma2).ext_size_high = (uint32_t)((uint64_t)(u64size) &gt;&gt; 32); \</div>
<div class="line">} <span class="keywordflow">while</span> (0)</div>
</div><!-- fragment -->
<p>Macro to set the 64-bit uncompressed size in ext_size_*. </p>
<p>This might be convenient when decoding using LZMA_FILTER_LZMA1EXT. This isn't used with LZMA_FILTER_LZMA1 or LZMA_FILTER_LZMA2. </p>

</div>
</div>
<h2 class="groupheader">Enumeration Type Documentation</h2>
<a id="acf740075f86fa61dc408d6d0dbf8fa80" name="acf740075f86fa61dc408d6d0dbf8fa80"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acf740075f86fa61dc408d6d0dbf8fa80">&#9670;&#160;</a></span>lzma_match_finder</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80">lzma_match_finder</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Match finders. </p>
<p>Match finder has major effect on both speed and compression ratio. Usually hash chains are faster than binary trees.</p>
<p>If you will use LZMA_SYNC_FLUSH often, the hash chains may be a better choice, because binary trees get much higher compression ratio penalty with LZMA_SYNC_FLUSH.</p>
<p>The memory usage formulas are only rough estimates, which are closest to reality when dict_size is a power of two. The formulas are more complex in reality, and can also change a little between liblzma versions. Use <a class="el" href="filter_8h.html#a730f9391e85a5979bcd1b32643ae7176" title="Calculate approximate memory requirements for raw encoder.">lzma_raw_encoder_memusage()</a> to get more accurate estimate of memory usage. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="acf740075f86fa61dc408d6d0dbf8fa80a6eb38f634021a192cada8a978b5de93b" name="acf740075f86fa61dc408d6d0dbf8fa80a6eb38f634021a192cada8a978b5de93b"></a>LZMA_MF_HC3&#160;</td><td class="fielddoc"><p>Hash Chain with 2- and 3-byte hashing. </p>
<p>Minimum nice_len: 3</p>
<p>Memory usage:</p><ul>
<li>dict_size &lt;= 16 MiB: dict_size * 7.5</li>
<li>dict_size &gt; 16 MiB: dict_size * 5.5 + 64 MiB </li>
</ul>
</td></tr>
<tr><td class="fieldname"><a id="acf740075f86fa61dc408d6d0dbf8fa80a0944620f4949289c2ebde613cae12b04" name="acf740075f86fa61dc408d6d0dbf8fa80a0944620f4949289c2ebde613cae12b04"></a>LZMA_MF_HC4&#160;</td><td class="fielddoc"><p>Hash Chain with 2-, 3-, and 4-byte hashing. </p>
<p>Minimum nice_len: 4</p>
<p>Memory usage:</p><ul>
<li>dict_size &lt;= 32 MiB: dict_size * 7.5</li>
<li>dict_size &gt; 32 MiB: dict_size * 6.5 </li>
</ul>
</td></tr>
<tr><td class="fieldname"><a id="acf740075f86fa61dc408d6d0dbf8fa80a7ab212446c3f6520f5c33ccfa4b3386a" name="acf740075f86fa61dc408d6d0dbf8fa80a7ab212446c3f6520f5c33ccfa4b3386a"></a>LZMA_MF_BT2&#160;</td><td class="fielddoc"><p>Binary Tree with 2-byte hashing. </p>
<p>Minimum nice_len: 2</p>
<p>Memory usage: dict_size * 9.5 </p>
</td></tr>
<tr><td class="fieldname"><a id="acf740075f86fa61dc408d6d0dbf8fa80a983ecc59bf3e07a7c43fea551ea11865" name="acf740075f86fa61dc408d6d0dbf8fa80a983ecc59bf3e07a7c43fea551ea11865"></a>LZMA_MF_BT3&#160;</td><td class="fielddoc"><p>Binary Tree with 2- and 3-byte hashing. </p>
<p>Minimum nice_len: 3</p>
<p>Memory usage:</p><ul>
<li>dict_size &lt;= 16 MiB: dict_size * 11.5</li>
<li>dict_size &gt; 16 MiB: dict_size * 9.5 + 64 MiB </li>
</ul>
</td></tr>
<tr><td class="fieldname"><a id="acf740075f86fa61dc408d6d0dbf8fa80a468c32cdea9861d1ff98478364e6c547" name="acf740075f86fa61dc408d6d0dbf8fa80a468c32cdea9861d1ff98478364e6c547"></a>LZMA_MF_BT4&#160;</td><td class="fielddoc"><p>Binary Tree with 2-, 3-, and 4-byte hashing. </p>
<p>Minimum nice_len: 4</p>
<p>Memory usage:</p><ul>
<li>dict_size &lt;= 32 MiB: dict_size * 11.5</li>
<li>dict_size &gt; 32 MiB: dict_size * 10.5 </li>
</ul>
</td></tr>
</table>

</div>
</div>
<a id="a1032316e3075c2c8086fb17104b91866" name="a1032316e3075c2c8086fb17104b91866"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1032316e3075c2c8086fb17104b91866">&#9670;&#160;</a></span>lzma_mode</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866">lzma_mode</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compression modes. </p>
<p>This selects the function used to analyze the data produced by the match finder. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a1032316e3075c2c8086fb17104b91866ac8c0926a91b4f756e11121efd30648cc" name="a1032316e3075c2c8086fb17104b91866ac8c0926a91b4f756e11121efd30648cc"></a>LZMA_MODE_FAST&#160;</td><td class="fielddoc"><p>Fast compression. </p>
<p>Fast mode is usually at its best when combined with a hash chain match finder. </p>
</td></tr>
<tr><td class="fieldname"><a id="a1032316e3075c2c8086fb17104b91866ad37225f30d5cd21fc8bb3eaba283bbf9" name="a1032316e3075c2c8086fb17104b91866ad37225f30d5cd21fc8bb3eaba283bbf9"></a>LZMA_MODE_NORMAL&#160;</td><td class="fielddoc"><p>Normal compression. </p>
<p>This is usually notably slower than fast mode. Use this together with binary tree match finders to expose the full potential of the LZMA1 or LZMA2 encoder. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="aefba1f7214ddcf8cd408a0702e8642b5" name="aefba1f7214ddcf8cd408a0702e8642b5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aefba1f7214ddcf8cd408a0702e8642b5">&#9670;&#160;</a></span>lzma_mf_is_supported()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a> lzma_mf_is_supported </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80">lzma_match_finder</a>&#160;</td>
          <td class="paramname"><em>match_finder</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Test if given match finder is supported. </p>
<p>It is safe to call this with a value that isn't listed in lzma_match_finder enumeration; the return value will be false.</p>
<p>There is no way to list which match finders are available in this particular liblzma version and build. It would be useless, because a new match finder, which the application developer wasn't aware, could require giving additional options to the encoder that the older match finders don't need.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">match_finder</td><td>Match finder ID</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>lzma_bool:<ul>
<li>true if the match finder is supported by this liblzma build.</li>
<li>false otherwise. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="ad1add1c2600fdbb3d737e4fb3465dfcb" name="ad1add1c2600fdbb3d737e4fb3465dfcb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad1add1c2600fdbb3d737e4fb3465dfcb">&#9670;&#160;</a></span>lzma_mode_is_supported()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a> lzma_mode_is_supported </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866">lzma_mode</a>&#160;</td>
          <td class="paramname"><em>mode</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Test if given compression mode is supported. </p>
<p>It is safe to call this with a value that isn't listed in lzma_mode enumeration; the return value will be false.</p>
<p>There is no way to list which modes are available in this particular liblzma version and build. It would be useless, because a new compression mode, which the application developer wasn't aware, could require giving additional options to the encoder that the older modes don't need.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">mode</td><td>Mode ID.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>lzma_bool:<ul>
<li>true if the compression mode is supported by this liblzma build.</li>
<li>false otherwise. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="aa62c28944fe3575653a4c25780400d77" name="aa62c28944fe3575653a4c25780400d77"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa62c28944fe3575653a4c25780400d77">&#9670;&#160;</a></span>lzma_lzma_preset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a> lzma_lzma_preset </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__options__lzma.html">lzma_options_lzma</a> *&#160;</td>
          <td class="paramname"><em>options</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>preset</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set a compression preset to <a class="el" href="structlzma__options__lzma.html" title="Options specific to the LZMA1 and LZMA2 filters.">lzma_options_lzma</a> structure. </p>
<p>0 is the fastest and 9 is the slowest. These match the switches -0 .. -9 of the xz command line tool. In addition, it is possible to bitwise-or flags to the preset. Currently only LZMA_PRESET_EXTREME is supported. The flags are defined in <a class="el" href="container_8h.html" title="File formats.">container.h</a>, because the flags are used also with <a class="el" href="container_8h.html#acbdad999c544872f0f5d242f0d1a4ed4" title="Initialize .xz Stream encoder using a preset number.">lzma_easy_encoder()</a>.</p>
<p>The preset levels are subject to changes between liblzma versions.</p>
<p>This function is available only if LZMA1 or LZMA2 encoder has been enabled when building liblzma.</p>
<p>If features (like certain match finders) have been disabled at build time, then the function may return success (false) even though the resulting LZMA1/LZMA2 options may not be usable for encoder initialization (LZMA_OPTIONS_ERROR).</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">options</td><td>Pointer to LZMA1 or LZMA2 options to be filled </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">preset</td><td>Preset level bitwse-ORed with preset flags</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>lzma_bool:<ul>
<li>true if the preset is not supported (failure).</li>
<li>false otherwise (success). </li>
</ul>
</dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.7
</small></address>
</body>
</html>
