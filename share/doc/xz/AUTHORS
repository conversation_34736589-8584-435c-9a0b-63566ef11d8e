
Authors of XZ Utils
===================

    XZ Utils is developed and maintained by <PERSON><PERSON>
    <<EMAIL>> and <PERSON><PERSON> <<EMAIL>>.

    Major parts of liblzma are based on code written by <PERSON>,
    specifically the LZMA SDK <https://7-zip.org/sdk.html>. Without
    this code, XZ Utils wouldn't exist.

    The SHA-256 implementation in liblzma is based on the code found from
    7-Zip <https://7-zip.org/>, which has a modified version of the SHA-256
    code found from Crypto++ <https://www.cryptopp.com/>. The SHA-256 code
    in Crypto++ was written by <PERSON> and <PERSON>.

    Some scripts have been adapted from gzip. The original versions
    were written by <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.
    <PERSON> helped adapting the scripts and their man pages for
    XZ Utils.

    The initial version of the threaded .xz decompressor was written
    by <PERSON>.

    The initial version of the .lz (lzip) decoder was written
    by <PERSON><PERSON><PERSON>.

    CLMUL-accelerated CRC code was contributed by <PERSON><PERSON>.

    Other authors:
      - <PERSON>
      - <PERSON>

    The GNU Autotools-based build system contains files from many authors,
    which I'm not trying to list here.

    Several people have contributed fixes or reported bugs. Most of them
    are mentioned in the file THANKS.

