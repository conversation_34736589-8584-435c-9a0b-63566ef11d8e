This is libquadmath.info, produced by makeinfo version 6.8 from
libquadmath.texi.

Copyright (C) 2010-2021 Free Software Foundation, Inc.

     Permission is granted to copy, distribute and/or modify this
     document under the terms of the GNU Free Documentation License,
     Version 1.2 or any later version published by the Free Software
     Foundation; with no Invariant Sections, with the Front-Cover Texts
     being "A GNU Manual," and with the Back-Cover Texts as in (a)
     below.  A copy of the license is included in the section entitled
     "GNU Free Documentation License."

     (a) The FSF's Back-Cover Text is: "You have the freedom to copy and
     modify this GNU manual.
INFO-DIR-SECTION GNU Libraries
START-INFO-DIR-ENTRY
* libquadmath: (libquadmath).                  GCC Quad-Precision Math Library
END-INFO-DIR-ENTRY

   This manual documents the GCC Quad-Precision Math Library API.

   Published by the Free Software Foundation 51 Franklin Street, Fifth
Floor Boston, MA 02110-1301 USA

   Copyright (C) 2010-2021 Free Software Foundation, Inc.

     Permission is granted to copy, distribute and/or modify this
     document under the terms of the GNU Free Documentation License,
     Version 1.2 or any later version published by the Free Software
     Foundation; with no Invariant Sections, with the Front-Cover Texts
     being "A GNU Manual," and with the Back-Cover Texts as in (a)
     below.  A copy of the license is included in the section entitled
     "GNU Free Documentation License."

     (a) The FSF's Back-Cover Text is: "You have the freedom to copy and
     modify this GNU manual.


File: libquadmath.info,  Node: Top,  Next: Typedef and constants,  Up: (dir)

Introduction
************

This manual documents the usage of libquadmath, the GCC Quad-Precision
Math Library Application Programming Interface (API).

* Menu:

* Typedef and constants::      Defined data types and constants
* Math Library Routines::      The Libquadmath math runtime application
                               programming interface.
* I/O Library Routines::       The Libquadmath I/O runtime application
                               programming interface.
* GNU Free Documentation License::
                               How you can copy and share this manual.
* Reporting Bugs::             How to report bugs in GCC Libquadmath.


File: libquadmath.info,  Node: Typedef and constants,  Next: Math Library Routines,  Prev: Top,  Up: Top

1 Typedef and constants
***********************

The following data type has been defined via 'typedef'.

'__complex128': '__float128'-based complex number

   The following macros are defined, which give the numeric limits of
the '__float128' data type.

'FLT128_MAX': largest finite number
'FLT128_MIN': smallest positive number with full precision
'FLT128_EPSILON': difference between 1 and the next larger
     representable number
'FLT128_DENORM_MIN': smallest positive denormalized number
'FLT128_MANT_DIG': number of digits in the mantissa (bit precision)
'FLT128_MIN_EXP': maximal negative exponent
'FLT128_MAX_EXP': maximal positive exponent
'FLT128_DIG': number of decimal digits in the mantissa
'FLT128_MIN_10_EXP': maximal negative decimal exponent
'FLT128_MAX_10_EXP': maximal positive decimal exponent

   The following mathematical constants of type '__float128' are
defined.

'M_Eq': the constant e (Euler's number)
'M_LOG2Eq': binary logarithm of 2
'M_LOG10Eq': common, decimal logarithm of 2
'M_LN2q': natural logarithm of 2
'M_LN10q': natural logarithm of 10
'M_PIq': pi
'M_PI_2q': pi divided by two
'M_PI_4q': pi divided by four
'M_1_PIq': one over pi
'M_2_PIq': one over two pi
'M_2_SQRTPIq': two over square root of pi
'M_SQRT2q': square root of 2
'M_SQRT1_2q': one over square root of 2


File: libquadmath.info,  Node: Math Library Routines,  Next: I/O Library Routines,  Prev: Typedef and constants,  Up: Top

2 Math Library Routines
***********************

The following mathematical functions are available:

'acosq': arc cosine function
'acoshq': inverse hyperbolic cosine function
'asinq': arc sine function
'asinhq': inverse hyperbolic sine function
'atanq': arc tangent function
'atanhq': inverse hyperbolic tangent function
'atan2q': arc tangent function
'cbrtq': cube root function
'ceilq': ceiling value function
'copysignq': copy sign of a number
'coshq': hyperbolic cosine function
'cosq': cosine function
'erfq': error function
'erfcq': complementary error function
'exp2q': base 2 exponential function
'expq': exponential function
'expm1q': exponential minus 1 function
'fabsq': absolute value function
'fdimq': positive difference function
'finiteq': check finiteness of value
'floorq': floor value function
'fmaq': fused multiply and add
'fmaxq': determine maximum of two values
'fminq': determine minimum of two values
'fmodq': remainder value function
'frexpq': extract mantissa and exponent
'hypotq': Eucledian distance function
'ilogbq': get exponent of the value
'isinfq': check for infinity
'isnanq': check for not a number
'issignalingq': check for signaling not a number
'j0q': Bessel function of the first kind, first order
'j1q': Bessel function of the first kind, second order
'jnq': Bessel function of the first kind, N-th order
'ldexpq': load exponent of the value
'lgammaq': logarithmic gamma function
'llrintq': round to nearest integer value
'llroundq': round to nearest integer value away from zero
'logbq': get exponent of the value
'logq': natural logarithm function
'log10q': base 10 logarithm function
'log1pq': compute natural logarithm of the value plus one
'log2q': base 2 logarithm function
'lrintq': round to nearest integer value
'lroundq': round to nearest integer value away from zero
'modfq': decompose the floating-point number
'nanq': return quiet NaN
'nearbyintq': round to nearest integer
'nextafterq': next representable floating-point number
'powq': power function
'remainderq': remainder function
'remquoq': remainder and part of quotient
'rintq': round-to-nearest integral value
'roundq': round-to-nearest integral value, return '__float128'
'scalblnq': compute exponent using 'FLT_RADIX'
'scalbnq': compute exponent using 'FLT_RADIX'
'signbitq': return sign bit
'sincosq': calculate sine and cosine simultaneously
'sinhq': hyperbolic sine function
'sinq': sine function
'sqrtq': square root function
'tanq': tangent function
'tanhq': hyperbolic tangent function
'tgammaq': true gamma function
'truncq': round to integer, towards zero
'y0q': Bessel function of the second kind, first order
'y1q': Bessel function of the second kind, second order
'ynq': Bessel function of the second kind, N-th order
'cabsq' complex absolute value function
'cargq': calculate the argument
'cimagq' imaginary part of complex number
'crealq': real part of complex number
'cacoshq': complex arc hyperbolic cosine function
'cacosq': complex arc cosine function
'casinhq': complex arc hyperbolic sine function
'casinq': complex arc sine function
'catanhq': complex arc hyperbolic tangent function
'catanq': complex arc tangent function
'ccosq' complex cosine function:
'ccoshq': complex hyperbolic cosine function
'cexpq': complex exponential function
'cexpiq': computes the exponential function of "i" times a
     real value
'clogq': complex natural logarithm
'clog10q': complex base 10 logarithm
'conjq': complex conjugate function
'cpowq': complex power function
'cprojq': project into Riemann Sphere
'csinq': complex sine function
'csinhq': complex hyperbolic sine function
'csqrtq': complex square root
'ctanq': complex tangent function
'ctanhq': complex hyperbolic tangent function


File: libquadmath.info,  Node: I/O Library Routines,  Next: GNU Free Documentation License,  Prev: Math Library Routines,  Up: Top

3 I/O Library Routines
**********************

* Menu:

* 'strtoflt128':          strtoflt128,          Convert from string
* 'quadmath_snprintf':    quadmath_snprintf,    Convert to string


File: libquadmath.info,  Node: strtoflt128,  Next: quadmath_snprintf,  Up: I/O Library Routines

3.1 'strtoflt128' -- Convert from string
========================================

The function 'strtoflt128' converts a string into a '__float128' number.

Syntax
     '__float128 strtoflt128 (const char *s, char **sp)'

_Arguments_:
     S           input string
     SP          the address of the next character in the string

     The argument SP contains, if not 'NULL', the address of the next
     character following the parts of the string, which have been read.

Example
          #include <quadmath.h>

          int main ()
          {
            __float128 r;

            r = strtoflt128 ("1.2345678", NULL);

            return 0;
          }


File: libquadmath.info,  Node: quadmath_snprintf,  Prev: strtoflt128,  Up: I/O Library Routines

3.2 'quadmath_snprintf' -- Convert to string
============================================

The function 'quadmath_snprintf' converts a '__float128' floating-point
number into a string.  It is a specialized alternative to 'snprintf',
where the format string is restricted to a single conversion specifier
with 'Q' modifier and conversion specifier 'e', 'E', 'f', 'F', 'g', 'G',
'a' or 'A', with no extra characters before or after the conversion
specifier.  The '%m$' or '*m$' style must not be used in the format.

Syntax
     'int quadmath_snprintf (char *s, size_t size, const char *format,
     ...)'

_Arguments_:
     S           output string
     SIZE        byte size of the string, including tailing NUL
     FORMAT      conversion specifier string

Note
     On some targets when supported by the C library hooks are installed
     for 'printf' family of functions, so that 'printf ("%Qe", 1.2Q);'
     etc. works too.

Example
          #include <quadmath.h>
          #include <stdlib.h>
          #include <stdio.h>

          int main ()
          {
            __float128 r;
            int prec = 20;
            int width = 46;
            char buf[128];

            r = 2.0q;
            r = sqrtq (r);
            int n = quadmath_snprintf (buf, sizeof buf, "%+-#*.20Qe", width, r);
            if ((size_t) n < sizeof buf)
              printf ("%s\n", buf);
              /* Prints: +1.41421356237309504880e+00 */
            quadmath_snprintf (buf, sizeof buf, "%Qa", r);
            if ((size_t) n < sizeof buf)
              printf ("%s\n", buf);
              /* Prints: 0x1.6a09e667f3bcc908b2fb1366ea96p+0 */
            n = quadmath_snprintf (NULL, 0, "%+-#46.*Qe", prec, r);
            if (n > -1)
              {
                char *str = malloc (n + 1);
                if (str)
                  {
                    quadmath_snprintf (str, n + 1, "%+-#46.*Qe", prec, r);
                    printf ("%s\n", str);
                    /* Prints: +1.41421356237309504880e+00 */
                  }
                free (str);
              }
            return 0;
          }


File: libquadmath.info,  Node: GNU Free Documentation License,  Next: Reporting Bugs,  Prev: I/O Library Routines,  Up: Top

GNU Free Documentation License
******************************

                     Version 1.3, 3 November 2008

     Copyright (C) 2000, 2001, 2002, 2007, 2008 Free Software Foundation, Inc.
     <http://fsf.org/>

     Everyone is permitted to copy and distribute verbatim copies
     of this license document, but changing it is not allowed.

  0. PREAMBLE

     The purpose of this License is to make a manual, textbook, or other
     functional and useful document "free" in the sense of freedom: to
     assure everyone the effective freedom to copy and redistribute it,
     with or without modifying it, either commercially or
     noncommercially.  Secondarily, this License preserves for the
     author and publisher a way to get credit for their work, while not
     being considered responsible for modifications made by others.

     This License is a kind of "copyleft", which means that derivative
     works of the document must themselves be free in the same sense.
     It complements the GNU General Public License, which is a copyleft
     license designed for free software.

     We have designed this License in order to use it for manuals for
     free software, because free software needs free documentation: a
     free program should come with manuals providing the same freedoms
     that the software does.  But this License is not limited to
     software manuals; it can be used for any textual work, regardless
     of subject matter or whether it is published as a printed book.  We
     recommend this License principally for works whose purpose is
     instruction or reference.

  1. APPLICABILITY AND DEFINITIONS

     This License applies to any manual or other work, in any medium,
     that contains a notice placed by the copyright holder saying it can
     be distributed under the terms of this License.  Such a notice
     grants a world-wide, royalty-free license, unlimited in duration,
     to use that work under the conditions stated herein.  The
     "Document", below, refers to any such manual or work.  Any member
     of the public is a licensee, and is addressed as "you".  You accept
     the license if you copy, modify or distribute the work in a way
     requiring permission under copyright law.

     A "Modified Version" of the Document means any work containing the
     Document or a portion of it, either copied verbatim, or with
     modifications and/or translated into another language.

     A "Secondary Section" is a named appendix or a front-matter section
     of the Document that deals exclusively with the relationship of the
     publishers or authors of the Document to the Document's overall
     subject (or to related matters) and contains nothing that could
     fall directly within that overall subject.  (Thus, if the Document
     is in part a textbook of mathematics, a Secondary Section may not
     explain any mathematics.)  The relationship could be a matter of
     historical connection with the subject or with related matters, or
     of legal, commercial, philosophical, ethical or political position
     regarding them.

     The "Invariant Sections" are certain Secondary Sections whose
     titles are designated, as being those of Invariant Sections, in the
     notice that says that the Document is released under this License.
     If a section does not fit the above definition of Secondary then it
     is not allowed to be designated as Invariant.  The Document may
     contain zero Invariant Sections.  If the Document does not identify
     any Invariant Sections then there are none.

     The "Cover Texts" are certain short passages of text that are
     listed, as Front-Cover Texts or Back-Cover Texts, in the notice
     that says that the Document is released under this License.  A
     Front-Cover Text may be at most 5 words, and a Back-Cover Text may
     be at most 25 words.

     A "Transparent" copy of the Document means a machine-readable copy,
     represented in a format whose specification is available to the
     general public, that is suitable for revising the document
     straightforwardly with generic text editors or (for images composed
     of pixels) generic paint programs or (for drawings) some widely
     available drawing editor, and that is suitable for input to text
     formatters or for automatic translation to a variety of formats
     suitable for input to text formatters.  A copy made in an otherwise
     Transparent file format whose markup, or absence of markup, has
     been arranged to thwart or discourage subsequent modification by
     readers is not Transparent.  An image format is not Transparent if
     used for any substantial amount of text.  A copy that is not
     "Transparent" is called "Opaque".

     Examples of suitable formats for Transparent copies include plain
     ASCII without markup, Texinfo input format, LaTeX input format,
     SGML or XML using a publicly available DTD, and standard-conforming
     simple HTML, PostScript or PDF designed for human modification.
     Examples of transparent image formats include PNG, XCF and JPG.
     Opaque formats include proprietary formats that can be read and
     edited only by proprietary word processors, SGML or XML for which
     the DTD and/or processing tools are not generally available, and
     the machine-generated HTML, PostScript or PDF produced by some word
     processors for output purposes only.

     The "Title Page" means, for a printed book, the title page itself,
     plus such following pages as are needed to hold, legibly, the
     material this License requires to appear in the title page.  For
     works in formats which do not have any title page as such, "Title
     Page" means the text near the most prominent appearance of the
     work's title, preceding the beginning of the body of the text.

     The "publisher" means any person or entity that distributes copies
     of the Document to the public.

     A section "Entitled XYZ" means a named subunit of the Document
     whose title either is precisely XYZ or contains XYZ in parentheses
     following text that translates XYZ in another language.  (Here XYZ
     stands for a specific section name mentioned below, such as
     "Acknowledgements", "Dedications", "Endorsements", or "History".)
     To "Preserve the Title" of such a section when you modify the
     Document means that it remains a section "Entitled XYZ" according
     to this definition.

     The Document may include Warranty Disclaimers next to the notice
     which states that this License applies to the Document.  These
     Warranty Disclaimers are considered to be included by reference in
     this License, but only as regards disclaiming warranties: any other
     implication that these Warranty Disclaimers may have is void and
     has no effect on the meaning of this License.

  2. VERBATIM COPYING

     You may copy and distribute the Document in any medium, either
     commercially or noncommercially, provided that this License, the
     copyright notices, and the license notice saying this License
     applies to the Document are reproduced in all copies, and that you
     add no other conditions whatsoever to those of this License.  You
     may not use technical measures to obstruct or control the reading
     or further copying of the copies you make or distribute.  However,
     you may accept compensation in exchange for copies.  If you
     distribute a large enough number of copies you must also follow the
     conditions in section 3.

     You may also lend copies, under the same conditions stated above,
     and you may publicly display copies.

  3. COPYING IN QUANTITY

     If you publish printed copies (or copies in media that commonly
     have printed covers) of the Document, numbering more than 100, and
     the Document's license notice requires Cover Texts, you must
     enclose the copies in covers that carry, clearly and legibly, all
     these Cover Texts: Front-Cover Texts on the front cover, and
     Back-Cover Texts on the back cover.  Both covers must also clearly
     and legibly identify you as the publisher of these copies.  The
     front cover must present the full title with all words of the title
     equally prominent and visible.  You may add other material on the
     covers in addition.  Copying with changes limited to the covers, as
     long as they preserve the title of the Document and satisfy these
     conditions, can be treated as verbatim copying in other respects.

     If the required texts for either cover are too voluminous to fit
     legibly, you should put the first ones listed (as many as fit
     reasonably) on the actual cover, and continue the rest onto
     adjacent pages.

     If you publish or distribute Opaque copies of the Document
     numbering more than 100, you must either include a machine-readable
     Transparent copy along with each Opaque copy, or state in or with
     each Opaque copy a computer-network location from which the general
     network-using public has access to download using public-standard
     network protocols a complete Transparent copy of the Document, free
     of added material.  If you use the latter option, you must take
     reasonably prudent steps, when you begin distribution of Opaque
     copies in quantity, to ensure that this Transparent copy will
     remain thus accessible at the stated location until at least one
     year after the last time you distribute an Opaque copy (directly or
     through your agents or retailers) of that edition to the public.

     It is requested, but not required, that you contact the authors of
     the Document well before redistributing any large number of copies,
     to give them a chance to provide you with an updated version of the
     Document.

  4. MODIFICATIONS

     You may copy and distribute a Modified Version of the Document
     under the conditions of sections 2 and 3 above, provided that you
     release the Modified Version under precisely this License, with the
     Modified Version filling the role of the Document, thus licensing
     distribution and modification of the Modified Version to whoever
     possesses a copy of it.  In addition, you must do these things in
     the Modified Version:

       A. Use in the Title Page (and on the covers, if any) a title
          distinct from that of the Document, and from those of previous
          versions (which should, if there were any, be listed in the
          History section of the Document).  You may use the same title
          as a previous version if the original publisher of that
          version gives permission.

       B. List on the Title Page, as authors, one or more persons or
          entities responsible for authorship of the modifications in
          the Modified Version, together with at least five of the
          principal authors of the Document (all of its principal
          authors, if it has fewer than five), unless they release you
          from this requirement.

       C. State on the Title page the name of the publisher of the
          Modified Version, as the publisher.

       D. Preserve all the copyright notices of the Document.

       E. Add an appropriate copyright notice for your modifications
          adjacent to the other copyright notices.

       F. Include, immediately after the copyright notices, a license
          notice giving the public permission to use the Modified
          Version under the terms of this License, in the form shown in
          the Addendum below.

       G. Preserve in that license notice the full lists of Invariant
          Sections and required Cover Texts given in the Document's
          license notice.

       H. Include an unaltered copy of this License.

       I. Preserve the section Entitled "History", Preserve its Title,
          and add to it an item stating at least the title, year, new
          authors, and publisher of the Modified Version as given on the
          Title Page.  If there is no section Entitled "History" in the
          Document, create one stating the title, year, authors, and
          publisher of the Document as given on its Title Page, then add
          an item describing the Modified Version as stated in the
          previous sentence.

       J. Preserve the network location, if any, given in the Document
          for public access to a Transparent copy of the Document, and
          likewise the network locations given in the Document for
          previous versions it was based on.  These may be placed in the
          "History" section.  You may omit a network location for a work
          that was published at least four years before the Document
          itself, or if the original publisher of the version it refers
          to gives permission.

       K. For any section Entitled "Acknowledgements" or "Dedications",
          Preserve the Title of the section, and preserve in the section
          all the substance and tone of each of the contributor
          acknowledgements and/or dedications given therein.

       L. Preserve all the Invariant Sections of the Document, unaltered
          in their text and in their titles.  Section numbers or the
          equivalent are not considered part of the section titles.

       M. Delete any section Entitled "Endorsements".  Such a section
          may not be included in the Modified Version.

       N. Do not retitle any existing section to be Entitled
          "Endorsements" or to conflict in title with any Invariant
          Section.

       O. Preserve any Warranty Disclaimers.

     If the Modified Version includes new front-matter sections or
     appendices that qualify as Secondary Sections and contain no
     material copied from the Document, you may at your option designate
     some or all of these sections as invariant.  To do this, add their
     titles to the list of Invariant Sections in the Modified Version's
     license notice.  These titles must be distinct from any other
     section titles.

     You may add a section Entitled "Endorsements", provided it contains
     nothing but endorsements of your Modified Version by various
     parties--for example, statements of peer review or that the text
     has been approved by an organization as the authoritative
     definition of a standard.

     You may add a passage of up to five words as a Front-Cover Text,
     and a passage of up to 25 words as a Back-Cover Text, to the end of
     the list of Cover Texts in the Modified Version.  Only one passage
     of Front-Cover Text and one of Back-Cover Text may be added by (or
     through arrangements made by) any one entity.  If the Document
     already includes a cover text for the same cover, previously added
     by you or by arrangement made by the same entity you are acting on
     behalf of, you may not add another; but you may replace the old
     one, on explicit permission from the previous publisher that added
     the old one.

     The author(s) and publisher(s) of the Document do not by this
     License give permission to use their names for publicity for or to
     assert or imply endorsement of any Modified Version.

  5. COMBINING DOCUMENTS

     You may combine the Document with other documents released under
     this License, under the terms defined in section 4 above for
     modified versions, provided that you include in the combination all
     of the Invariant Sections of all of the original documents,
     unmodified, and list them all as Invariant Sections of your
     combined work in its license notice, and that you preserve all
     their Warranty Disclaimers.

     The combined work need only contain one copy of this License, and
     multiple identical Invariant Sections may be replaced with a single
     copy.  If there are multiple Invariant Sections with the same name
     but different contents, make the title of each such section unique
     by adding at the end of it, in parentheses, the name of the
     original author or publisher of that section if known, or else a
     unique number.  Make the same adjustment to the section titles in
     the list of Invariant Sections in the license notice of the
     combined work.

     In the combination, you must combine any sections Entitled
     "History" in the various original documents, forming one section
     Entitled "History"; likewise combine any sections Entitled
     "Acknowledgements", and any sections Entitled "Dedications".  You
     must delete all sections Entitled "Endorsements."

  6. COLLECTIONS OF DOCUMENTS

     You may make a collection consisting of the Document and other
     documents released under this License, and replace the individual
     copies of this License in the various documents with a single copy
     that is included in the collection, provided that you follow the
     rules of this License for verbatim copying of each of the documents
     in all other respects.

     You may extract a single document from such a collection, and
     distribute it individually under this License, provided you insert
     a copy of this License into the extracted document, and follow this
     License in all other respects regarding verbatim copying of that
     document.

  7. AGGREGATION WITH INDEPENDENT WORKS

     A compilation of the Document or its derivatives with other
     separate and independent documents or works, in or on a volume of a
     storage or distribution medium, is called an "aggregate" if the
     copyright resulting from the compilation is not used to limit the
     legal rights of the compilation's users beyond what the individual
     works permit.  When the Document is included in an aggregate, this
     License does not apply to the other works in the aggregate which
     are not themselves derivative works of the Document.

     If the Cover Text requirement of section 3 is applicable to these
     copies of the Document, then if the Document is less than one half
     of the entire aggregate, the Document's Cover Texts may be placed
     on covers that bracket the Document within the aggregate, or the
     electronic equivalent of covers if the Document is in electronic
     form.  Otherwise they must appear on printed covers that bracket
     the whole aggregate.

  8. TRANSLATION

     Translation is considered a kind of modification, so you may
     distribute translations of the Document under the terms of section
     4.  Replacing Invariant Sections with translations requires special
     permission from their copyright holders, but you may include
     translations of some or all Invariant Sections in addition to the
     original versions of these Invariant Sections.  You may include a
     translation of this License, and all the license notices in the
     Document, and any Warranty Disclaimers, provided that you also
     include the original English version of this License and the
     original versions of those notices and disclaimers.  In case of a
     disagreement between the translation and the original version of
     this License or a notice or disclaimer, the original version will
     prevail.

     If a section in the Document is Entitled "Acknowledgements",
     "Dedications", or "History", the requirement (section 4) to
     Preserve its Title (section 1) will typically require changing the
     actual title.

  9. TERMINATION

     You may not copy, modify, sublicense, or distribute the Document
     except as expressly provided under this License.  Any attempt
     otherwise to copy, modify, sublicense, or distribute it is void,
     and will automatically terminate your rights under this License.

     However, if you cease all violation of this License, then your
     license from a particular copyright holder is reinstated (a)
     provisionally, unless and until the copyright holder explicitly and
     finally terminates your license, and (b) permanently, if the
     copyright holder fails to notify you of the violation by some
     reasonable means prior to 60 days after the cessation.

     Moreover, your license from a particular copyright holder is
     reinstated permanently if the copyright holder notifies you of the
     violation by some reasonable means, this is the first time you have
     received notice of violation of this License (for any work) from
     that copyright holder, and you cure the violation prior to 30 days
     after your receipt of the notice.

     Termination of your rights under this section does not terminate
     the licenses of parties who have received copies or rights from you
     under this License.  If your rights have been terminated and not
     permanently reinstated, receipt of a copy of some or all of the
     same material does not give you any rights to use it.

  10. FUTURE REVISIONS OF THIS LICENSE

     The Free Software Foundation may publish new, revised versions of
     the GNU Free Documentation License from time to time.  Such new
     versions will be similar in spirit to the present version, but may
     differ in detail to address new problems or concerns.  See
     <http://www.gnu.org/copyleft/>.

     Each version of the License is given a distinguishing version
     number.  If the Document specifies that a particular numbered
     version of this License "or any later version" applies to it, you
     have the option of following the terms and conditions either of
     that specified version or of any later version that has been
     published (not as a draft) by the Free Software Foundation.  If the
     Document does not specify a version number of this License, you may
     choose any version ever published (not as a draft) by the Free
     Software Foundation.  If the Document specifies that a proxy can
     decide which future versions of this License can be used, that
     proxy's public statement of acceptance of a version permanently
     authorizes you to choose that version for the Document.

  11. RELICENSING

     "Massive Multiauthor Collaboration Site" (or "MMC Site") means any
     World Wide Web server that publishes copyrightable works and also
     provides prominent facilities for anybody to edit those works.  A
     public wiki that anybody can edit is an example of such a server.
     A "Massive Multiauthor Collaboration" (or "MMC") contained in the
     site means any set of copyrightable works thus published on the MMC
     site.

     "CC-BY-SA" means the Creative Commons Attribution-Share Alike 3.0
     license published by Creative Commons Corporation, a not-for-profit
     corporation with a principal place of business in San Francisco,
     California, as well as future copyleft versions of that license
     published by that same organization.

     "Incorporate" means to publish or republish a Document, in whole or
     in part, as part of another Document.

     An MMC is "eligible for relicensing" if it is licensed under this
     License, and if all works that were first published under this
     License somewhere other than this MMC, and subsequently
     incorporated in whole or in part into the MMC, (1) had no cover
     texts or invariant sections, and (2) were thus incorporated prior
     to November 1, 2008.

     The operator of an MMC Site may republish an MMC contained in the
     site under CC-BY-SA on the same site at any time before August 1,
     2009, provided the MMC is eligible for relicensing.

ADDENDUM: How to use this License for your documents
====================================================

To use this License in a document you have written, include a copy of
the License in the document and put the following copyright and license
notices just after the title page:

       Copyright (C)  YEAR  YOUR NAME.
       Permission is granted to copy, distribute and/or modify this document
       under the terms of the GNU Free Documentation License, Version 1.3
       or any later version published by the Free Software Foundation;
       with no Invariant Sections, no Front-Cover Texts, and no Back-Cover
       Texts.  A copy of the license is included in the section entitled ``GNU
       Free Documentation License''.

   If you have Invariant Sections, Front-Cover Texts and Back-Cover
Texts, replace the "with...Texts."  line with this:

         with the Invariant Sections being LIST THEIR TITLES, with
         the Front-Cover Texts being LIST, and with the Back-Cover Texts
         being LIST.

   If you have Invariant Sections without Cover Texts, or some other
combination of the three, merge those two alternatives to suit the
situation.

   If your document contains nontrivial examples of program code, we
recommend releasing these examples in parallel under your choice of free
software license, such as the GNU General Public License, to permit
their use in free software.


File: libquadmath.info,  Node: Reporting Bugs,  Prev: GNU Free Documentation License,  Up: Top

4 Reporting Bugs
****************

Bugs in the GCC Quad-Precision Math Library implementation should be
reported via <https://gcc.gnu.org/bugs/>.



Tag Table:
Node: Top1633
Node: Typedef and constants2367
Node: Math Library Routines3786
Node: I/O Library Routines7623
Node: strtoflt1287948
Node: quadmath_snprintf8708
Node: GNU Free Documentation License10918
Node: Reporting Bugs36065

End Tag Table


Local Variables:
coding: utf-8
End:
