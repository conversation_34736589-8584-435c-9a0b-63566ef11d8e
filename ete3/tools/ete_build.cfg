[genetree_meta_workflow]
phylomedb4=metaligner_phylomedb-trimal01-prottest_default-phyml_default,
eggnog41=metaligner_trimmed-trimal01-prottest_default-phyml_default,
ensembl_small = mcoffee_ensembl-none-none-treebest_ensembl,
ensembl_large = mafft_linsi-none-none-treebest_ensembl,

#ensembl=mcoffee_soft-trimal01-prottest_default-phyml_default,
# linsi_fasttree=mafft_linsi-none-none-fasttree_full,
# linsi_phyml=mafft_linsi-trimal01-none-phyml_default,
# linsi_phyml_bootstrap=mafft_linsi-trimal01-none-phyml_default_bootstrap,
# linsi_raxml=mafft_linsi-trimal01-none-raxml_default,
# linsi_raxml_bootstrap=mafft_linsi-trimal01-none-raxml_default_bootstrap,

full_modeltest = clustalo_default-none-pmodeltest_full_slow-phyml_default,
full_modeltest_bootstrap = clustalo_default-none-pmodeltest_full_slow-phyml_default_bootstrap,
full_fast_modeltest = clustalo_default-none-pmodeltest_full_fast-phyml_default,
full_fast_modeltest_bootstrap = clustalo_default-none-pmodeltest_full_fast-phyml_default_bootstrap,
full_ultrafast_modeltest = clustalo_default-none-pmodeltest_full_ultrafast-phyml_default,
full_ultrafast_modeltest_bootstrap = clustalo_default-none-pmodeltest_full_ultrafast-phyml_default_bootstrap,

soft_modeltest = clustalo_default-none-pmodeltest_soft_slow-phyml_default,
soft_modeltest_bootstrap = clustalo_default-none-pmodeltest_soft_slow-phyml_default_bootstrap,
soft_fast_modeltest = clustalo_default-none-pmodeltest_soft_fast-phyml_default,
soft_fast_modeltest_bootstrap = clustalo_default-none-pmodeltest_soft_fast-phyml_default_bootstrap,
soft_ultrafast_modeltest = clustalo_default-none-pmodeltest_soft_ultrafast-phyml_default,
soft_ultrafast_modeltest_bootstrap = clustalo_default-none-pmodeltest_soft_ultrafast-phyml_default_bootstrap,

standard_raxml=clustalo_default-none-none-raxml_default,
standard_raxml_bootstrap=clustalo_default-none-none-raxml_default_bootstrap,
standard_phyml=clustalo_default-none-none-phyml_default,
standard_phyml_bootstrap=clustalo_default-none-none-phyml_default_bootstrap,
standard_fasttree=clustalo_default-none-none-fasttree_full,

standard_trimmed_raxml=clustalo_default-trimal01-none-raxml_default,
standard_trimmed_raxml_bootstrap=clustalo_default-trimal01-none-raxml_default_bootstrap,
standard_trimmed_phyml=clustalo_default-trimal01-none-phyml_default,
standard_trimmed_phyml_bootstrap=clustalo_default-trimal01-none-phyml_default_bootstrap,
standard_trimmed_fasttree=clustalo_default-trimal01-none-fasttree_full,

c_iqtree_bestmodel = clustalo_default-none-none-iqtree_bestmodel,
c_iqtree_codon_bestmodel = clustalo_default-none-none-iqtree_codon_bestmodel,
c_iqtree_codon = clustalo_default-none-none-iqtree_codon_default,

[workflow_desc]
phylomedb4=     "Worflow described in Huerta-Cepas, et al (Nucleic Acid Research, 2013) and used in http://PhylomeDB.org"

eggnog41 =      "Workflow used in the EggNOG orthology database and based on the phylomedb4 workflow"
ensembl_small = "Experimental (do not use yet)"
ensembl_large = "Experimental (do not use yet)"

full_modeltest =                     "Complete (and slow) pipeline testing all evolutionary models and inferring tree with Phyml"
full_modeltest_bootstrap =           "Complete (and slow) pipeline testing all evolutionary models and inferring tree with Phyml and 100 bootstraps"
full_fast_modeltest =                "Test all evolutionary models using NJ inference and computes final tree with Phyml"
full_fast_modeltest_bootstrap =      "Test all evolutionary models using NJ inference and computes final tree with Phyml and 100 bootstraps"
full_ultrafast_modeltest =           "Test all evolutionary models using NJ inference with no F, I and G estimation and computes final tree with Phyml"
full_ultrafast_modeltest_bootstrap = "Test all evolutionary models using NJ inference with no F, I and G estimation and computes final tree with Phyml and 100 bootstraps"

soft_modeltest =                      "Complete (and slow) pipeline testing 5 protein evolutionary models and inferring tree with Phyml"
soft_modeltest_bootstrap =            "Complete (and slow) pipeline testing 5 protein evolutionary models and inferring tree with Phyml and 100 bootstraps"
soft_fast_modeltest =                 "Test 5 protein evolutionary models using NJ inference and computes final tree with Phyml"
soft_fast_modeltest_bootstrap =       "Test 5 protein evolutionary models using NJ inference and computes final tree with Phyml and 100 bootstraps"
soft_ultrafast_modeltest =            "Test 5 protein evolutionary models using NJ inference with no F, I and G estimation and computes final tree with Phyml"
soft_ultrafast_modeltest_bootstrap =  "Test 5 protein evolutionary models using NJ inference with no F, I and G estimation and computes final tree with Phyml and 100 bootstraps"

standard_raxml=            "Computes a tree using (default) ClustalOmega and RAxML execution (SH-like aLRT supports)"
standard_raxml_bootstrap=  "Computes a tree using (default) ClustalOmega and RAxML with 100 bootstraps"
standard_phyml=            "Computes a tree using (default) ClustalOmega and Phyml (SH-like aLRT supports)"
standard_phyml_bootstrap=  "Computes a tree using (default) ClustalOmega and Phyml and 100 bootstraps"
standard_fasttree=         "Computes a tree using (default) ClustalOmega and FastTree (SH-like aLRT supports)"

standard_trimmed_raxml=             "Computes a tree using (default) ClustalOmega as aligner, trimAl for alg. cleaning and RAxML (SH-like aLRT supports)"
standard_trimmed_raxml_bootstrap=   "Computes a tree using (deafult) ClustalOmega trimAl (-gt01) and RAxML with 100 bootstraps"
standard_trimmed_phyml=             "Computes a tree using (deafult) ClustalOmega trimAl (-gt01) and Phyml (SH-like aLRT supports)"
standard_trimmed_phyml_bootstrap=   "Computes a tree using (deafult) ClustalOmega trimAl (-gt01) and Phyml and 100 bootstraps"
standard_trimmed_fasttree=          "Computes a tree using (deafult) ClustalOmega trimAl (-gt01) and FastTree (SH-like aLRT supports)"

sptree_fasttree_100=   "Selects COGs present in all target species, make a concatenated alignment and build a tree with FastTree"
sptree_fasttree_95=    "Selects COGs present in at least 95% of the target species, make a concatenated alignment and build a tree with FastTree"
sptree_fasttree_90=    "Selects COGs present in at least 90% of the target species, make a concatenated alignment and build a tree with FastTree"
sptree_fasttree_85=    "Selects COGs present in at least 85% of the target species, make a concatenated alignment and build a tree with FastTree"
sptree_fasttree_all=   "Selects all COGS regardless of how many species are represented, make a concatenated alignment and build a tree with FastTree"

sptree_raxml_100=      "Selects COGs present in all target species, make a concatenated alignment and build a tree with RAxML"
sptree_raxml_95=       "Selects COGs present in at least 95% of the target species, make a concatenated alignment and build a tree with RAxML"
sptree_raxml_90=       "Selects COGs present in at least 90% of the target species, make a concatenated alignment and build a tree with RAxML"
sptree_raxml_85=       "Selects COGs present in at least 85% of the target species, make a concatenated alignment and build a tree with RAxML"
sptree_raxml_all=      "Selects all COGS regardless of how many species are represented, make a concatenated alignment and build a tree with RAxML"





[supermatrix_meta_workflow]
sptree_fasttree_100=cog_100-alg_concat_default-fasttree_full,
sptree_fasttree_95=cog_95-alg_concat_default-fasttree_full,
sptree_fasttree_90=cog_90-alg_concat_default-fasttree_full,
sptree_fasttree_85=cog_85-alg_concat_default-fasttree_full,
sptree_fasttree_all=cog_all-alg_concat_default-fasttree_full,

sptree_raxml_100=cog_100-alg_concat_default-raxml_default,
sptree_raxml_95=cog_95-alg_concat_default-raxml_default,
sptree_raxml_90=cog_90-alg_concat_default-raxml_default,
sptree_raxml_85=cog_85-alg_concat_default-raxml_default,
sptree_raxml_all=cog_all-alg_concat_default-raxml_default,

sptree_iqtreeC10_all=cog_all-alg_concat_default-iqtree_C10,
sptree_iqtree_all=cog_all-alg_concat_default-iqtree_default,


[builtin_apps]
## Portable applications are used by default
#app_name             path    , max_cpu
muscle              = built-in, 1
mafft               = built-in, 2
clustalo            = built-in, 1
trimal              = built-in, 1
readal              = built-in, 1
tcoffee             = built-in, 1
phyml               = built-in, 1
raxml-pthreads      = built-in, 48
raxml               = built-in, 1
dialigntx           = built-in, 1
fasttree            = built-in, 2
statal              = built-in, 1
pmodeltest          = built-in, 48
prank               = built-in, 1
probcons            = built-in, 1
kalign              = built-in, 1
iqtree              = built-in, 48

# #################
# APPS
# ################
[metaligner_phylomedb]
_desc = 'Meta-aligning based on head/tail alignments produced by muscle, mafft and dialign-tx, scanned with M-Coffee. Unconsistent columns are removed and final alignment is cleaned with trimAl'
_app = metaligner
_alg_trimming = True
_aligners = @muscle_default, @mafft_default, @clustalo_default, @dialigntx_default

[metaligner_trimmed]
_desc = 'Meta-aligning based on head/tail alignments produced by muscle, mafft and clustalomega, scanned with M-Coffee. Unconsistent columns are removed and final alignment is cleaned with trimAl'
_app = metaligner
_alg_trimming = True
_aligners = @muscle_default, @mafft_default, @clustalo_default

[metaligner_default]
_desc = 'Meta-aligning based on head/tail alignments produced by muscle, mafft and clustalomega, scanned with M-Coffee. Unconsistent columns are removed'
_app = metaligner
_alg_trimming = False
_aligners = @muscle_default, @mafft_default, @clustalo_default

[tcoffee_default]
_desc = '(EXPERIMENTAL) tcoffee alignment with default paramerters'
_app = tcoffee
-output = "fasta_aln"

[mcoffee_ensembl]
_desc = '(EXPERIMENTAL) mcoffee alignment as used in the Ensembl database'
_app = tcoffee
-method = "mafftgins_msa,muscle_msa,kalign_msa,t_coffee_msa" 
-output = "fasta_aln"
#-mode = mcoffee # choose mode or customize your methods combo

[trimal001]
_desc = 'trimal alignment cleaning removing columns with >1% gaps'
_app = trimal
-gt = 0.01

[trimal01]
_desc = 'trimal alignment cleaning removing columns with >10% gaps'
_app = trimal
-gt = 0.1

[trimal02]
_desc = 'trimal alignment cleaning removing columns with >20% gaps'
_app = trimal
-gt = 0.2

[trimal03]
_desc = 'trimal alignment cleaning removing columns with >30% gaps'
_app = trimal
-gt = 0.3

[trimal05]
_desc = 'trimal alignment cleaning removing columns with >50% gaps'
_app = trimal
-gt = 0.5

[trimal_gappyout]
_desc = 'trimal alignment cleaning using gappyout algorithm'
_app = trimal
-gappyout= ''

[muscle_default]
_desc = 'muscle alignment with default parameters'
_app = muscle

[mafft_default]
_desc = 'mafft alignment with default parameters'
_app = mafft
--auto = ""

[mafft_einsi]
_desc = 'mafft alignment using the E-INS-i mode'
# E-INS-i
#is suitable for alignments like this:
#
#oooooooooXXX------XXXX---------------------------------XXXXXXXXXXX-XXXXXXXXXXXXXXXooooooooooooo
#---------XXXXXXXXXXXXXooo------------------------------XXXXXXXXXXXXXXXXXX-XXXXXXXX-------------
#-----ooooXXXXXX---XXXXooooooooooo----------------------XXXXX----XXXXXXXXXXXXXXXXXXooooooooooooo
#---------XXXXX----XXXXoooooooooooooooooooooooooooooooooXXXXX-XXXXXXXXXXXX--XXXXXXX-------------
#---------XXXXX----XXXX---------------------------------XXXXX---XXXXXXXXXX--XXXXXXXooooo--------
# where 'X's indicate alignable residues, 'o's indicate unalignable residues and
# '-'s indicate gaps. Unalignable residues are left unaligned at the pairwise
# alignment stage, because of the use of the generalized affine gap
# cost. Therefore E-INS-i is applicable to a difficult problem such as RNA
# polymerase, which has several conserved motifs embedded in long unalignable
# regions. As E-INS-i has the minimum assumption of the three methods, this is
# recommended if the nature of sequences to be aligned is not clear. Note that
# E-INS-i assumes that the arrangement of the conserved motifs is shared by all
# sequences.
_app = mafft
--genafpair = ''
--maxiterate=1000

[mafft_linsi]
_desc = 'mafft alignment using the L-INS-i mode'
# L-INS-i
#is suitable to:
#ooooooooooooooooooooooooooooooooXXXXXXXXXXX-XXXXXXXXXXXXXXX------------------
#--------------------------------XX-XXXXXXXXXXXXXXX-XXXXXXXXooooooooooo-------
#------------------ooooooooooooooXXXXX----XXXXXXXX---XXXXXXXooooooooooo-------
#--------ooooooooooooooooooooooooXXXXX-XXXXXXXXXX----XXXXXXXoooooooooooooooooo
#--------------------------------XXXXXXXXXXXXXXXX----XXXXXXX------------------
# L-INS-i can align a set of sequences containing sequences flanking around one
# alignable domain. Flanking sequences are ignored in the pairwise alignment by
# the Smith-Waterman algorithm. Note that the input sequences are assumed to
# have only one alignable domain. In benchmark tests, the ref4 of BAliBASE
# corresponds to this. The other categories of BAliBASE also correspond to
# similar situations, because they have flanking sequences. L-INS-i also shows
# higher accuracy values for a part of SABmark and HOMSTRAD than G-INS-i, but we
# have not identified the reason for this.
_app = mafft
--localpair = ''
--maxiterate = 1000

[mafft_ginsi]
_desc = 'mafft alignment using the G-INS-i mode'
#G-INS-i
#is suitable to:
# XXXXXXXXXXX-XXXXXXXXXXXXXXX
# XX-XXXXXXXXXXXXXXX-XXXXXXXX
# XXXXX----XXXXXXXX---XXXXXXX
# XXXXX-XXXXXXXXXX----XXXXXXX
# XXXXXXXXXXXXXXXX----XXXXXXX
# G-INS-i assumes that entire region can be aligned and tries to align them
#globally using the Needleman-Wunsch algorithm; that is, a set of sequences of
#one domain must be extracted by truncating flanking sequences. In benchmark
#tests, SABmark and HOMSTRAD correspond to this.
_app = mafft
--globalpair = ''
--maxiterate = 1000

[clustalo_default]
_desc = 'clustalo with default parameters'
_app = clustalo

[dialigntx_default]
_desc = 'dialign-tx with default parameters'
_app = dialigntx

[prottest_default]
_desc = 'Uses BioNJ trees to select best protein model from: JTT, WAG, VT, LG and MtREV. Fixed parameters: +G+I+F'
_app = prottest
_lk_mode = "phyml" # let "phyml" or "raxml" to optimize likelihood
_models = "JTT", "WAG", "VT", "LG", "MtREV"
-f = m            # char freq (m)odel or (e)stimated
--pinv = e        # Proportion of invariant sites
--alpha = e       #
--nclasses =  4   # Number of classes
--no_memory_check = ""
--quiet = ""


[pmodeltest_full_ultrafast]
_app = pmodeltest
_desc = 'Test all models using NJ tree inference and skipping gamma, invariant sites, and frequency site estimations (!G!I!F)'
_aa_models = "all"
_nt_models = "all"
--fast = ""
--noinv = ""
--nogam = ""
--nofrq = ""

[pmodeltest_full_fast]
_app = pmodeltest
_desc = 'Test all models using NJ tree inference'
_aa_models = "all"
_nt_models = "all"
--fast = ""

[pmodeltest_full_slow]
_app = pmodeltest
_desc = 'Test all models using ML tree inference'
_aa_models = "all"
_nt_models = "all"


[pmodeltest_soft_ultrafast]
_desc = 'Test JTT,WAG,VT,LG,MtREV models using NJ tree inference and skipping gamma, invariant sites, and frequency site estimation (!G!I!F)'
_app = pmodeltest
_aa_models = "JTT,WAG,VT,LG,MtREV"
_nt_models = "GTR"
--fast = ""
--noinv = ""
--nogam = ""
#--nofrq = ""

[pmodeltest_soft_fast]
_desc = 'Test JTT,WAG,VT,LG,MtREV models using NJ tree inference'
_app = pmodeltest
_aa_models = "JTT,WAG,VT,LG,MtREV"
_nt_models = "GTR"
--fast = ""

[pmodeltest_soft_slow]
_desc = 'Test JTT,WAG,VT,LG,MtREV models using ML tree inference'
_app = pmodeltest
_aa_models = "JTT,WAG,VT,LG,MtREV"
_nt_models = "GTR"


[bionj_default]
_desc = 'BioNJ tree inferred with Phyml. default models JTT/GTR'
_app = phyml
_aa_model = JTT # AA model used if no model selection is performed
_nt_model = GTR # Nt model used if no model selection is performed
--pinv = e        # Proportion of invariant sites.  Fixed value in the
                  # [0,1] range or "e" for estimated
--alpha = e       # Gamma distribution shape parameter. fixed value or
                  # "e" for "estimated"
--nclasses =  4   # Number of rate categories
-o = lr          # Tree optimization
-f = m            # e: estiamte character frequencies.  m: character
                  # frequencies from model
--bootstrap = -2  #  approximate likelihood ratio test returning
                  #  Chi2-based parametric branch supports.

[fasttree_default]
_desc = 'Fasttree with default parameters'
_app = fasttree

[fasttree_full]
_inherits = fasttree_default
_desc = 'Fasttree with slow NNI and MLACC=3'
-pseudo = ''
-mlacc = 3
-slownni = ''

[phyml_default]
_desc = 'Phyml tree using +G+I+F, 4 classes and aLRT branch supports. Default models JTT/GTR'
_app = phyml
_aa_model = JTT # AA model used if no model selection is performed
_nt_model = GTR # Nt model used if no model selection is performed
--pinv = e        # Proportion of invariant sites.  Fixed value in the
                  # [0,1] range or "e" for estimated
--alpha = e       # Gamma distribution shape parameter. fixed value or
                  # "e" for "estimated"
--nclasses =  4   # Number of rate categories
-o = tlr          # Tree optimization
-f = m            # e: estiamte character frequencies.  m: character
                  # frequencies from model
--bootstrap = -2  #  approximate likelihood ratio test returning
                  #  Chi2-based parametric branch supports.

[phyml_default_bootstrap]
_inherits = phyml_default
_desc = 'Phyml tree using +G+I+F, 4 classes and 100 bootstraps. Default models JTT/GTR'
--bootstrap = 100 
                  

[raxml_default]
_desc = 'RAxML with default parameters, GAMMA JTT/GTR and aLRT branch supports.'
_app = raxml
_bootstrap = alrt # alrt, alrt_phyml or and integer number
_method = GAMMA # GAMMA or CAT
_aa_model = JTT # Model used if no model selection is performed
# _model_suffix = "I" or "IF"
_model_suffix = ""
-f = d
-p = 31416 # Random seed to generate starting tree. Make results reproducible


[raxml_default_bootstrap]
_inherits = raxml_default
_desc = 'RAxML with default parameters, GAMMA JTT/GTR and 100 boostrap replicates'
_bootstrap = 100

[iqtree_default]
_desc = 'IQTree with default parameters and alrt branch supports'
_app = iqtree
-alrt = 1000
-seed = 31416

[iqtree_bestmodel]
_inherits = iqtree_default
_desc = 'IQTree '
-m = TESTNEWONLY

[iqtree_codon_default] 
_inherits = iqtree_default
_desc = 'IQTree'
-st = CODON

[iqtree_codon_bestmodel]
_inherits = iqtree_default
_desc = 'IQTree'
-st = CODON
-m = TEST

[iqtree_C10]
_inherits = iqtree_default
_desc = 'IQTree C10'
-m = C10

[iqtree_C30]
_inherits = iqtree_default
_desc = 'IQTree C30'
-m = C30

[iqtree_C60]
_inherits = iqtree_default
_desc = 'IQTree C60'
-m = C60


[treebest_ensembl]
_desc = '(EXPERIMENTAL, do not use)'
_app = phyml
_aa_model = JTT # AA model used if no model selection is performed
_nt_model = GTR # Nt model used if no model selection is performed
--no_memory_check = ""
--quiet = ""
--pinv = e        # Proportion of invariant sites.  Fixed value in the
                  # [0,1] range or "e" for estimated
--alpha = e       # Gamma distribution shape parameter. fixed value or
                  # "e" for "estimated"
--nclasses =  4   # Number of rate categories
-o = tlr          # Tree optimization
-f = m            # e: estiamte character frequencies.  m: character
                  # frequencies from model
--bootstrap = -2  #  approximate likelihood ratio test returning
                  #  Chi2-based parametric branch supports.



# #################
# SUPERMATRIX APPS
# ################

#dummy
[sptree_dummy]
_app = supermatrix
_desc = 'Only 4 genes are selected. Just for testing purposes'
_cog_selector         = @cog_dummy
_alg_concatenator     = @alg_concat_default
_aa_tree_builder      = @fasttree_default
_nt_tree_builder      = @fasttree_default
_appset = @builtin_apps

[cog_dummy]
_desc = 'Only 4 genes are selected. Just for testing purposes'
_app = cogselector
_species_missing_factor = 0.50
_max_species_missing_factor = 0.60
_max_cogs = 4

[alg_concat_default]
_app = concatalg
_default_aa_model = JTT
_default_nt_model = GTR

[cog_all]
_app = cogselector
_desc = 'all clusters of orthologous groups (COGs) will be used'
_species_missing_factor = 1.0
_max_species_missing_factor = 1.0
_max_cogs = 999999

[cog_100]
_desc = 'only COGs including 100% of the target species will be used'
_app = cogselector
_species_missing_factor = 0
_max_species_missing_factor = 0
_max_cogs = 999999

[cog_95]
_desc = 'all COGs including at least 95% of the target species will be used'
_app = cogselector
_species_missing_factor = 0.05
_max_species_missing_factor = 0.07
_max_cogs = 999999


[cog_90]
_desc = 'all COGs including at least 90% of the target species will be used'
_app = cogselector
_species_missing_factor = 0.10
_max_species_missing_factor = 0.15
_max_cogs = 999999

[cog_85]
_desc = 'all COGs including at least 85% of the target species will be used'
_app = cogselector
_species_missing_factor = 0.15
_max_species_missing_factor = 0.20
_max_cogs = 999999

# #################
# NPR configs
# ################

[splitter_default]
_desc = 'Select anchoring sequences from sister clade until reaching 10% of the actual partition size.'
_app = treesplitter
_max_outgroup_size = 10%
_min_outgroup_support = 0.90
_outgroup_topology_dist = False
_first_split = midpoint


