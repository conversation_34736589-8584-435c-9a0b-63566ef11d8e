# pkg-config file generated by gen-pkgconfig
# vile:makemode

prefix=/home/<USER>/anaconda3/envs/django
exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${prefix}/include/ncursesw
abi_version=6
major_version=6
version=6.4.20221231

Name: ncursesw
Description: ncurses 6.4 library
Version: ${version}
URL: https://invisible-island.net/ncurses
Requires.private: 
Libs:  -L/home/<USER>/anaconda3/envs/django/lib -Wl,-O2 -Wl,--sort-common -Wl,--disable-new-dtags -Wl,--gc-sections -Wl,-rpath,/home/<USER>/anaconda3/envs/django/lib -Wl,-rpath-link,/home/<USER>/anaconda3/envs/django/lib -lncursesw -ltinfow
Libs.private:  
Cflags:  -D_GNU_SOURCE -DNCURSES_WIDECHAR -I${includedir} -I/home/<USER>/anaconda3/envs/django/include
