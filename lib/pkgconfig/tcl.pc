# tcl pkg-config source file

prefix=/home/<USER>/anaconda3/envs/django
exec_prefix=/home/<USER>/anaconda3/envs/django
libdir=/home/<USER>/anaconda3/envs/django/lib
includedir=${prefix}/include

Name: Tool Command Language
Description: Tcl is a powerful, easy-to-learn dynamic programming language, suitable for a wide range of uses.
URL: https://www.tcl-tk.org/
Version: 8.6.12
Requires.private: zlib >= 1.2.3
Libs: -L${libdir} -ltcl8.6 -ltclstub8.6
Libs.private: -ldl -lz  -lpthread -lm
Cflags: -I${includedir}
