# system configuration generated and used by the sysconfig module
build_time_vars = {'ABIFLAGS': '',
 'AC_APPLE_UNIVERSAL_BUILD': 0,
 'AIX_GENUINE_CPLUSPLUS': 0,
 'ANDROID_API_LEVEL': 0,
 'AR': 'x86_64-apple-darwin13.4.0-ar',
 'ARFLAGS': 'rcs',
 'BASECFLAGS': '-fno-strict-aliasing -Wsign-compare -Wunreachable-code',
 'BASECPPFLAGS': '-IObjects -IInclude -IPython',
 'BASEMODLIBS': '',
 'BINDIR': '/home/<USER>/anaconda3/envs/django/bin',
 'BINLIBDEST': '/home/<USER>/anaconda3/envs/django/lib/python3.8',
 'BLDLIBRARY': 'libpython3.8.a',
 'BLDSHARED': 'x86_64-apple-darwin13.4.0-clang -bundle -undefined '
              'dynamic_lookup -Wl,-pie -Wl,-headerpad_max_install_names '
              '-Wl,-dead_strip_dylibs '
              '-Wl,-rpath,/home/<USER>/anaconda3/envs/django/lib '
              '-L/home/<USER>/anaconda3/envs/django/lib '
              '-flto -Wl,-export_dynamic -Wl,-pie '
              '-Wl,-headerpad_max_install_names -Wl,-dead_strip_dylibs '
              '-Wl,-rpath,/home/<USER>/anaconda3/envs/django/lib '
              '-L/home/<USER>/anaconda3/envs/django/lib',
 'BUILDEXE': '.exe',
 'BUILDPYTHON': 'python.exe',
 'BUILD_GNU_TYPE': 'x86_64-apple-darwin13.4.0',
 'BYTESTR_DEPS': '\\',
 'CC': 'x86_64-apple-darwin13.4.0-clang',
 'CCSHARED': '',
 'CFLAGS': '-fno-strict-aliasing -Wsign-compare -Wunreachable-code -DNDEBUG '
           '-fwrapv -O3 -Wall -Wstrict-prototypes -march=core2 -mtune=haswell '
           '-mssse3 -ftree-vectorize -fPIC -fPIE -fstack-protector-strong -O3 '
           '-pipe  '
           '-fdebug-prefix-map=${SRC_DIR}=/usr/local/src/conda/${PKG_NAME}-${PKG_VERSION} '
           '-fdebug-prefix-map=/home/<USER>/anaconda3/envs/django=/usr/local/src/conda-prefix '
           '-flto -Wl,-export_dynamic -march=core2 -mtune=haswell '
           '-mssse3 -ftree-vectorize -fPIC -fPIE -fstack-protector-strong -O3 ',
 'CFLAGSFORSHARED': '',
 'CFLAGS_ALIASING': '-fno-strict-aliasing',
 'CONFIGFILES': 'configure configure.ac acconfig.h pyconfig.h.in '
                'Makefile.pre.in',
 'CONFIGURE_CFLAGS': '-march=core2 -mtune=haswell -mssse3 -ftree-vectorize '
                     '-fPIC -fPIE -fstack-protector-strong -O3 -pipe  '
                     '-fdebug-prefix-map=${SRC_DIR}=/usr/local/src/conda/${PKG_NAME}-${PKG_VERSION} '
                     '-fdebug-prefix-map=/home/<USER>/anaconda3/envs/django=/usr/local/src/conda-prefix '
                     '-flto -Wl,-export_dynamic',
 'CONFIGURE_CFLAGS_NODIST': '-std=c99 -Werror=implicit-function-declaration',
 'CONFIGURE_CPPFLAGS': '-D_FORTIFY_SOURCE=2 -mmacosx-version-min=10.9 '
                       '-I/home/<USER>/anaconda3/envs/django/include',
 'CONFIGURE_LDFLAGS': '-Wl,-pie -Wl,-headerpad_max_install_names '
                      '-Wl,-dead_strip_dylibs '
                      '-Wl,-rpath,/home/<USER>/anaconda3/envs/django/lib '
                      '-L/home/<USER>/anaconda3/envs/django/lib '
                      '-flto -Wl,-export_dynamic',
 'CONFIG_ARGS': "'--prefix=/home/<USER>/anaconda3/envs/django' "
                "'--build=x86_64-apple-darwin13.4.0' "
                "'--host=x86_64-apple-darwin13.4.0' '--enable-ipv6' "
                "'--with-ensurepip=no' '--with-computed-gotos' "
                "'--with-system-ffi' '--enable-loadable-sqlite-extensions' "
                "'--with-tcltk-includes=-I/home/<USER>/anaconda3/envs/django/include' "
                "'--with-tcltk-libs=-L/home/<USER>/anaconda3/envs/django/lib "
                "-ltcl8.6 -ltk8.6' '--enable-optimizations' '--with-lto' "
                "'--disable-shared' 'build_alias=x86_64-apple-darwin13.4.0' "
                "'host_alias=x86_64-apple-darwin13.4.0' "
                "'CC=x86_64-apple-darwin13.4.0-clang' 'CFLAGS=-march=core2 "
                '-mtune=haswell -mssse3 -ftree-vectorize -fPIC -fPIE '
                '-fstack-protector-strong -O3 -pipe  '
                '-fdebug-prefix-map=${SRC_DIR}=/usr/local/src/conda/${PKG_NAME}-${PKG_VERSION} '
                '-fdebug-prefix-map=/home/<USER>/anaconda3/envs/django=/usr/local/src/conda-prefix '
                "-flto' 'LDFLAGS=-Wl,-pie -Wl,-headerpad_max_install_names "
                '-Wl,-dead_strip_dylibs '
                '-Wl,-rpath,/home/<USER>/anaconda3/envs/django/lib '
                "-L/home/<USER>/anaconda3/envs/django/lib' "
                "'CPPFLAGS=-D_FORTIFY_SOURCE=2 -mmacosx-version-min=10.9 "
                "-I/home/<USER>/anaconda3/envs/django/include' "
                "'PKG_CONFIG_PATH=/home/<USER>/anaconda3/envs/django/lib/pkgconfig'",
 'CONFINCLUDEDIR': '/home/<USER>/anaconda3/envs/django/include',
 'CONFINCLUDEPY': '/home/<USER>/anaconda3/envs/django/include/python3.8',
 'COREPYTHONPATH': '',
 'COVERAGE_INFO': '${SRC_DIR}/build-static/coverage.info',
 'COVERAGE_REPORT': '${SRC_DIR}/build-static/lcov-report',
 'COVERAGE_REPORT_OPTIONS': '--no-branch-coverage --title "CPython lcov '
                            'report"',
 'CPPFLAGS': '-IObjects -IInclude -IPython -I. '
             '-I${SRC_DIR}/Include '
             '-D_FORTIFY_SOURCE=2 -mmacosx-version-min=10.9 '
             '-I/home/<USER>/anaconda3/envs/django/include '
             '-D_FORTIFY_SOURCE=2 -mmacosx-version-min=10.9 '
             '-I/home/<USER>/anaconda3/envs/django/include',
 'CXX': 'x86_64-apple-darwin13.4.0-clang++',
 'DESTDIRS': '/home/<USER>/anaconda3/envs/django '
             '/home/<USER>/anaconda3/envs/django/lib '
             '/home/<USER>/anaconda3/envs/django/lib/python3.8 '
             '/home/<USER>/anaconda3/envs/django/lib/python3.8/lib-dynload',
 'DESTLIB': '/home/<USER>/anaconda3/envs/django/lib/python3.8',
 'DESTPATH': '',
 'DESTSHARED': '/home/<USER>/anaconda3/envs/django/lib/python3.8/lib-dynload',
 'DFLAGS': '',
 'DIRMODE': 755,
 'DIST': 'README.rst ChangeLog configure configure.ac acconfig.h pyconfig.h.in '
         'Makefile.pre.in Include Lib Misc Ext-dummy',
 'DISTDIRS': 'Include Lib Misc Ext-dummy',
 'DISTFILES': 'README.rst ChangeLog configure configure.ac acconfig.h '
              'pyconfig.h.in Makefile.pre.in',
 'DLINCLDIR': '.',
 'DLLLIBRARY': '',
 'DOUBLE_IS_ARM_MIXED_ENDIAN_IEEE754': 0,
 'DOUBLE_IS_BIG_ENDIAN_IEEE754': 0,
 'DOUBLE_IS_LITTLE_ENDIAN_IEEE754': 1,
 'DTRACE': '',
 'DTRACE_DEPS': '\\',
 'DTRACE_HEADERS': '',
 'DTRACE_OBJS': '',
 'DYNLOADFILE': 'dynload_shlib.o',
 'ENABLE_IPV6': 1,
 'ENSUREPIP': 'no',
 'EXE': '',
 'EXEMODE': 755,
 'EXTRATESTOPTS': '',
 'EXT_SUFFIX': '.cpython-38-darwin.so',
 'FILEMODE': 644,
 'FLOCK_NEEDS_LIBBSD': 0,
 'GETPGRP_HAVE_ARG': 0,
 'GETTIMEOFDAY_NO_TZ': 0,
 'GITBRANCH': '',
 'GITTAG': '',
 'GITVERSION': '',
 'GNULD': 'no',
 'HAVE_ACCEPT4': 0,
 'HAVE_ACOSH': 1,
 'HAVE_ADDRINFO': 1,
 'HAVE_ALARM': 1,
 'HAVE_ALIGNED_REQUIRED': 0,
 'HAVE_ALLOCA_H': 1,
 'HAVE_ALTZONE': 0,
 'HAVE_ASINH': 1,
 'HAVE_ASM_TYPES_H': 0,
 'HAVE_ATANH': 1,
 'HAVE_BIND_TEXTDOMAIN_CODESET': 0,
 'HAVE_BLUETOOTH_BLUETOOTH_H': 0,
 'HAVE_BLUETOOTH_H': 0,
 'HAVE_BROKEN_MBSTOWCS': 0,
 'HAVE_BROKEN_NICE': 0,
 'HAVE_BROKEN_PIPE_BUF': 0,
 'HAVE_BROKEN_POLL': 0,
 'HAVE_BROKEN_POSIX_SEMAPHORES': 0,
 'HAVE_BROKEN_PTHREAD_SIGMASK': 0,
 'HAVE_BROKEN_SEM_GETVALUE': 1,
 'HAVE_BROKEN_UNSETENV': 0,
 'HAVE_BUILTIN_ATOMIC': 1,
 'HAVE_CHFLAGS': 1,
 'HAVE_CHOWN': 1,
 'HAVE_CHROOT': 1,
 'HAVE_CLOCK': 1,
 'HAVE_CLOCK_GETRES': 0,
 'HAVE_CLOCK_GETTIME': 0,
 'HAVE_CLOCK_SETTIME': 0,
 'HAVE_COMPUTED_GOTOS': 1,
 'HAVE_CONFSTR': 1,
 'HAVE_CONIO_H': 0,
 'HAVE_COPYSIGN': 1,
 'HAVE_CRYPT_H': 0,
 'HAVE_CTERMID': 1,
 'HAVE_CTERMID_R': 1,
 'HAVE_CURSES_FILTER': 1,
 'HAVE_CURSES_H': 1,
 'HAVE_CURSES_HAS_KEY': 1,
 'HAVE_CURSES_IMMEDOK': 1,
 'HAVE_CURSES_IS_PAD': 1,
 'HAVE_CURSES_IS_TERM_RESIZED': 1,
 'HAVE_CURSES_RESIZETERM': 1,
 'HAVE_CURSES_RESIZE_TERM': 1,
 'HAVE_CURSES_SYNCOK': 1,
 'HAVE_CURSES_TYPEAHEAD': 1,
 'HAVE_CURSES_USE_ENV': 1,
 'HAVE_CURSES_WCHGAT': 1,
 'HAVE_DECL_ISFINITE': 1,
 'HAVE_DECL_ISINF': 1,
 'HAVE_DECL_ISNAN': 1,
 'HAVE_DECL_RTLD_DEEPBIND': 0,
 'HAVE_DECL_RTLD_GLOBAL': 1,
 'HAVE_DECL_RTLD_LAZY': 1,
 'HAVE_DECL_RTLD_LOCAL': 1,
 'HAVE_DECL_RTLD_MEMBER': 0,
 'HAVE_DECL_RTLD_NODELETE': 1,
 'HAVE_DECL_RTLD_NOLOAD': 1,
 'HAVE_DECL_RTLD_NOW': 1,
 'HAVE_DECL_TZNAME': 0,
 'HAVE_DEVICE_MACROS': 1,
 'HAVE_DEV_PTC': 0,
 'HAVE_DEV_PTMX': 1,
 'HAVE_DIRECT_H': 0,
 'HAVE_DIRENT_D_TYPE': 1,
 'HAVE_DIRENT_H': 1,
 'HAVE_DIRFD': 1,
 'HAVE_DLFCN_H': 1,
 'HAVE_DLOPEN': 1,
 'HAVE_DUP2': 1,
 'HAVE_DUP3': 0,
 'HAVE_DYNAMIC_LOADING': 1,
 'HAVE_ENDIAN_H': 0,
 'HAVE_EPOLL': 0,
 'HAVE_EPOLL_CREATE1': 0,
 'HAVE_ERF': 1,
 'HAVE_ERFC': 1,
 'HAVE_ERRNO_H': 1,
 'HAVE_EXECV': 1,
 'HAVE_EXPM1': 1,
 'HAVE_FACCESSAT': 0,
 'HAVE_FCHDIR': 1,
 'HAVE_FCHMOD': 1,
 'HAVE_FCHMODAT': 0,
 'HAVE_FCHOWN': 1,
 'HAVE_FCHOWNAT': 0,
 'HAVE_FCNTL_H': 1,
 'HAVE_FDATASYNC': 0,
 'HAVE_FDOPENDIR': 0,
 'HAVE_FEXECVE': 0,
 'HAVE_FINITE': 1,
 'HAVE_FLOCK': 1,
 'HAVE_FORK': 1,
 'HAVE_FORKPTY': 1,
 'HAVE_FPATHCONF': 1,
 'HAVE_FSEEK64': 0,
 'HAVE_FSEEKO': 1,
 'HAVE_FSTATAT': 0,
 'HAVE_FSTATVFS': 1,
 'HAVE_FSYNC': 1,
 'HAVE_FTELL64': 0,
 'HAVE_FTELLO': 1,
 'HAVE_FTIME': 1,
 'HAVE_FTRUNCATE': 1,
 'HAVE_FUTIMENS': 0,
 'HAVE_FUTIMES': 1,
 'HAVE_FUTIMESAT': 0,
 'HAVE_GAI_STRERROR': 1,
 'HAVE_GAMMA': 1,
 'HAVE_GCC_ASM_FOR_MC68881': 0,
 'HAVE_GCC_ASM_FOR_X64': 1,
 'HAVE_GCC_ASM_FOR_X87': 1,
 'HAVE_GCC_UINT128_T': 1,
 'HAVE_GETADDRINFO': 1,
 'HAVE_GETC_UNLOCKED': 1,
 'HAVE_GETENTROPY': 0,
 'HAVE_GETGROUPLIST': 1,
 'HAVE_GETGROUPS': 1,
 'HAVE_GETHOSTBYNAME': 1,
 'HAVE_GETHOSTBYNAME_R': 0,
 'HAVE_GETHOSTBYNAME_R_3_ARG': 0,
 'HAVE_GETHOSTBYNAME_R_5_ARG': 0,
 'HAVE_GETHOSTBYNAME_R_6_ARG': 0,
 'HAVE_GETITIMER': 1,
 'HAVE_GETLOADAVG': 1,
 'HAVE_GETLOGIN': 1,
 'HAVE_GETNAMEINFO': 1,
 'HAVE_GETPAGESIZE': 1,
 'HAVE_GETPEERNAME': 1,
 'HAVE_GETPGID': 1,
 'HAVE_GETPGRP': 1,
 'HAVE_GETPID': 1,
 'HAVE_GETPRIORITY': 1,
 'HAVE_GETPWENT': 1,
 'HAVE_GETRANDOM': 0,
 'HAVE_GETRANDOM_SYSCALL': 0,
 'HAVE_GETRESGID': 0,
 'HAVE_GETRESUID': 0,
 'HAVE_GETSID': 1,
 'HAVE_GETSPENT': 0,
 'HAVE_GETSPNAM': 0,
 'HAVE_GETTIMEOFDAY': 1,
 'HAVE_GETWD': 1,
 'HAVE_GLIBC_MEMMOVE_BUG': 0,
 'HAVE_GRP_H': 1,
 'HAVE_HSTRERROR': 1,
 'HAVE_HTOLE64': 0,
 'HAVE_HYPOT': 1,
 'HAVE_IEEEFP_H': 0,
 'HAVE_IF_NAMEINDEX': 1,
 'HAVE_INET_ATON': 1,
 'HAVE_INET_PTON': 1,
 'HAVE_INITGROUPS': 1,
 'HAVE_INTTYPES_H': 1,
 'HAVE_IO_H': 0,
 'HAVE_IPA_PURE_CONST_BUG': 0,
 'HAVE_KILL': 1,
 'HAVE_KILLPG': 1,
 'HAVE_KQUEUE': 1,
 'HAVE_LANGINFO_H': 1,
 'HAVE_LARGEFILE_SUPPORT': 0,
 'HAVE_LCHFLAGS': 1,
 'HAVE_LCHMOD': 1,
 'HAVE_LCHOWN': 1,
 'HAVE_LGAMMA': 1,
 'HAVE_LIBDL': 1,
 'HAVE_LIBDLD': 0,
 'HAVE_LIBIEEE': 0,
 'HAVE_LIBINTL_H': 0,
 'HAVE_LIBREADLINE': 1,
 'HAVE_LIBRESOLV': 0,
 'HAVE_LIBSENDFILE': 0,
 'HAVE_LIBUTIL_H': 0,
 'HAVE_LINK': 1,
 'HAVE_LINKAT': 0,
 'HAVE_LINUX_CAN_BCM_H': 0,
 'HAVE_LINUX_CAN_H': 0,
 'HAVE_LINUX_CAN_RAW_FD_FRAMES': 0,
 'HAVE_LINUX_CAN_RAW_H': 0,
 'HAVE_LINUX_NETLINK_H': 0,
 'HAVE_LINUX_RANDOM_H': 0,
 'HAVE_LINUX_TIPC_H': 0,
 'HAVE_LINUX_VM_SOCKETS_H': 0,
 'HAVE_LOCKF': 1,
 'HAVE_LOG1P': 1,
 'HAVE_LOG2': 1,
 'HAVE_LONG_DOUBLE': 1,
 'HAVE_LSTAT': 1,
 'HAVE_LUTIMES': 1,
 'HAVE_MAKEDEV': 1,
 'HAVE_MBRTOWC': 1,
 'HAVE_MEMORY_H': 1,
 'HAVE_MEMRCHR': 0,
 'HAVE_MKDIRAT': 0,
 'HAVE_MKFIFO': 1,
 'HAVE_MKFIFOAT': 0,
 'HAVE_MKNOD': 1,
 'HAVE_MKNODAT': 0,
 'HAVE_MKTIME': 1,
 'HAVE_MMAP': 1,
 'HAVE_MREMAP': 0,
 'HAVE_NCURSES_H': 1,
 'HAVE_NDIR_H': 0,
 'HAVE_NETPACKET_PACKET_H': 0,
 'HAVE_NET_IF_H': 1,
 'HAVE_NICE': 1,
 'HAVE_OPENAT': 0,
 'HAVE_OPENPTY': 1,
 'HAVE_PATHCONF': 1,
 'HAVE_PAUSE': 1,
 'HAVE_PIPE2': 0,
 'HAVE_PLOCK': 0,
 'HAVE_POLL': 1,
 'HAVE_POLL_H': 1,
 'HAVE_POSIX_FADVISE': 0,
 'HAVE_POSIX_FALLOCATE': 0,
 'HAVE_POSIX_SPAWN': 1,
 'HAVE_PREAD': 1,
 'HAVE_PREADV': 0,
 'HAVE_PREADV2': 0,
 'HAVE_PRLIMIT': 0,
 'HAVE_PROCESS_H': 0,
 'HAVE_PROTOTYPES': 1,
 'HAVE_PTHREAD_DESTRUCTOR': 0,
 'HAVE_PTHREAD_GETCPUCLOCKID': 0,
 'HAVE_PTHREAD_H': 1,
 'HAVE_PTHREAD_INIT': 0,
 'HAVE_PTHREAD_KILL': 1,
 'HAVE_PTHREAD_SIGMASK': 1,
 'HAVE_PTY_H': 0,
 'HAVE_PUTENV': 1,
 'HAVE_PWRITE': 1,
 'HAVE_PWRITEV': 0,
 'HAVE_PWRITEV2': 0,
 'HAVE_READLINK': 1,
 'HAVE_READLINKAT': 0,
 'HAVE_READV': 1,
 'HAVE_REALPATH': 1,
 'HAVE_RENAMEAT': 0,
 'HAVE_RL_APPEND_HISTORY': 1,
 'HAVE_RL_CATCH_SIGNAL': 1,
 'HAVE_RL_COMPLETION_APPEND_CHARACTER': 1,
 'HAVE_RL_COMPLETION_DISPLAY_MATCHES_HOOK': 1,
 'HAVE_RL_COMPLETION_MATCHES': 1,
 'HAVE_RL_COMPLETION_SUPPRESS_APPEND': 1,
 'HAVE_RL_PRE_INPUT_HOOK': 1,
 'HAVE_RL_RESIZE_TERMINAL': 1,
 'HAVE_ROUND': 1,
 'HAVE_SCHED_GET_PRIORITY_MAX': 1,
 'HAVE_SCHED_H': 1,
 'HAVE_SCHED_RR_GET_INTERVAL': 0,
 'HAVE_SCHED_SETAFFINITY': 0,
 'HAVE_SCHED_SETPARAM': 0,
 'HAVE_SCHED_SETSCHEDULER': 0,
 'HAVE_SEM_GETVALUE': 1,
 'HAVE_SEM_OPEN': 1,
 'HAVE_SEM_TIMEDWAIT': 0,
 'HAVE_SEM_UNLINK': 1,
 'HAVE_SENDFILE': 1,
 'HAVE_SETEGID': 1,
 'HAVE_SETEUID': 1,
 'HAVE_SETGID': 1,
 'HAVE_SETGROUPS': 1,
 'HAVE_SETHOSTNAME': 1,
 'HAVE_SETITIMER': 1,
 'HAVE_SETLOCALE': 1,
 'HAVE_SETPGID': 1,
 'HAVE_SETPGRP': 1,
 'HAVE_SETPRIORITY': 1,
 'HAVE_SETREGID': 1,
 'HAVE_SETRESGID': 0,
 'HAVE_SETRESUID': 0,
 'HAVE_SETREUID': 1,
 'HAVE_SETSID': 1,
 'HAVE_SETUID': 1,
 'HAVE_SETVBUF': 1,
 'HAVE_SHADOW_H': 0,
 'HAVE_SIGACTION': 1,
 'HAVE_SIGALTSTACK': 1,
 'HAVE_SIGINFO_T_SI_BAND': 1,
 'HAVE_SIGINTERRUPT': 1,
 'HAVE_SIGNAL_H': 1,
 'HAVE_SIGPENDING': 1,
 'HAVE_SIGRELSE': 1,
 'HAVE_SIGTIMEDWAIT': 0,
 'HAVE_SIGWAIT': 1,
 'HAVE_SIGWAITINFO': 0,
 'HAVE_SNPRINTF': 1,
 'HAVE_SOCKADDR_ALG': 0,
 'HAVE_SOCKADDR_SA_LEN': 1,
 'HAVE_SOCKADDR_STORAGE': 1,
 'HAVE_SOCKETPAIR': 1,
 'HAVE_SPAWN_H': 1,
 'HAVE_SSIZE_T': 1,
 'HAVE_STATVFS': 1,
 'HAVE_STAT_TV_NSEC': 0,
 'HAVE_STAT_TV_NSEC2': 1,
 'HAVE_STDARG_PROTOTYPES': 1,
 'HAVE_STDINT_H': 1,
 'HAVE_STDLIB_H': 1,
 'HAVE_STD_ATOMIC': 0,
 'HAVE_STRDUP': 1,
 'HAVE_STRFTIME': 1,
 'HAVE_STRINGS_H': 1,
 'HAVE_STRING_H': 1,
 'HAVE_STRLCPY': 1,
 'HAVE_STROPTS_H': 0,
 'HAVE_STRUCT_PASSWD_PW_GECOS': 1,
 'HAVE_STRUCT_PASSWD_PW_PASSWD': 1,
 'HAVE_STRUCT_STAT_ST_BIRTHTIME': 1,
 'HAVE_STRUCT_STAT_ST_BLKSIZE': 1,
 'HAVE_STRUCT_STAT_ST_BLOCKS': 1,
 'HAVE_STRUCT_STAT_ST_FLAGS': 1,
 'HAVE_STRUCT_STAT_ST_GEN': 1,
 'HAVE_STRUCT_STAT_ST_RDEV': 1,
 'HAVE_STRUCT_TM_TM_ZONE': 1,
 'HAVE_SYMLINK': 1,
 'HAVE_SYMLINKAT': 0,
 'HAVE_SYNC': 1,
 'HAVE_SYSCONF': 1,
 'HAVE_SYSEXITS_H': 1,
 'HAVE_SYS_AUDIOIO_H': 0,
 'HAVE_SYS_BSDTTY_H': 0,
 'HAVE_SYS_DEVPOLL_H': 0,
 'HAVE_SYS_DIR_H': 0,
 'HAVE_SYS_ENDIAN_H': 0,
 'HAVE_SYS_EPOLL_H': 0,
 'HAVE_SYS_EVENT_H': 1,
 'HAVE_SYS_FILE_H': 1,
 'HAVE_SYS_IOCTL_H': 1,
 'HAVE_SYS_KERN_CONTROL_H': 1,
 'HAVE_SYS_LOADAVG_H': 0,
 'HAVE_SYS_LOCK_H': 1,
 'HAVE_SYS_MKDEV_H': 0,
 'HAVE_SYS_MODEM_H': 0,
 'HAVE_SYS_NDIR_H': 0,
 'HAVE_SYS_PARAM_H': 1,
 'HAVE_SYS_POLL_H': 1,
 'HAVE_SYS_RANDOM_H': 1,
 'HAVE_SYS_RESOURCE_H': 1,
 'HAVE_SYS_SELECT_H': 1,
 'HAVE_SYS_SENDFILE_H': 0,
 'HAVE_SYS_SOCKET_H': 1,
 'HAVE_SYS_STATVFS_H': 1,
 'HAVE_SYS_STAT_H': 1,
 'HAVE_SYS_SYSCALL_H': 1,
 'HAVE_SYS_SYSMACROS_H': 0,
 'HAVE_SYS_SYS_DOMAIN_H': 1,
 'HAVE_SYS_TERMIO_H': 0,
 'HAVE_SYS_TIMES_H': 1,
 'HAVE_SYS_TIME_H': 1,
 'HAVE_SYS_TYPES_H': 1,
 'HAVE_SYS_UIO_H': 1,
 'HAVE_SYS_UN_H': 1,
 'HAVE_SYS_UTSNAME_H': 1,
 'HAVE_SYS_WAIT_H': 1,
 'HAVE_SYS_XATTR_H': 1,
 'HAVE_TCGETPGRP': 1,
 'HAVE_TCSETPGRP': 1,
 'HAVE_TEMPNAM': 1,
 'HAVE_TERMIOS_H': 1,
 'HAVE_TERM_H': 1,
 'HAVE_TGAMMA': 1,
 'HAVE_TIMEGM': 1,
 'HAVE_TIMES': 1,
 'HAVE_TMPFILE': 1,
 'HAVE_TMPNAM': 1,
 'HAVE_TMPNAM_R': 0,
 'HAVE_TM_ZONE': 1,
 'HAVE_TRUNCATE': 1,
 'HAVE_TZNAME': 0,
 'HAVE_UCS4_TCL': 0,
 'HAVE_UNAME': 1,
 'HAVE_UNISTD_H': 1,
 'HAVE_UNLINKAT': 0,
 'HAVE_UNSETENV': 1,
 'HAVE_USABLE_WCHAR_T': 0,
 'HAVE_UTIL_H': 1,
 'HAVE_UTIMENSAT': 0,
 'HAVE_UTIMES': 1,
 'HAVE_UTIME_H': 1,
 'HAVE_UUID_CREATE': 0,
 'HAVE_UUID_GENERATE_TIME_SAFE': 0,
 'HAVE_UUID_H': 0,
 'HAVE_UUID_UUID_H': 1,
 'HAVE_WAIT3': 1,
 'HAVE_WAIT4': 1,
 'HAVE_WAITID': 1,
 'HAVE_WAITPID': 1,
 'HAVE_WCHAR_H': 1,
 'HAVE_WCSCOLL': 1,
 'HAVE_WCSFTIME': 1,
 'HAVE_WCSXFRM': 1,
 'HAVE_WMEMCMP': 1,
 'HAVE_WORKING_TZSET': 1,
 'HAVE_WRITEV': 1,
 'HAVE_X509_VERIFY_PARAM_SET1_HOST': 1,
 'HAVE_ZLIB_COPY': 1,
 'HAVE__GETPTY': 0,
 'HOST_GNU_TYPE': 'x86_64-apple-darwin13.4.0',
 'INCLDIRSTOMAKE': '/home/<USER>/anaconda3/envs/django/include '
                   '/home/<USER>/anaconda3/envs/django/include '
                   '/home/<USER>/anaconda3/envs/django/include/python3.8 '
                   '/home/<USER>/anaconda3/envs/django/include/python3.8',
 'INCLUDEDIR': '/home/<USER>/anaconda3/envs/django/include',
 'INCLUDEPY': '/home/<USER>/anaconda3/envs/django/include/python3.8',
 'INSTALL': '/usr/bin/install -c',
 'INSTALL_DATA': '/usr/bin/install -c -m 644',
 'INSTALL_PROGRAM': '/usr/bin/install -c',
 'INSTALL_SCRIPT': '/usr/bin/install -c',
 'INSTALL_SHARED': '/usr/bin/install -c -m 555',
 'INSTSONAME': 'libpython3.8.a',
 'IO_H': 'Modules/_io/_iomodule.h',
 'IO_OBJS': '\\',
 'LDCXXSHARED': 'x86_64-apple-darwin13.4.0-clang++ -bundle -undefined '
                'dynamic_lookup',
 'LDFLAGS': '-Wl,-pie -Wl,-headerpad_max_install_names -Wl,-dead_strip_dylibs '
            '-Wl,-rpath,/home/<USER>/anaconda3/envs/django/lib '
            '-L/home/<USER>/anaconda3/envs/django/lib '
            '-flto -Wl,-export_dynamic -Wl,-pie '
            '-Wl,-headerpad_max_install_names -Wl,-dead_strip_dylibs '
            '-Wl,-rpath,/home/<USER>/anaconda3/envs/django/lib '
            '-L/home/<USER>/anaconda3/envs/django/lib',
 'LDLAST': '',
 'LDLIBRARY': 'libpython3.8.a',
 'LDLIBRARYDIR': '',
 'LDSHARED': 'x86_64-apple-darwin13.4.0-clang -bundle -undefined '
             'dynamic_lookup -Wl,-pie -Wl,-headerpad_max_install_names '
             '-Wl,-dead_strip_dylibs '
             '-Wl,-rpath,/home/<USER>/anaconda3/envs/django/lib '
             '-L/home/<USER>/anaconda3/envs/django/lib '
             '-flto -Wl,-export_dynamic -Wl,-pie '
             '-Wl,-headerpad_max_install_names -Wl,-dead_strip_dylibs '
             '-Wl,-rpath,/home/<USER>/anaconda3/envs/django/lib '
             '-L/home/<USER>/anaconda3/envs/django/lib',
 'LDVERSION': '3.8',
 'LIBC': '',
 'LIBDEST': '/home/<USER>/anaconda3/envs/django/lib/python3.8',
 'LIBDIR': '/home/<USER>/anaconda3/envs/django/lib',
 'LIBFFI_INCLUDEDIR': '/home/<USER>/anaconda3/envs/django/include',
 'LIBM': '',
 'LIBOBJDIR': 'Python/',
 'LIBOBJS': '',
 'LIBPC': '/home/<USER>/anaconda3/envs/django/lib/pkgconfig',
 'LIBPL': '/home/<USER>/anaconda3/envs/django/lib/python3.8/config-3.8-darwin',
 'LIBRARY': 'libpython3.8.a',
 'LIBRARY_OBJS': '\\',
 'LIBRARY_OBJS_OMIT_FROZEN': '\\',
 'LIBS': '-ldl  -framework CoreFoundation',
 'LIBSUBDIRS': 'tkinter tkinter/test tkinter/test/test_tkinter \\',
 'LINKCC': 'x86_64-apple-darwin13.4.0-clang',
 'LINKFORSHARED': '-Wl,-stack_size,1000000  -framework CoreFoundation',
 'LIPO_32BIT_FLAGS': '',
 'LLVM_PROF_ERR': 'no',
 'LLVM_PROF_FILE': 'LLVM_PROFILE_FILE="code-%p.profclangr"',
 'LLVM_PROF_MERGER': '/opt/conda/conda-bld/python_1524823838194/_build_env/bin/llvm-profdata '
                     'merge -output=code.profclangd *.profclangr',
 'LN': 'ln',
 'LOCALMODLIBS': '',
 'LOG1P_DROPS_ZERO_SIGN': 0,
 'MACHDEP': 'darwin',
 'MACHDEP_OBJS': '',
 'MACHDESTLIB': '/home/<USER>/anaconda3/envs/django/lib/python3.8',
 'MACOSX_DEPLOYMENT_TARGET': '10.9',
 'MAINCC': 'x86_64-apple-darwin13.4.0-clang',
 'MAJOR_IN_MKDEV': 0,
 'MAJOR_IN_SYSMACROS': 0,
 'MAKESETUP': '${SRC_DIR}/Modules/makesetup',
 'MANDIR': '/home/<USER>/anaconda3/envs/django/share/man',
 'MKDIR_P': '${SRC_DIR}/install-sh -c -d',
 'MODBUILT_NAMES': 'posix  errno  pwd  _sre  _codecs  _weakref  _functools  '
                   '_operator  _collections  _abc  itertools  atexit  _signal  '
                   '_stat  time  _thread  _locale  _io  zipimport  '
                   'faulthandler  _tracemalloc  _symtable  xxsubtype',
 'MODDISABLED_NAMES': '',
 'MODLIBS': '',
 'MODOBJS': 'Modules/posixmodule.o  Modules/errnomodule.o  '
            'Modules/pwdmodule.o  Modules/_sre.o  Modules/_codecsmodule.o  '
            'Modules/_weakref.o  Modules/_functoolsmodule.o  '
            'Modules/_operator.o  Modules/_collectionsmodule.o  '
            'Modules/_abc.o  Modules/itertoolsmodule.o  '
            'Modules/atexitmodule.o  Modules/signalmodule.o  Modules/_stat.o  '
            'Modules/timemodule.o  Modules/_threadmodule.o  '
            'Modules/_localemodule.o  Modules/_iomodule.o Modules/iobase.o '
            'Modules/fileio.o Modules/bytesio.o Modules/bufferedio.o '
            'Modules/textio.o Modules/stringio.o  Modules/zipimport.o  '
            'Modules/faulthandler.o  Modules/_tracemalloc.o '
            'Modules/hashtable.o  Modules/symtablemodule.o  '
            'Modules/xxsubtype.o',
 'MODULE_OBJS': '\\',
 'MULTIARCH': 'darwin',
 'MULTIARCH_CPPFLAGS': '-DMULTIARCH=\\"darwin\\"',
 'MVWDELCH_IS_EXPRESSION': 1,
 'NO_AS_NEEDED': '',
 'OBJECT_OBJS': '\\',
 'OPENSSL_INCLUDES': '-I/home/<USER>/anaconda3/envs/django/include',
 'OPENSSL_LDFLAGS': '-L/home/<USER>/anaconda3/envs/django/lib',
 'OPENSSL_LIBS': '-lssl -lcrypto',
 'OPT': '-DNDEBUG -fwrapv -O3 -Wall -Wstrict-prototypes',
 'OTHER_LIBTOOL_OPT': '',
 'PACKAGE_BUGREPORT': 0,
 'PACKAGE_NAME': 0,
 'PACKAGE_STRING': 0,
 'PACKAGE_TARNAME': 0,
 'PACKAGE_URL': 0,
 'PACKAGE_VERSION': 0,
 'PARSER_HEADERS': '\\',
 'PARSER_OBJS': '\\ Parser/myreadline.o Parser/parsetok.o Parser/tokenizer.o',
 'PGEN': 'Parser/pgen',
 'PGENOBJS': '\\ \\',
 'PGOBJS': '\\',
 'PGO_PROF_GEN_FLAG': '-fprofile-instr-generate',
 'PGO_PROF_USE_FLAG': '-fprofile-instr-use=code.profclangd',
 'POBJS': '\\',
 'POSIX_SEMAPHORES_NOT_ENABLED': 0,
 'PROFILE_TASK': '-m test.regrtest --pgo',
 'PTHREAD_KEY_T_IS_COMPATIBLE_WITH_INT': 0,
 'PTHREAD_SYSTEM_SCHED_SUPPORTED': 1,
 'PURIFY': '',
 'PY3LIBRARY': '',
 'PYLONG_BITS_IN_DIGIT': 0,
 'PYTHON': 'python',
 'PYTHONFRAMEWORK': '',
 'PYTHONFRAMEWORKDIR': 'no-framework',
 'PYTHONFRAMEWORKINSTALLDIR': '',
 'PYTHONFRAMEWORKPREFIX': '',
 'PYTHONPATH': '',
 'PYTHON_FOR_BUILD': './python.exe -E',
 'PYTHON_FOR_REGEN': 'python3',
 'PYTHON_HEADERS': '\\',
 'PYTHON_OBJS': '\\',
 'PY_BUILD_ENVIRON': '',
 'PY_CFLAGS': '-fno-strict-aliasing -Wsign-compare -Wunreachable-code -DNDEBUG '
              '-fwrapv -O3 -Wall -Wstrict-prototypes -march=core2 '
              '-mtune=haswell -mssse3 -ftree-vectorize -fPIC -fPIE '
              '-fstack-protector-strong -O3 -pipe  '
              '-fdebug-prefix-map=${SRC_DIR}=/usr/local/src/conda/${PKG_NAME}-${PKG_VERSION} '
              '-fdebug-prefix-map=/home/<USER>/anaconda3/envs/django=/usr/local/src/conda-prefix '
              '-flto -flto -Wl,-export_dynamic -march=core2 -mtune=haswell '
              '-mssse3 -ftree-vectorize -fPIC -fPIE -fstack-protector-strong '
              '-O3 -pipe  '
              '-fdebug-prefix-map=${SRC_DIR}=/usr/local/src/conda/${PKG_NAME}-${PKG_VERSION} '
              '-fdebug-prefix-map=/home/<USER>/anaconda3/envs/django=/usr/local/src/conda-prefix '
              '-flto',
 'PY_CFLAGS_NODIST': '-std=c99 -Werror=implicit-function-declaration '
                     '-march=core2 -mtune=haswell -mssse3 -ftree-vectorize '
                     '-fPIC -fPIE -fstack-protector-strong -O3 -pipe  '
                     '-fdebug-prefix-map=${SRC_DIR}=/usr/local/src/conda/${PKG_NAME}-${PKG_VERSION} '
                     '-fdebug-prefix-map=/home/<USER>/anaconda3/envs/django=/usr/local/src/conda-prefix '
                     '-flto -fprofile-instr-use=code.profclangd',
 'PY_COERCE_C_LOCALE': 1,
 'PY_CORE_CFLAGS': '-fno-strict-aliasing -Wsign-compare -Wunreachable-code '
                   '-DNDEBUG -fwrapv -O3 -Wall -Wstrict-prototypes '
                   '-march=core2 -mtune=haswell -mssse3 -ftree-vectorize -fPIC '
                   '-fPIE -fstack-protector-strong -O3 -pipe  '
                   '-fdebug-prefix-map=${SRC_DIR}=/usr/local/src/conda/${PKG_NAME}-${PKG_VERSION} '
                   '-fdebug-prefix-map=/home/<USER>/anaconda3/envs/django=/usr/local/src/conda-prefix '
                   '-flto -flto -Wl,-export_dynamic -march=core2 '
                   '-mtune=haswell -mssse3 -ftree-vectorize -fPIC -fPIE '
                   '-fstack-protector-strong -O3 -pipe  '
                   '-fdebug-prefix-map=${SRC_DIR}=/usr/local/src/conda/${PKG_NAME}-${PKG_VERSION} '
                   '-fdebug-prefix-map=/home/<USER>/anaconda3/envs/django=/usr/local/src/conda-prefix '
                   '-flto -std=c99 -Werror=implicit-function-declaration '
                   '-march=core2 -mtune=haswell -mssse3 -ftree-vectorize -fPIC '
                   '-fPIE -fstack-protector-strong -O3 -pipe  '
                   '-fdebug-prefix-map=${SRC_DIR}=/usr/local/src/conda/${PKG_NAME}-${PKG_VERSION} '
                   '-fdebug-prefix-map=/home/<USER>/anaconda3/envs/django=/usr/local/src/conda-prefix '
                   '-flto -fprofile-instr-use=code.profclangd -IObjects '
                   '-IInclude -IPython -I. '
                   '-I${SRC_DIR}/Include '
                   '-D_FORTIFY_SOURCE=2 -mmacosx-version-min=10.9 '
                   '-I/home/<USER>/anaconda3/envs/django/include '
                   '-D_FORTIFY_SOURCE=2 -mmacosx-version-min=10.9 '
                   '-I/home/<USER>/anaconda3/envs/django/include  '
                   '-DPy_BUILD_CORE',
 'PY_CPPFLAGS': '-IObjects -IInclude -IPython -I. '
                '-I${SRC_DIR}/Include '
                '-D_FORTIFY_SOURCE=2 -mmacosx-version-min=10.9 '
                '-I/home/<USER>/anaconda3/envs/django/include '
                '-D_FORTIFY_SOURCE=2 -mmacosx-version-min=10.9 '
                '-I/home/<USER>/anaconda3/envs/django/include',
 'PY_FORMAT_SIZE_T': '"z"',
 'PY_LDFLAGS': '-Wl,-pie -Wl,-headerpad_max_install_names '
               '-Wl,-dead_strip_dylibs '
               '-Wl,-rpath,/home/<USER>/anaconda3/envs/django/lib '
               '-L/home/<USER>/anaconda3/envs/django/lib '
               '-flto -Wl,-export_dynamic -Wl,-pie '
               '-Wl,-headerpad_max_install_names -Wl,-dead_strip_dylibs '
               '-Wl,-rpath,/home/<USER>/anaconda3/envs/django/lib '
               '-L/home/<USER>/anaconda3/envs/django/lib',
 'PY_SSL_DEFAULT_CIPHERS': 1,
 'PY_SSL_DEFAULT_CIPHER_STRING': 0,
 'PY_WARN_ON_C_LOCALE': 1,
 'Py_DEBUG': 0,
 'Py_ENABLE_SHARED': 0,
 'Py_HASH_ALGORITHM': 0,
 'QUICKTESTOPTS': '-x test_subprocess test_io test_lib2to3 \\',
 'READELF': ':',
 'RESSRCDIR': 'Mac/Resources/framework',
 'RETSIGTYPE': 'void',
 'RUNSHARED': '',
 'SCRIPTDIR': '/home/<USER>/anaconda3/envs/django/lib',
 'SETPGRP_HAVE_ARG': 0,
 'SGI_ABI': '',
 'SHELL': '/bin/sh',
 'SHLIBS': '-ldl  -framework CoreFoundation',
 'SHLIB_SUFFIX': '.so',
 'SIGNED_RIGHT_SHIFT_ZERO_FILLS': 0,
 'SITEPATH': '',
 'SIZEOF_DOUBLE': 8,
 'SIZEOF_FLOAT': 4,
 'SIZEOF_FPOS_T': 8,
 'SIZEOF_INT': 4,
 'SIZEOF_LONG': 8,
 'SIZEOF_LONG_DOUBLE': 16,
 'SIZEOF_LONG_LONG': 8,
 'SIZEOF_OFF_T': 8,
 'SIZEOF_PID_T': 4,
 'SIZEOF_PTHREAD_KEY_T': 8,
 'SIZEOF_PTHREAD_T': 8,
 'SIZEOF_SHORT': 2,
 'SIZEOF_SIZE_T': 8,
 'SIZEOF_TIME_T': 8,
 'SIZEOF_UINTPTR_T': 8,
 'SIZEOF_VOID_P': 8,
 'SIZEOF_WCHAR_T': 4,
 'SIZEOF__BOOL': 1,
 'SOABI': 'cpython-38-darwin',
 'SRCDIRS': 'Parser Objects Python Modules Programs',
 'SRC_GDB_HOOKS': '${SRC_DIR}/Tools/gdb/libpython.py',
 'STDC_HEADERS': 1,
 'STRICT_SYSV_CURSES': "/* Don't use ncurses extensions */",
 'STRIPFLAG': '-s',
 'SUBDIRS': '',
 'SUBDIRSTOO': 'Include Lib Misc',
 'SYSLIBS': '',
 'SYS_SELECT_WITH_SYS_TIME': 1,
 'TANH_PRESERVES_ZERO_SIGN': 1,
 'TCLTK_INCLUDES': '-I/home/<USER>/anaconda3/envs/django/include',
 'TCLTK_LIBS': '-L/home/<USER>/anaconda3/envs/django/lib '
               '-ltcl8.6 -ltk8.6',
 'TESTOPTS': '',
 'TESTPATH': '',
 'TESTPYTHON': './python.exe',
 'TESTPYTHONOPTS': '',
 'TESTRUNNER': './python.exe '
               '${SRC_DIR}/Tools/scripts/run_tests.py',
 'TESTTIMEOUT': 1200,
 'TIMEMODULE_LIB': 0,
 'TIME_WITH_SYS_TIME': 1,
 'TM_IN_SYS_TIME': 0,
 'UNICODE_DEPS': '\\',
 'UNIVERSALSDK': '',
 'UPDATE_FILE': 'python3 '
                '${SRC_DIR}/Tools/scripts/update_file.py',
 'USE_COMPUTED_GOTOS': 1,
 'VERSION': '3.8',
 'VPATH': '${SRC_DIR}',
 'WINDOW_HAS_FLAGS': 1,
 'WITH_DOC_STRINGS': 1,
 'WITH_DTRACE': 0,
 'WITH_DYLD': 1,
 'WITH_LIBINTL': 0,
 'WITH_NEXT_FRAMEWORK': 0,
 'WITH_PYMALLOC': 1,
 'WITH_VALGRIND': 0,
 'X87_DOUBLE_ROUNDING': 0,
 'XMLLIBSUBDIRS': 'xml xml/dom xml/etree xml/parsers xml/sax',
 'abs_builddir': '${SRC_DIR}/build-static',
 'abs_srcdir': '${SRC_DIR}',
 'datarootdir': '/home/<USER>/anaconda3/envs/django/share',
 'exec_prefix': '/home/<USER>/anaconda3/envs/django',
 'prefix': '/home/<USER>/anaconda3/envs/django',
 'srcdir': '${SRC_DIR}'}
