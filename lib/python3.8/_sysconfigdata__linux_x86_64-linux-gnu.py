# system configuration generated and used by the sysconfig module
build_time_vars = {'ABIFLAGS': '',
 'AC_APPLE_UNIVERSAL_BUILD': 0,
 'AIX_GENUINE_CPLUSPLUS': 0,
 'ANDROID_API_LEVEL': 0,
 'AR': 'ar',
 'ARFLAGS': 'rc',
 'BASECFLAGS': '-Wsign-compare',
 'BASECPPFLAGS': '',
 'BASEMODLIBS': '',
 'BINDIR': '/home/<USER>/anaconda3/envs/django/bin',
 'BINLIBDEST': '/home/<USER>/anaconda3/envs/django/lib/python3.8',
 'BLDLIBRARY': '-L. -lpython3.8',
 'BLDSHARED': 'gcc -pthread -shared -B /home/<USER>/anaconda3/envs/django/compiler_compat '
              '-L/home/<USER>/anaconda3/envs/django/lib '
              '-Wl,-rpath=/home/<USER>/anaconda3/envs/django/lib -Wl,--no-as-needed '
              '-Wl,--sysroot=/',
 'BUILDEXE': '',
 'BUILDPYTHON': 'python',
 'BUILD_GNU_TYPE': 'x86_64-pc-linux-gnu',
 'BYTESTR_DEPS': '\\',
 'CC': 'gcc -pthread -B /home/<USER>/anaconda3/envs/django/compiler_compat -Wl,--sysroot=/',
 'CCSHARED': '-fPIC',
 'CFLAGS': '-Wsign-compare -DNDEBUG -g -fwrapv -O3 -Wall -Wstrict-prototypes',
 'CFLAGSFORSHARED': '-fPIC',
 'CFLAGS_ALIASING': '',
 'CFLAGS_NODIST': '',
 'CONFIGFILES': 'configure configure.ac acconfig.h pyconfig.h.in '
                'Makefile.pre.in',
 'CONFIGURE_CFLAGS': '',
 'CONFIGURE_CFLAGS_NODIST': '-std=c99 -Wextra -Wno-unused-parameter '
                            '-Wno-missing-field-initializers',
 'CONFIGURE_CPPFLAGS': '-I/home/<USER>/anaconda3/envs/django/include',
 'CONFIGURE_LDFLAGS': '-L/home/<USER>/anaconda3/envs/django/lib '
                      '-Wl,-rpath=/home/<USER>/anaconda3/envs/django/lib -Wl,--no-as-needed '
                      '-Wl,--sysroot=/',
 'CONFIG_ARGS': "'--enable-shared' '--enable-ipv6' '--with-ensurepip=no' "
                "'--prefix=/home/<USER>/anaconda3/envs/django' "
                "'--with-tcltk-includes=-I/home/<USER>/anaconda3/envs/django/include' "
                "'--with-tcltk-libs=-L/home/<USER>/anaconda3/envs/django/lib "
                "-ltcl8.6 -ltk8.6' "
                "'CPPFLAGS=-I/home/<USER>/anaconda3/envs/django/include' "
                "'LDFLAGS=-L/home/<USER>/anaconda3/envs/django/lib "
                "-Wl,-rpath=/home/<USER>/anaconda3/envs/django/lib -Wl,--no-as-needed -Wl,--sysroot=/' "
                "'PKG_CONFIG_PATH=/home/<USER>/anaconda3/envs/django/lib/pkgconfig'",
 'CONFINCLUDEDIR': '/home/<USER>/anaconda3/envs/django/include',
 'CONFINCLUDEPY': '/home/<USER>/anaconda3/envs/django/include/python3.8',
 'COREPYTHONPATH': ':',
 'COVERAGE_INFO': '/home/<USER>/anaconda3/envs/django/../work/Python-3.8.18/coverage.info',
 'COVERAGE_REPORT': '/home/<USER>/anaconda3/envs/django/../work/Python-3.8.18/lcov-report',
 'COVERAGE_REPORT_OPTIONS': '--no-branch-coverage --title "CPython lcov '
                            'report"',
 'CPPFLAGS': '-I. -I./Include '
             '-I/home/<USER>/anaconda3/envs/django/include',
 'CXX': 'g++ -pthread -B /home/<USER>/anaconda3/envs/django/compiler_compat -Wl,--sysroot=/',
 'DESTDIRS': '/home/<USER>/anaconda3/envs/django '
             '/home/<USER>/anaconda3/envs/django/lib '
             '/home/<USER>/anaconda3/envs/django/lib/python3.8 '
             '/home/<USER>/anaconda3/envs/django/lib/python3.8/lib-dynload',
 'DESTLIB': '/home/<USER>/anaconda3/envs/django/lib/python3.8',
 'DESTPATH': '',
 'DESTSHARED': '/home/<USER>/anaconda3/envs/django/lib/python3.8/lib-dynload',
 'DFLAGS': '',
 'DIRMODE': 755,
 'DIST': 'README ChangeLog configure configure.ac acconfig.h pyconfig.h.in '
         'Makefile.pre.in Include Lib Misc Ext-dummy',
 'DISTDIRS': 'Include Lib Misc Ext-dummy',
 'DISTFILES': 'README ChangeLog configure configure.ac acconfig.h '
              'pyconfig.h.in Makefile.pre.in',
 'DLINCLDIR': '.',
 'DLLLIBRARY': '',
 'DOUBLE_IS_ARM_MIXED_ENDIAN_IEEE754': 0,
 'DOUBLE_IS_BIG_ENDIAN_IEEE754': 0,
 'DOUBLE_IS_LITTLE_ENDIAN_IEEE754': 1,
 'DTRACE': '',
 'DTRACE_DEPS': '\\',
 'DTRACE_HEADERS': '',
 'DTRACE_OBJS': '',
 'DYNLOADFILE': 'dynload_shlib.o',
 'ENABLE_IPV6': 1,
 'ENSUREPIP': 'no',
 'EXE': '',
 'EXEMODE': 755,
 'EXTRAMACHDEPPATH': '',
 'EXTRATESTOPTS': '',
 'EXTRA_CFLAGS': '',
 'EXT_SUFFIX': '.cpython-38-x86_64-linux-gnu.so',
 'FILEMODE': 644,
 'FLOCK_NEEDS_LIBBSD': 0,
 'GETPGRP_HAVE_ARG': 0,
 'GETTIMEOFDAY_NO_TZ': 0,
 'GITBRANCH': '',
 'GITTAG': '',
 'GITVERSION': '',
 'GNULD': 'yes',
 'HAVE_ACCEPT4': 0,
 'HAVE_ACOSH': 1,
 'HAVE_ADDRINFO': 1,
 'HAVE_ALARM': 1,
 'HAVE_ALIGNED_REQUIRED': 0,
 'HAVE_ALLOCA_H': 1,
 'HAVE_ALTZONE': 0,
 'HAVE_ASINH': 1,
 'HAVE_ASM_TYPES_H': 1,
 'HAVE_ATANH': 1,
 'HAVE_BIND_TEXTDOMAIN_CODESET': 1,
 'HAVE_BLUETOOTH_BLUETOOTH_H': 0,
 'HAVE_BLUETOOTH_H': 0,
 'HAVE_BROKEN_MBSTOWCS': 0,
 'HAVE_BROKEN_NICE': 0,
 'HAVE_BROKEN_PIPE_BUF': 0,
 'HAVE_BROKEN_POLL': 0,
 'HAVE_BROKEN_POSIX_SEMAPHORES': 0,
 'HAVE_BROKEN_PTHREAD_SIGMASK': 0,
 'HAVE_BROKEN_SEM_GETVALUE': 0,
 'HAVE_BROKEN_UNSETENV': 0,
 'HAVE_BUILTIN_ATOMIC': 0,
 'HAVE_CHFLAGS': 0,
 'HAVE_CHOWN': 1,
 'HAVE_CHROOT': 1,
 'HAVE_CLOCK': 1,
 'HAVE_CLOCK_GETRES': 1,
 'HAVE_CLOCK_GETTIME': 1,
 'HAVE_CLOCK_SETTIME': 1,
 'HAVE_COMPUTED_GOTOS': 1,
 'HAVE_CONFSTR': 1,
 'HAVE_CONIO_H': 0,
 'HAVE_COPYSIGN': 1,
 'HAVE_CTERMID': 1,
 'HAVE_CTERMID_R': 0,
 'HAVE_CURSES_H': 1,
 'HAVE_CURSES_IS_TERM_RESIZED': 1,
 'HAVE_CURSES_RESIZETERM': 1,
 'HAVE_CURSES_RESIZE_TERM': 1,
 'HAVE_DECL_ISFINITE': 1,
 'HAVE_DECL_ISINF': 1,
 'HAVE_DECL_ISNAN': 1,
 'HAVE_DECL_RTLD_DEEPBIND': 1,
 'HAVE_DECL_RTLD_GLOBAL': 1,
 'HAVE_DECL_RTLD_LAZY': 1,
 'HAVE_DECL_RTLD_LOCAL': 1,
 'HAVE_DECL_RTLD_NODELETE': 1,
 'HAVE_DECL_RTLD_NOLOAD': 1,
 'HAVE_DECL_RTLD_NOW': 1,
 'HAVE_DECL_TZNAME': 0,
 'HAVE_DEVICE_MACROS': 1,
 'HAVE_DEV_PTC': 0,
 'HAVE_DEV_PTMX': 1,
 'HAVE_DIRECT_H': 0,
 'HAVE_DIRENT_D_TYPE': 1,
 'HAVE_DIRENT_H': 1,
 'HAVE_DIRFD': 1,
 'HAVE_DLFCN_H': 1,
 'HAVE_DLOPEN': 1,
 'HAVE_DUP2': 1,
 'HAVE_DUP3': 0,
 'HAVE_DYNAMIC_LOADING': 1,
 'HAVE_ENDIAN_H': 1,
 'HAVE_EPOLL': 1,
 'HAVE_EPOLL_CREATE1': 0,
 'HAVE_ERF': 1,
 'HAVE_ERFC': 1,
 'HAVE_ERRNO_H': 1,
 'HAVE_EXECV': 1,
 'HAVE_EXPM1': 1,
 'HAVE_FACCESSAT': 1,
 'HAVE_FCHDIR': 1,
 'HAVE_FCHMOD': 1,
 'HAVE_FCHMODAT': 1,
 'HAVE_FCHOWN': 1,
 'HAVE_FCHOWNAT': 1,
 'HAVE_FCNTL_H': 1,
 'HAVE_FDATASYNC': 1,
 'HAVE_FDOPENDIR': 1,
 'HAVE_FEXECVE': 1,
 'HAVE_FINITE': 1,
 'HAVE_FLOCK': 1,
 'HAVE_FORK': 1,
 'HAVE_FORKPTY': 1,
 'HAVE_FPATHCONF': 1,
 'HAVE_FSEEK64': 0,
 'HAVE_FSEEKO': 1,
 'HAVE_FSTATAT': 1,
 'HAVE_FSTATVFS': 1,
 'HAVE_FSYNC': 1,
 'HAVE_FTELL64': 0,
 'HAVE_FTELLO': 1,
 'HAVE_FTIME': 1,
 'HAVE_FTRUNCATE': 1,
 'HAVE_FUTIMENS': 0,
 'HAVE_FUTIMES': 1,
 'HAVE_FUTIMESAT': 1,
 'HAVE_GAI_STRERROR': 1,
 'HAVE_GAMMA': 1,
 'HAVE_GCC_ASM_FOR_MC68881': 0,
 'HAVE_GCC_ASM_FOR_X64': 1,
 'HAVE_GCC_ASM_FOR_X87': 1,
 'HAVE_GCC_UINT128_T': 1,
 'HAVE_GETADDRINFO': 1,
 'HAVE_GETC_UNLOCKED': 1,
 'HAVE_GETENTROPY': 0,
 'HAVE_GETGROUPLIST': 1,
 'HAVE_GETGROUPS': 1,
 'HAVE_GETHOSTBYNAME': 0,
 'HAVE_GETHOSTBYNAME_R': 1,
 'HAVE_GETHOSTBYNAME_R_3_ARG': 0,
 'HAVE_GETHOSTBYNAME_R_5_ARG': 0,
 'HAVE_GETHOSTBYNAME_R_6_ARG': 1,
 'HAVE_GETITIMER': 1,
 'HAVE_GETLOADAVG': 1,
 'HAVE_GETLOGIN': 1,
 'HAVE_GETNAMEINFO': 1,
 'HAVE_GETPAGESIZE': 1,
 'HAVE_GETPEERNAME': 1,
 'HAVE_GETPGID': 1,
 'HAVE_GETPGRP': 1,
 'HAVE_GETPID': 1,
 'HAVE_GETPRIORITY': 1,
 'HAVE_GETPWENT': 1,
 'HAVE_GETRANDOM': 0,
 'HAVE_GETRANDOM_SYSCALL': 0,
 'HAVE_GETRESGID': 1,
 'HAVE_GETRESUID': 1,
 'HAVE_GETSID': 1,
 'HAVE_GETSPENT': 1,
 'HAVE_GETSPNAM': 1,
 'HAVE_GETTIMEOFDAY': 1,
 'HAVE_GETWD': 1,
 'HAVE_GLIBC_MEMMOVE_BUG': 0,
 'HAVE_GRP_H': 1,
 'HAVE_HSTRERROR': 1,
 'HAVE_HTOLE64': 0,
 'HAVE_HYPOT': 1,
 'HAVE_IEEEFP_H': 0,
 'HAVE_IF_NAMEINDEX': 1,
 'HAVE_INET_ATON': 1,
 'HAVE_INET_PTON': 1,
 'HAVE_INITGROUPS': 1,
 'HAVE_INTTYPES_H': 1,
 'HAVE_IO_H': 0,
 'HAVE_IPA_PURE_CONST_BUG': 0,
 'HAVE_KILL': 1,
 'HAVE_KILLPG': 1,
 'HAVE_KQUEUE': 0,
 'HAVE_LANGINFO_H': 1,
 'HAVE_LARGEFILE_SUPPORT': 0,
 'HAVE_LCHFLAGS': 0,
 'HAVE_LCHMOD': 0,
 'HAVE_LCHOWN': 1,
 'HAVE_LGAMMA': 1,
 'HAVE_LIBDL': 1,
 'HAVE_LIBDLD': 0,
 'HAVE_LIBIEEE': 0,
 'HAVE_LIBINTL_H': 1,
 'HAVE_LIBREADLINE': 1,
 'HAVE_LIBRESOLV': 0,
 'HAVE_LIBSENDFILE': 0,
 'HAVE_LIBUTIL_H': 0,
 'HAVE_LINK': 1,
 'HAVE_LINKAT': 1,
 'HAVE_LINUX_CAN_BCM_H': 0,
 'HAVE_LINUX_CAN_H': 0,
 'HAVE_LINUX_CAN_RAW_FD_FRAMES': 0,
 'HAVE_LINUX_CAN_RAW_H': 0,
 'HAVE_LINUX_NETLINK_H': 1,
 'HAVE_LINUX_RANDOM_H': 0,
 'HAVE_LINUX_TIPC_H': 1,
 'HAVE_LOCKF': 1,
 'HAVE_LOG1P': 1,
 'HAVE_LOG2': 1,
 'HAVE_LONG_DOUBLE': 1,
 'HAVE_LSTAT': 1,
 'HAVE_LUTIMES': 0,
 'HAVE_MAKEDEV': 1,
 'HAVE_MBRTOWC': 1,
 'HAVE_MEMMOVE': 1,
 'HAVE_MEMORY_H': 1,
 'HAVE_MEMRCHR': 1,
 'HAVE_MKDIRAT': 1,
 'HAVE_MKFIFO': 1,
 'HAVE_MKFIFOAT': 1,
 'HAVE_MKNOD': 1,
 'HAVE_MKNODAT': 1,
 'HAVE_MKTIME': 1,
 'HAVE_MMAP': 1,
 'HAVE_MREMAP': 1,
 'HAVE_NCURSES_H': 1,
 'HAVE_NDIR_H': 0,
 'HAVE_NETPACKET_PACKET_H': 1,
 'HAVE_NET_IF_H': 1,
 'HAVE_NICE': 1,
 'HAVE_OPENAT': 1,
 'HAVE_OPENPTY': 1,
 'HAVE_PATHCONF': 1,
 'HAVE_PAUSE': 1,
 'HAVE_PIPE2': 0,
 'HAVE_PLOCK': 0,
 'HAVE_POLL': 1,
 'HAVE_POLL_H': 1,
 'HAVE_POSIX_FADVISE': 1,
 'HAVE_POSIX_FALLOCATE': 1,
 'HAVE_PREAD': 1,
 'HAVE_PRLIMIT': 0,
 'HAVE_PROCESS_H': 0,
 'HAVE_PROTOTYPES': 1,
 'HAVE_PTHREAD_ATFORK': 1,
 'HAVE_PTHREAD_DESTRUCTOR': 0,
 'HAVE_PTHREAD_H': 1,
 'HAVE_PTHREAD_INIT': 0,
 'HAVE_PTHREAD_KILL': 1,
 'HAVE_PTHREAD_SIGMASK': 1,
 'HAVE_PTY_H': 1,
 'HAVE_PUTENV': 1,
 'HAVE_PWRITE': 1,
 'HAVE_READLINK': 1,
 'HAVE_READLINKAT': 1,
 'HAVE_READV': 1,
 'HAVE_REALPATH': 1,
 'HAVE_RENAMEAT': 1,
 'HAVE_RL_APPEND_HISTORY': 1,
 'HAVE_RL_CALLBACK': 1,
 'HAVE_RL_CATCH_SIGNAL': 1,
 'HAVE_RL_COMPLETION_APPEND_CHARACTER': 1,
 'HAVE_RL_COMPLETION_DISPLAY_MATCHES_HOOK': 1,
 'HAVE_RL_COMPLETION_MATCHES': 1,
 'HAVE_RL_COMPLETION_SUPPRESS_APPEND': 1,
 'HAVE_RL_PRE_INPUT_HOOK': 1,
 'HAVE_RL_RESIZE_TERMINAL': 1,
 'HAVE_ROUND': 1,
 'HAVE_SCHED_GET_PRIORITY_MAX': 1,
 'HAVE_SCHED_H': 1,
 'HAVE_SCHED_RR_GET_INTERVAL': 1,
 'HAVE_SCHED_SETAFFINITY': 1,
 'HAVE_SCHED_SETPARAM': 1,
 'HAVE_SCHED_SETSCHEDULER': 1,
 'HAVE_SELECT': 1,
 'HAVE_SEM_GETVALUE': 1,
 'HAVE_SEM_OPEN': 1,
 'HAVE_SEM_TIMEDWAIT': 1,
 'HAVE_SEM_UNLINK': 1,
 'HAVE_SENDFILE': 1,
 'HAVE_SETEGID': 1,
 'HAVE_SETEUID': 1,
 'HAVE_SETGID': 1,
 'HAVE_SETGROUPS': 1,
 'HAVE_SETHOSTNAME': 1,
 'HAVE_SETITIMER': 1,
 'HAVE_SETLOCALE': 1,
 'HAVE_SETPGID': 1,
 'HAVE_SETPGRP': 1,
 'HAVE_SETPRIORITY': 1,
 'HAVE_SETREGID': 1,
 'HAVE_SETRESGID': 1,
 'HAVE_SETRESUID': 1,
 'HAVE_SETREUID': 1,
 'HAVE_SETSID': 1,
 'HAVE_SETUID': 1,
 'HAVE_SETVBUF': 1,
 'HAVE_SHADOW_H': 1,
 'HAVE_SIGACTION': 1,
 'HAVE_SIGALTSTACK': 1,
 'HAVE_SIGINTERRUPT': 1,
 'HAVE_SIGNAL_H': 1,
 'HAVE_SIGPENDING': 1,
 'HAVE_SIGRELSE': 1,
 'HAVE_SIGTIMEDWAIT': 1,
 'HAVE_SIGWAIT': 1,
 'HAVE_SIGWAITINFO': 1,
 'HAVE_SNPRINTF': 1,
 'HAVE_SOCKADDR_ALG': 0,
 'HAVE_SOCKADDR_SA_LEN': 0,
 'HAVE_SOCKADDR_STORAGE': 1,
 'HAVE_SOCKETPAIR': 1,
 'HAVE_SPAWN_H': 1,
 'HAVE_SSIZE_T': 1,
 'HAVE_STATVFS': 1,
 'HAVE_STAT_TV_NSEC': 1,
 'HAVE_STAT_TV_NSEC2': 0,
 'HAVE_STDARG_PROTOTYPES': 1,
 'HAVE_STDINT_H': 1,
 'HAVE_STDLIB_H': 1,
 'HAVE_STD_ATOMIC': 0,
 'HAVE_STRDUP': 1,
 'HAVE_STRFTIME': 1,
 'HAVE_STRINGS_H': 1,
 'HAVE_STRING_H': 1,
 'HAVE_STRLCPY': 0,
 'HAVE_STROPTS_H': 1,
 'HAVE_STRUCT_PASSWD_PW_GECOS': 1,
 'HAVE_STRUCT_PASSWD_PW_PASSWD': 1,
 'HAVE_STRUCT_STAT_ST_BIRTHTIME': 0,
 'HAVE_STRUCT_STAT_ST_BLKSIZE': 1,
 'HAVE_STRUCT_STAT_ST_BLOCKS': 1,
 'HAVE_STRUCT_STAT_ST_FLAGS': 0,
 'HAVE_STRUCT_STAT_ST_GEN': 0,
 'HAVE_STRUCT_STAT_ST_RDEV': 1,
 'HAVE_STRUCT_TM_TM_ZONE': 1,
 'HAVE_SYMLINK': 1,
 'HAVE_SYMLINKAT': 1,
 'HAVE_SYNC': 1,
 'HAVE_SYSCONF': 1,
 'HAVE_SYSEXITS_H': 1,
 'HAVE_SYS_AUDIOIO_H': 0,
 'HAVE_SYS_BSDTTY_H': 0,
 'HAVE_SYS_DEVPOLL_H': 0,
 'HAVE_SYS_DIR_H': 0,
 'HAVE_SYS_ENDIAN_H': 0,
 'HAVE_SYS_EPOLL_H': 1,
 'HAVE_SYS_EVENT_H': 0,
 'HAVE_SYS_FILE_H': 1,
 'HAVE_SYS_IOCTL_H': 1,
 'HAVE_SYS_KERN_CONTROL_H': 0,
 'HAVE_SYS_LOADAVG_H': 0,
 'HAVE_SYS_LOCK_H': 0,
 'HAVE_SYS_MKDEV_H': 0,
 'HAVE_SYS_MODEM_H': 0,
 'HAVE_SYS_NDIR_H': 0,
 'HAVE_SYS_PARAM_H': 1,
 'HAVE_SYS_POLL_H': 1,
 'HAVE_SYS_RANDOM_H': 0,
 'HAVE_SYS_RESOURCE_H': 1,
 'HAVE_SYS_SELECT_H': 1,
 'HAVE_SYS_SENDFILE_H': 1,
 'HAVE_SYS_SOCKET_H': 1,
 'HAVE_SYS_STATVFS_H': 1,
 'HAVE_SYS_STAT_H': 1,
 'HAVE_SYS_SYSCALL_H': 1,
 'HAVE_SYS_SYS_DOMAIN_H': 0,
 'HAVE_SYS_TERMIO_H': 0,
 'HAVE_SYS_TIMES_H': 1,
 'HAVE_SYS_TIME_H': 1,
 'HAVE_SYS_TYPES_H': 1,
 'HAVE_SYS_UIO_H': 1,
 'HAVE_SYS_UN_H': 1,
 'HAVE_SYS_UTSNAME_H': 1,
 'HAVE_SYS_WAIT_H': 1,
 'HAVE_SYS_XATTR_H': 1,
 'HAVE_TCGETPGRP': 1,
 'HAVE_TCSETPGRP': 1,
 'HAVE_TEMPNAM': 1,
 'HAVE_TERMIOS_H': 1,
 'HAVE_TERM_H': 1,
 'HAVE_TGAMMA': 1,
 'HAVE_TIMEGM': 1,
 'HAVE_TIMES': 1,
 'HAVE_TMPFILE': 1,
 'HAVE_TMPNAM': 1,
 'HAVE_TMPNAM_R': 1,
 'HAVE_TM_ZONE': 1,
 'HAVE_TRUNCATE': 1,
 'HAVE_TZNAME': 0,
 'HAVE_UCS4_TCL': 0,
 'HAVE_UNAME': 1,
 'HAVE_UNISTD_H': 1,
 'HAVE_UNLINKAT': 1,
 'HAVE_UNSETENV': 1,
 'HAVE_USABLE_WCHAR_T': 0,
 'HAVE_UTIL_H': 0,
 'HAVE_UTIMENSAT': 0,
 'HAVE_UTIMES': 1,
 'HAVE_UTIME_H': 1,
 'HAVE_WAIT3': 1,
 'HAVE_WAIT4': 1,
 'HAVE_WAITID': 1,
 'HAVE_WAITPID': 1,
 'HAVE_WCHAR_H': 1,
 'HAVE_WCSCOLL': 1,
 'HAVE_WCSFTIME': 1,
 'HAVE_WCSXFRM': 1,
 'HAVE_WMEMCMP': 1,
 'HAVE_WORKING_TZSET': 1,
 'HAVE_WRITEV': 1,
 'HAVE_ZLIB_COPY': 1,
 'HAVE__GETPTY': 0,
 'HOST_GNU_TYPE': 'x86_64-pc-linux-gnu',
 'INCLDIRSTOMAKE': '/home/<USER>/anaconda3/envs/django/include '
                   '/home/<USER>/anaconda3/envs/django/include '
                   '/home/<USER>/anaconda3/envs/django/include/python3.8 '
                   '/home/<USER>/anaconda3/envs/django/include/python3.8',
 'INCLUDEDIR': '/home/<USER>/anaconda3/envs/django/include',
 'INCLUDEPY': '/home/<USER>/anaconda3/envs/django/include/python3.8',
 'INSTALL': '/usr/bin/install -c',
 'INSTALL_DATA': '/usr/bin/install -c -m 644',
 'INSTALL_PROGRAM': '/usr/bin/install -c',
 'INSTALL_SCRIPT': '/usr/bin/install -c',
 'INSTALL_SHARED': '/usr/bin/install -c -m 555',
 'INSTSONAME': 'libpython3.8.so.1.0',
 'IO_H': 'Modules/_io/_iomodule.h',
 'IO_OBJS': '\\',
 'LDCXXSHARED': 'g++ -pthread -shared -B /home/<USER>/anaconda3/envs/django/compiler_compat '
                '-L/home/<USER>/anaconda3/envs/django/lib '
                '-Wl,-rpath=/home/<USER>/anaconda3/envs/django/lib -Wl,--no-as-needed '
                '-Wl,--sysroot=/',
 'LDFLAGS': '-L/home/<USER>/anaconda3/envs/django/lib '
            '-Wl,-rpath=/home/<USER>/anaconda3/envs/django/lib -Wl,--no-as-needed '
            '-Wl,--sysroot=/',
 'LDLAST': '',
 'LDLIBRARY': 'libpython3.8.so',
 'LDLIBRARYDIR': '',
 'LDSHARED': 'gcc -pthread -shared -B /home/<USER>/anaconda3/envs/django/compiler_compat '
             '-L/home/<USER>/anaconda3/envs/django/lib '
             '-Wl,-rpath=/home/<USER>/anaconda3/envs/django/lib -Wl,--no-as-needed '
             '-Wl,--sysroot=/',
 'LDVERSION': '3.8',
 'LIBC': '',
 'LIBDEST': '/home/<USER>/anaconda3/envs/django/lib/python3.8',
 'LIBDIR': '/home/<USER>/anaconda3/envs/django/lib',
 'LIBFFI_INCLUDEDIR': '',
 'LIBM': '-lm',
 'LIBOBJDIR': 'Python/',
 'LIBOBJS': '',
 'LIBPC': '/home/<USER>/anaconda3/envs/django/lib/pkgconfig',
 'LIBPL': '/home/<USER>/anaconda3/envs/django/lib/python3.8/config-3.8-x86_64-linux-gnu',
 'LIBRARY': 'libpython3.8.a',
 'LIBRARY_OBJS': '\\',
 'LIBRARY_OBJS_OMIT_FROZEN': '\\',
 'LIBS': '-lpthread -ldl -lutil -lrt',
 'LIBSUBDIRS': 'tkinter tkinter/test tkinter/test/test_tkinter \\',
 'LINKCC': 'gcc -pthread -B /home/<USER>/anaconda3/envs/django/compiler_compat -Wl,--sysroot/',
 'LINKFORSHARED': '-Xlinker -export-dynamic',
 'LIPO_32BIT_FLAGS': '',
 'LLVM_PROF_ERR': 'no',
 'LLVM_PROF_FILE': '',
 'LLVM_PROF_MERGER': 'true',
 'LN': 'ln',
 'LOCALMODLIBS': '',
 'LOG1P_DROPS_ZERO_SIGN': 0,
 'MACHDEP': 'linux',
 'MACHDEPPATH': ':',
 'MACHDEP_OBJS': '',
 'MACHDESTLIB': '/home/<USER>/anaconda3/envs/django/lib/python3.8',
 'MACOSX_DEPLOYMENT_TARGET': '',
 'MAINCC': 'gcc -pthread -B /home/<USER>/anaconda3/envs/django/compiler_compat -Wl,--sysroot=/',
 'MAJOR_IN_MKDEV': 0,
 'MAJOR_IN_SYSMACROS': 0,
 'MAKESETUP': './Modules/makesetup',
 'MANDIR': '/home/<USER>/anaconda3/envs/django/share/man',
 'MKDIR_P': '/bin/mkdir -p',
 'MODLIBS': '',
 'MODNAMES': '_thread posix errno pwd _sre _codecs _weakref _functools '
             '_operator _collections itertools atexit _signal _stat time '
             '_locale _io zipimport faulthandler _tracemalloc _symtable '
             'xxsubtype',
 'MODOBJS': 'Modules/_threadmodule.o  Modules/posixmodule.o  '
            'Modules/errnomodule.o  Modules/pwdmodule.o  Modules/_sre.o  '
            'Modules/_codecsmodule.o  Modules/_weakref.o  '
            'Modules/_functoolsmodule.o  Modules/_operator.o  '
            'Modules/_collectionsmodule.o  Modules/itertoolsmodule.o  '
            'Modules/atexitmodule.o  Modules/signalmodule.o  Modules/_stat.o  '
            'Modules/timemodule.o  Modules/_localemodule.o  '
            'Modules/_iomodule.o Modules/iobase.o Modules/fileio.o '
            'Modules/bytesio.o Modules/bufferedio.o Modules/textio.o '
            'Modules/stringio.o  Modules/zipimport.o  Modules/faulthandler.o  '
            'Modules/_tracemalloc.o Modules/hashtable.o  '
            'Modules/symtablemodule.o  Modules/xxsubtype.o',
 'MODULE_OBJS': '\\',
 'MULTIARCH': 'x86_64-linux-gnu',
 'MULTIARCH_CPPFLAGS': '-DMULTIARCH=\\"x86_64-linux-gnu\\"',
 'MVWDELCH_IS_EXPRESSION': 1,
 'NO_AS_NEEDED': '-Wl,--no-as-needed',
 'OBJECT_OBJS': '\\',
 'OPT': '-DNDEBUG -g -fwrapv -O3 -Wall -Wstrict-prototypes',
 'OTHER_LIBTOOL_OPT': '',
 'PACKAGE_BUGREPORT': 0,
 'PACKAGE_NAME': 0,
 'PACKAGE_STRING': 0,
 'PACKAGE_TARNAME': 0,
 'PACKAGE_URL': 0,
 'PACKAGE_VERSION': 0,
 'PARSER_HEADERS': '\\',
 'PARSER_OBJS': '\\ Parser/myreadline.o Parser/parsetok.o Parser/tokenizer.o',
 'PGEN': 'Parser/pgen',
 'PGENOBJS': '\\ \\',
 'PGOBJS': '\\',
 'PGO_PROF_GEN_FLAG': '-fprofile-generate',
 'PGO_PROF_USE_FLAG': '-fprofile-use -fprofile-correction',
 'PLATDIR': '',
 'POBJS': '\\',
 'POSIX_SEMAPHORES_NOT_ENABLED': 0,
 'PROFILE_TASK': '-m test.regrtest --pgo',
 'PTHREAD_SYSTEM_SCHED_SUPPORTED': 1,
 'PURIFY': '',
 'PY3LIBRARY': 'libpython3.so',
 'PYLONG_BITS_IN_DIGIT': 0,
 'PYTHON': 'python',
 'PYTHONFRAMEWORK': '',
 'PYTHONFRAMEWORKDIR': 'no-framework',
 'PYTHONFRAMEWORKINSTALLDIR': '',
 'PYTHONFRAMEWORKPREFIX': '',
 'PYTHONPATH': ':',
 'PYTHON_FOR_BUILD': './python -E',
 'PYTHON_FOR_REGEN': 'python',
 'PYTHON_HEADERS': '\\',
 'PYTHON_OBJS': '\\',
 'PY_CFLAGS': '-Wsign-compare -DNDEBUG -g -fwrapv -O3 -Wall '
              '-Wstrict-prototypes',
 'PY_CFLAGS_NODIST': '-std=c99 -Wextra -Wno-unused-parameter '
                     '-Wno-missing-field-initializers',
 'PY_CORE_CFLAGS': '-Wsign-compare -DNDEBUG -g -fwrapv -O3 -Wall '
                   '-Wstrict-prototypes -std=c99 -Wextra -Wno-unused-parameter '
                   '-Wno-missing-field-initializers -I. -I./Include '
                   '-I/home/<USER>/anaconda3/envs/django/include '
                   '-fPIC -DPy_BUILD_CORE',
 'PY_CPPFLAGS': '-I. -I./Include '
                '-I/home/<USER>/anaconda3/envs/django/include',
 'PY_FORMAT_SIZE_T': '"z"',
 'PY_LDFLAGS': '-L/home/<USER>/anaconda3/envs/django/lib '
               '-Wl,-rpath=/home/<USER>/anaconda3/envs/django/lib -Wl,--no-as-needed '
               '-Wl,--sysroot=/',
 'Py_DEBUG': 0,
 'Py_ENABLE_SHARED': 0,
 'Py_HASH_ALGORITHM': 0,
 'QUICKTESTOPTS': '-x test_subprocess test_io test_lib2to3 \\',
 'RANLIB': 'ranlib',
 'READELF': 'readelf',
 'RESSRCDIR': 'Mac/Resources/framework',
 'RETSIGTYPE': 'void',
 'RUNSHARED': 'LD_LIBRARY_PATH=/home/<USER>/anaconda3/envs/django/../work/Python-3.8.18',
 'SCRIPTDIR': '/home/<USER>/anaconda3/envs/django/lib',
 'SETPGRP_HAVE_ARG': 0,
 'SGI_ABI': '',
 'SHELL': '/bin/sh',
 'SHLIBS': '-lpthread -ldl -lutil -lrt',
 'SHLIB_SUFFIX': '.so',
 'SIGNED_RIGHT_SHIFT_ZERO_FILLS': 0,
 'SITEPATH': '',
 'SIZEOF_DOUBLE': 8,
 'SIZEOF_FLOAT': 4,
 'SIZEOF_FPOS_T': 16,
 'SIZEOF_INT': 4,
 'SIZEOF_LONG': 8,
 'SIZEOF_LONG_DOUBLE': 16,
 'SIZEOF_LONG_LONG': 8,
 'SIZEOF_OFF_T': 8,
 'SIZEOF_PID_T': 4,
 'SIZEOF_PTHREAD_T': 8,
 'SIZEOF_SHORT': 2,
 'SIZEOF_SIZE_T': 8,
 'SIZEOF_TIME_T': 8,
 'SIZEOF_UINTPTR_T': 8,
 'SIZEOF_VOID_P': 8,
 'SIZEOF_WCHAR_T': 4,
 'SIZEOF__BOOL': 1,
 'SOABI': 'cpython-38-x86_64-linux-gnu',
 'SRCDIRS': 'Parser Grammar Objects Python Modules Mac Programs',
 'SRC_GDB_HOOKS': './Tools/gdb/libpython.py',
 'STDC_HEADERS': 1,
 'STRICT_SYSV_CURSES': "/* Don't use ncurses extensions */",
 'STRIPFLAG': '-s',
 'SUBDIRS': '',
 'SUBDIRSTOO': 'Include Lib Misc',
 'SYSLIBS': '-lm',
 'SYS_SELECT_WITH_SYS_TIME': 1,
 'TANH_PRESERVES_ZERO_SIGN': 1,
 'TCLTK_INCLUDES': '-I/home/<USER>/anaconda3/envs/django/include',
 'TCLTK_LIBS': '-L/home/<USER>/anaconda3/envs/django/lib '
               '-ltcl8.6 -ltk8.6',
 'TESTOPTS': '',
 'TESTPATH': '',
 'TESTPYTHON': 'LD_LIBRARY_PATH=/home/<USER>/anaconda3/envs/django/../work/Python-3.8.18 '
               './python',
 'TESTPYTHONOPTS': '',
 'TESTRUNNER': 'LD_LIBRARY_PATH=/home/<USER>/anaconda3/envs/django/../work/Python-3.8.18 '
               './python ./Tools/scripts/run_tests.py',
 'TESTTIMEOUT': 1200,
 'THREADOBJ': 'Python/thread.o',
 'TIMEMODULE_LIB': 'rt',
 'TIME_WITH_SYS_TIME': 1,
 'TM_IN_SYS_TIME': 0,
 'UNICODE_DEPS': '\\',
 'UNIVERSALSDK': '',
 'USE_COMPUTED_GOTOS': 0,
 'USE_INLINE': 1,
 'VERSION': '3.8',
 'WANT_SIGFPE_HANDLER': 0,
 'WINDOW_HAS_FLAGS': 1,
 'WITH_DOC_STRINGS': 1,
 'WITH_DTRACE': 0,
 'WITH_DYLD': 0,
 'WITH_LIBINTL': 0,
 'WITH_NEXT_FRAMEWORK': 0,
 'WITH_PYMALLOC': 1,
 'WITH_THREAD': 1,
 'WITH_VALGRIND': 0,
 'X87_DOUBLE_ROUNDING': 0,
 'XMLLIBSUBDIRS': 'xml xml/dom xml/etree xml/parsers xml/sax',
 'abs_builddir': '/home/<USER>/anaconda3/envs/django/../work/Python-3.8.18',
 'abs_srcdir': '/home/<USER>/anaconda3/envs/django/../work/Python-3.8.18',
 'datarootdir': '/home/<USER>/anaconda3/envs/django/share',
 'exec_prefix': '/home/<USER>/anaconda3/envs/django',
 'prefix': '/home/<USER>/anaconda3/envs/django',
 'srcdir': '.'}
