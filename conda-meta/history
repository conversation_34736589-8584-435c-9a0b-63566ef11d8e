==> 2023-12-14 11:08:21 <==
# cmd: /home/<USER>/anaconda3/bin/conda create -n django python=3.8
# conda version: 23.9.0
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::_libgcc_mutex-0.1-main
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::_openmp_mutex-5.1-1_gnu
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::ca-certificates-2023.08.22-h06a4308_0
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::ld_impl_linux-64-2.38-h1181459_1
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::libffi-3.4.4-h6a678d5_0
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::libgcc-ng-11.2.0-h1234567_1
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::libgomp-11.2.0-h1234567_1
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::libstdcxx-ng-11.2.0-h1234567_1
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::ncurses-6.4-h6a678d5_0
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::openssl-3.0.12-h7f8727e_0
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::pip-23.3.1-py38h06a4308_0
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::python-3.8.18-h955ad1f_0
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::readline-8.2-h5eee18b_0
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::setuptools-68.2.2-py38h06a4308_0
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::sqlite-3.41.2-h5eee18b_0
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::tk-8.6.12-h1ccaba5_0
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::wheel-0.41.2-py38h06a4308_0
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::xz-5.4.5-h5eee18b_0
+https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64::zlib-1.2.13-h5eee18b_0
# update specs: ['python=3.8']
