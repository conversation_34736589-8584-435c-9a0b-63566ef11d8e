{"build": "h1234567_1", "build_number": 1, "channel": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64", "constrains": [], "depends": ["_libgcc_mutex 0.1 main"], "extracted_package_dir": "/home/<USER>/anaconda3/pkgs/libgomp-11.2.0-h1234567_1", "files": ["lib/libgomp.so", "lib/libgomp.so.1.0.0", "share/licenses/gcc-libs/RUNTIME.LIBRARY.EXCEPTION.gomp_copy"], "fn": "libgomp-11.2.0-h1234567_1.conda", "legacy_bz2_md5": "3080c2813e6e1d0f8a100a38b5b3456d", "license": "GPL-3.0-only WITH GCC-exception-3.1", "link": {"source": "/home/<USER>/anaconda3/pkgs/libgomp-11.2.0-h1234567_1", "type": 1}, "md5": "b372c0eea9b60732fdae4b817a63c8cd", "name": "libgomp", "package_tarball_full_path": "/home/<USER>/anaconda3/pkgs/libgomp-11.2.0-h1234567_1.conda", "paths_data": {"paths": [{"_path": "lib/libgomp.so", "path_type": "softlink", "sha256": "e3b68c5f37afb7b70bd12273b69706ab33a397714e8336910f0e47f8f1cf6854", "size_in_bytes": 1265616}, {"_path": "lib/libgomp.so.1.0.0", "path_type": "hardlink", "sha256": "e3b68c5f37afb7b70bd12273b69706ab33a397714e8336910f0e47f8f1cf6854", "sha256_in_prefix": "e3b68c5f37afb7b70bd12273b69706ab33a397714e8336910f0e47f8f1cf6854", "size_in_bytes": 1265616}, {"_path": "share/licenses/gcc-libs/RUNTIME.LIBRARY.EXCEPTION.gomp_copy", "path_type": "hardlink", "sha256": "9d6b43ce4d8de0c878bf16b54d8e7a10d9bd42b75178153e3af6a815bdc90f74", "sha256_in_prefix": "9d6b43ce4d8de0c878bf16b54d8e7a10d9bd42b75178153e3af6a815bdc90f74", "size_in_bytes": 3324}], "paths_version": 1}, "requested_spec": "None", "sha256": "a1c6e599df45e116af81c36ec4c9efb1793fa3a0b854dd90dd6c8813cd476e90", "size": 485145, "subdir": "linux-64", "timestamp": 1654090775721, "url": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64/libgomp-11.2.0-h1234567_1.conda", "version": "11.2.0"}