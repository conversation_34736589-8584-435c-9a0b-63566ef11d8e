{"build": "h06a4308_0", "build_number": 0, "channel": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64", "constrains": [], "depends": [], "extracted_package_dir": "/home/<USER>/anaconda3/pkgs/ca-certificates-2023.08.22-h06a4308_0", "files": ["ssl/cacert.pem", "ssl/cert.pem"], "fn": "ca-certificates-2023.08.22-h06a4308_0.conda", "legacy_bz2_md5": "3ef00f4c5278b688552efc259c463dc8", "license": "MPL-2.0", "license_family": "Other", "link": {"source": "/home/<USER>/anaconda3/pkgs/ca-certificates-2023.08.22-h06a4308_0", "type": 1}, "md5": "243d5065a09a3e85ab888c05f5b6445a", "name": "ca-certificates", "package_tarball_full_path": "/home/<USER>/anaconda3/pkgs/ca-certificates-2023.08.22-h06a4308_0.conda", "paths_data": {"paths": [{"_path": "ssl/cacert.pem", "path_type": "hardlink", "sha256": "23c2469e2a568362a62eecf1b49ed90a15621e6fa30e29947ded3436422de9b9", "sha256_in_prefix": "23c2469e2a568362a62eecf1b49ed90a15621e6fa30e29947ded3436422de9b9", "size_in_bytes": 221470}, {"_path": "ssl/cert.pem", "path_type": "softlink", "sha256": "23c2469e2a568362a62eecf1b49ed90a15621e6fa30e29947ded3436422de9b9", "size_in_bytes": 221470}], "paths_version": 1}, "requested_spec": "None", "sha256": "1d721d9183dc6e5e6fd639827f6755aa3250d9c65d66f2108c4bc9d08208382a", "size": 125488, "subdir": "linux-64", "timestamp": 1694009767372, "url": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64/ca-certificates-2023.08.22-h06a4308_0.conda", "version": "2023.08.22"}